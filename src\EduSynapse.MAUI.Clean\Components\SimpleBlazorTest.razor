@using Microsoft.AspNetCore.Components.Web

<div style="padding: 40px; font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto;">
    <h1 style="color: #2196F3; text-align: center; margin-bottom: 30px;">
        🎓 EduSynapse - 第二阶段 Blazor 测试
    </h1>
    
    <div style="background: #E8F5E8; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
        <h2 style="color: #2E7D32; margin-top: 0;">✅ 第二阶段成功！</h2>
        <p><strong>Blazor WebView 正常工作！</strong></p>
        <p>启动时间: @startTime</p>
        <p>当前时间: @currentTime</p>
        <p>运行状态: <span style="color: green;">Blazor 组件正常运行</span></p>
    </div>
    
    <div style="background: #E3F2FD; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
        <h2 style="color: #1976D2; margin-top: 0;">🧪 Blazor 功能测试</h2>
        
        <div style="margin-bottom: 15px;">
            <button @onclick="UpdateTime" 
                    style="padding: 10px 20px; background: #2196F3; color: white; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px;">
                🔄 更新时间
            </button>
            
            <button @onclick="TestCounter" 
                    style="padding: 10px 20px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px;">
                ➕ 计数器 (@counter)
            </button>
            
            <button @onclick="TestAlert" 
                    style="padding: 10px 20px; background: #FF9800; color: white; border: none; border-radius: 4px; cursor: pointer;">
                💬 测试消息
            </button>
        </div>
    </div>
    
    <div style="background: #FFF3E0; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
        <h2 style="color: #F57C00; margin-top: 0;">📝 数据绑定测试</h2>
        <input @bind="userInput" 
               placeholder="测试 Blazor 数据绑定..." 
               style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; margin-bottom: 10px;" />
        <p><strong>绑定的内容:</strong> @userInput</p>
    </div>
    
    @if (!string.IsNullOrEmpty(message))
    {
        <div style="background: #E8F5E8; padding: 15px; border-radius: 8px; border-left: 4px solid #4CAF50;">
            <p style="margin: 0; color: #2E7D32;"><strong>消息:</strong> @message</p>
        </div>
    }
    
    <div style="background: #F5F5F5; padding: 20px; border-radius: 10px; margin-top: 20px;">
        <h3 style="color: #666; margin-top: 0;">📋 下一步计划</h3>
        <p style="font-size: 14px; color: #666;">
            ✅ 第一阶段：基础 MAUI 应用 - 完成<br/>
            ✅ 第二阶段：Blazor WebView 集成 - 正在测试<br/>
            ⏳ 第三阶段：MudBlazor UI 组件库<br/>
            ⏳ 第四阶段：HTTP 客户端和服务<br/>
            ⏳ 第五阶段：业务功能迁移
        </p>
    </div>
</div>

@code {
    private string startTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
    private string currentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
    private string userInput = "";
    private string message = "";
    private int counter = 0;

    protected override void OnInitialized()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("SimpleBlazorTest: OnInitialized 开始");
            startTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            currentTime = startTime;
            message = "Blazor 组件初始化成功！";
            System.Diagnostics.Debug.WriteLine("SimpleBlazorTest: OnInitialized 成功完成");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"SimpleBlazorTest OnInitialized 错误: {ex}");
            message = $"初始化错误: {ex.Message}";
        }
    }

    private void UpdateTime()
    {
        try
        {
            currentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            message = "时间更新成功！";
        }
        catch (Exception ex)
        {
            message = $"更新时间错误: {ex.Message}";
        }
    }

    private void TestCounter()
    {
        try
        {
            counter++;
            message = $"计数器增加到 {counter}";
        }
        catch (Exception ex)
        {
            message = $"计数器错误: {ex.Message}";
        }
    }

    private void TestAlert()
    {
        try
        {
            message = "🎉 恭喜！Blazor WebView 功能完全正常工作！";
        }
        catch (Exception ex)
        {
            message = $"测试错误: {ex.Message}";
        }
    }
}
