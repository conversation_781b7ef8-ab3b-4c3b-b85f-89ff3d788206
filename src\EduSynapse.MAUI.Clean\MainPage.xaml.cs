﻿using System.Diagnostics;

namespace EduSynapse.MAUI;

/// <summary>
/// EduSynapse 主页面 - 第一阶段：基础测试版本
/// </summary>
public partial class MainPage : ContentPage
{
    private DateTime _startTime;

    public MainPage()
    {
        try
        {
            Debug.WriteLine("🎓 EduSynapse MainPage: 开始初始化...");

            InitializeComponent();
            _startTime = DateTime.Now;

            Debug.WriteLine("🎉 EduSynapse MainPage: 初始化成功完成！");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"❌ EduSynapse MainPage 初始化失败: {ex}");
        }
    }

    private async void OnGetStartedClicked(object? sender, EventArgs e)
    {
        try
        {
            Debug.WriteLine("🎓 用户点击了开始使用按钮，导航到干净的测试页面");

            // 导航到完全干净的测试页面
            await Navigation.PushAsync(new CleanTestPage());
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"❌ 导航到测试页面失败: {ex}");
            await Display<PERSON><PERSON>t("错误", $"无法打开测试页面: {ex.Message}", "确定");
        }
    }

    private async void OnFunctionTestClicked(object? sender, EventArgs e)
    {
        try
        {
            Debug.WriteLine("🧪 用户点击了功能测试按钮，导航到功能测试中心");

            // 导航到功能测试页面
            await Navigation.PushAsync(new FunctionTestPage());
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"❌ 导航到功能测试页面失败: {ex}");
            await DisplayAlert("错误", $"无法打开功能测试页面: {ex.Message}", "确定");
        }
    }

    private async void OnLearningPlanClicked(object? sender, EventArgs e)
    {
        try
        {
            Debug.WriteLine("📚 用户点击了学习计划按钮，导航到学习计划创建器");

            // 导航到学习计划创建器页面
            await Navigation.PushAsync(new LearningPlanCreatorPage());
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"❌ 导航到学习计划创建器失败: {ex}");
            await DisplayAlert("错误", $"无法打开学习计划创建器: {ex.Message}", "确定");
        }
    }
}
