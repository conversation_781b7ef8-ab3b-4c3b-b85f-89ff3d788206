<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="EduSynapse.MAUI.PurePage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             Title="🧪 第一阶段测试">

    <ScrollView>
        <StackLayout Padding="40" Spacing="30">
            
            <!-- 标题 -->
            <Label Text="🎓 EduSynapse - 第一阶段纯净测试"
                   FontSize="28"
                   FontAttributes="Bold"
                   HorizontalOptions="Center"
                   TextColor="#2196F3" />
            
            <!-- 成功信息 -->
            <Frame BackgroundColor="#E8F5E8" Padding="20" CornerRadius="10" HasShadow="True">
                <StackLayout>
                    <Label Text="🎉 恭喜！第一阶段测试成功！" 
                           FontSize="20" 
                           FontAttributes="Bold" 
                           TextColor="#2E7D32" 
                           HorizontalOptions="Center" />
                    
                    <Label Text="✅ MAUI 基础框架正常工作"
                           FontSize="16" 
                           TextColor="#388E3C" 
                           Margin="0,10,0,0" />
                    
                    <Label Text="✅ 依赖注入容器构建成功"
                           FontSize="16" 
                           TextColor="#388E3C" />
                    
                    <Label Text="✅ 应用程序正常启动"
                           FontSize="16" 
                           TextColor="#388E3C" />
                    
                    <Label Text="✅ XAML 页面渲染正常"
                           FontSize="16" 
                           TextColor="#388E3C" />
                </StackLayout>
            </Frame>
            
            <!-- 状态信息 -->
            <Frame BackgroundColor="#F5F5F5" Padding="20" CornerRadius="10">
                <StackLayout>
                    <Label Text="📊 应用状态" 
                           FontSize="18" 
                           FontAttributes="Bold" 
                           TextColor="#333" />
                    
                    <Label x:Name="StatusLabel" 
                           Text="应用正在运行..." 
                           FontSize="14" 
                           TextColor="#666" />
                    
                    <Label x:Name="TimeLabel" 
                           Text="" 
                           FontSize="14" 
                           TextColor="#666" />
                </StackLayout>
            </Frame>
            
            <!-- 基础功能测试 -->
            <Frame BackgroundColor="#E3F2FD" Padding="20" CornerRadius="10">
                <StackLayout>
                    <Label Text="🧪 基础功能测试" 
                           FontSize="18" 
                           FontAttributes="Bold" 
                           TextColor="#1976D2" />
                    
                    <Button x:Name="TestButton"
                            Text="🔄 测试按钮点击"
                            BackgroundColor="#2196F3"
                            TextColor="White"
                            Clicked="OnTestButtonClicked"
                            Margin="0,10,0,0" />
                    
                    <Label x:Name="TestResultLabel" 
                           Text="点击上面的按钮测试基础功能" 
                           FontSize="14" 
                           TextColor="#1976D2" 
                           HorizontalOptions="Center"
                           Margin="0,10,0,0" />
                </StackLayout>
            </Frame>
            
            <!-- 下一步说明 -->
            <Frame BackgroundColor="#FFF3E0" Padding="20" CornerRadius="10">
                <StackLayout>
                    <Label Text="📋 下一步计划" 
                           FontSize="18" 
                           FontAttributes="Bold" 
                           TextColor="#F57C00" />
                    
                    <Label Text="1️⃣ 确认这个纯净版本正常工作"
                           FontSize="14" 
                           TextColor="#E65100" 
                           Margin="0,5,0,0" />
                    
                    <Label Text="2️⃣ 逐步添加 Blazor WebView"
                           FontSize="14" 
                           TextColor="#E65100" />
                    
                    <Label Text="3️⃣ 逐步添加 MudBlazor 组件"
                           FontSize="14" 
                           TextColor="#E65100" />
                    
                    <Label Text="4️⃣ 逐步添加自定义服务"
                           FontSize="14" 
                           TextColor="#E65100" />
                    
                    <Label Text="5️⃣ 逐步添加业务组件"
                           FontSize="14" 
                           TextColor="#E65100" />
                </StackLayout>
            </Frame>
            
        </StackLayout>
    </ScrollView>

</ContentPage>
