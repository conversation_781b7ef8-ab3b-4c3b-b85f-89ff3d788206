@using Microsoft.AspNetCore.Components.Web

<div style="padding: 40px; font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto;">
    <h1 style="color: #2196F3; text-align: center; margin-bottom: 30px;">
        🎓 EduSynapse - 简单 Blazor 测试
    </h1>
    
    <div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h2 style="color: #333; margin-top: 0;">✅ Blazor 状态</h2>
        <p><strong>启动时间:</strong> @startTime</p>
        <p><strong>当前时间:</strong> @currentTime</p>
        <p><strong>运行状态:</strong> <span style="color: green;">Blazor 正常运行</span></p>
        <p><strong>框架版本:</strong> .NET MAUI + Blazor (无 MudBlazor)</p>
    </div>
    
    <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h2 style="color: #1976d2; margin-top: 0;">🧪 Blazor 功能测试</h2>
        
        <div style="margin-bottom: 15px;">
            <button @onclick="UpdateTime" 
                    style="padding: 10px 20px; background: #2196F3; color: white; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px;">
                🔄 更新时间
            </button>
            
            <button @onclick="TestCounter" 
                    style="padding: 10px 20px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px;">
                ➕ 计数器 (@counter)
            </button>
        </div>
    </div>
    
    <div style="background: #fff3e0; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h2 style="color: #f57c00; margin-top: 0;">📝 Blazor 绑定测试</h2>
        <input @bind="userInput" 
               placeholder="测试 Blazor 数据绑定..." 
               style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; margin-bottom: 10px;" />
        <p><strong>绑定的内容:</strong> @userInput</p>
    </div>
    
    @if (!string.IsNullOrEmpty(message))
    {
        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; border-left: 4px solid #4CAF50;">
            <p style="margin: 0; color: #2e7d32;"><strong>消息:</strong> @message</p>
        </div>
    }
    
    <div style="background: #fafafa; padding: 20px; border-radius: 8px; margin-top: 20px; border: 1px solid #e0e0e0;">
        <h3 style="color: #666; margin-top: 0;">🔧 调试信息</h3>
        <p style="font-size: 12px; color: #666; font-family: monospace;">
            如果您看到这个页面，说明 .NET MAUI + Blazor 基本功能正常工作！<br/>
            这是一个不依赖 MudBlazor 的简单 Blazor 组件。
        </p>
    </div>
</div>

@code {
    private string startTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
    private string currentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
    private string userInput = "";
    private string message = "";
    private int counter = 0;

    protected override void OnInitialized()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("SimpleBlazorApp: OnInitialized started");
            startTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            currentTime = startTime;
            message = "Blazor 组件初始化成功！";
            System.Diagnostics.Debug.WriteLine("SimpleBlazorApp: OnInitialized completed successfully");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"SimpleBlazorApp OnInitialized error: {ex}");
            message = $"初始化错误: {ex.Message}";
        }
    }

    private void UpdateTime()
    {
        try
        {
            currentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            message = "时间更新成功！";
        }
        catch (Exception ex)
        {
            message = $"更新时间错误: {ex.Message}";
        }
    }

    private void TestCounter()
    {
        try
        {
            counter++;
            message = $"计数器增加到 {counter}";
        }
        catch (Exception ex)
        {
            message = $"计数器错误: {ex.Message}";
        }
    }
}
