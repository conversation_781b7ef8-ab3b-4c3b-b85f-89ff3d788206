# EduSynapse MAUI Clean 故障排除指南

> **项目状态**: ✅ 主要问题已解决 - 本文档记录了解决过程和预防措施

## 🎯 已解决的重大问题

### ✅ Blazor 事件绑定失效问题

**问题描述**: 所有 Blazor 事件（`@onclick`、`@onchange` 等）完全无响应

**根本原因**: MudBlazor 包依赖冲突导致 Blazor 运行时异常

**解决方案**:
1. 移除 MudBlazor 包引用
2. 清理复杂的 JavaScript 代码
3. 恢复到标准 Blazor 配置

**验证方法**:
```bash
# 运行应用
dotnet run --framework net9.0-windows10.0.19041.0

# 测试步骤：
# 1. 点击主页面的"开始使用"按钮
# 2. 进入 CleanTestPage
# 3. 测试按钮点击是否有响应
```

### ✅ 页面导航错误问题

**问题描述**: `PushAsync is not supported, please use a NavigationPage`

**根本原因**: 未正确配置 NavigationPage 架构

**解决方案**:
在 `App.xaml.cs` 中使用 NavigationPage 包装主页面：
```csharp
return new Window(new NavigationPage(new MainPage()))
{
    Title = "EduSynapse - 智能学习系统"
};
```

### ✅ 内联 JavaScript 错误问题

**问题描述**: 组件内 `<script>` 标签导致"An unhandled error has occurred"

**根本原因**: Blazor WebView 安全策略限制内联脚本

**解决方案**: 移除所有内联 JavaScript，使用纯 Blazor 实现

## 🔧 环境配置问题

### Visual Studio 2022 调试问题

#### 问题：AppxManifest 配置错误

**错误信息**：
```
Improper project configuration: no AppxManifest is specified, but WindowsPackageType is not set to MSIX.
```

**解决方案**：

1. **使用 Windows (Unpackaged) 配置**
   - 在 Visual Studio 中，点击调试目标下拉菜单
   - 选择 "Windows (Unpackaged)" 而不是 "Windows Machine"
   - 按 F5 启动调试

2. **清理和重建项目**
   ```bash
   dotnet clean
   dotnet build --framework net9.0-windows10.0.19041.0
   ```

3. **检查 MAUI 工作负载**
   ```bash
   dotnet workload install maui
   dotnet workload repair
   ```

4. **使用命令行运行**
   ```bash
   cd src/EduSynapse.MAUI.Clean
   dotnet build
   dotnet run --framework net9.0-windows10.0.19041.0
   ```

#### 4. Visual Studio 版本要求
- **Visual Studio 2022** 17.8 或更高版本
- 确保安装了 ".NET Multi-platform App UI development" 工作负载

## 🚨 常见运行时问题

### NuGet 包还原失败

**症状**: 构建时出现包引用错误

**解决方案**:
```bash
# 清理 NuGet 缓存
dotnet nuget locals all --clear

# 重新还原包
dotnet restore

# 如果仍有问题，删除 bin 和 obj 文件夹
rm -rf bin obj
dotnet restore
```

### WebView2 运行时问题

**症状**: Blazor WebView 无法加载或显示空白

**解决方案**:
1. **检查 WebView2 运行时**:
   - 通常随 Windows 11 或 Edge 浏览器自动安装
   - 手动下载：[Microsoft Edge WebView2](https://developer.microsoft.com/microsoft-edge/webview2/)

2. **验证 WebView2 版本**:
   ```powershell
   Get-AppxPackage -Name "Microsoft.WebView2"
   ```

### Blazor 组件问题

**症状**: 组件无法加载或显示错误

**诊断步骤**:
1. **检查组件命名空间**
2. **验证组件引用**
3. **查看浏览器开发者工具** (F12)

### API 服务问题

**症状**: API 调用失败或服务未注册

**解决方案**:
1. **重新启用服务注册** (在 MauiProgram.cs 中):
   ```csharp
   // 取消注释这些行
   builder.Services.AddHttpClient("EduSynapseAPI", client => { ... });
   builder.Services.AddScoped<IApiService, ApiService>();
   builder.Services.AddSingleton<IStateService, StateService>();
   ```

2. **检查后端服务**:
   ```bash
   # 确保后端服务运行在 http://localhost:8000
   curl http://localhost:8000/health
   ```

## 🔧 开发环境配置

### 推荐的调试配置

1. **开发阶段**: 使用 "Windows (Unpackaged)"
   - 更快的启动时间
   - 支持热重载
   - 更好的调试体验

2. **测试阶段**: 使用 "Windows Machine"
   - 模拟真实部署环境
   - 测试打包和安装

### 环境检查清单

**必需组件**:
- [ ] **Visual Studio 2022** (17.8+)
- [ ] **.NET 9.0 SDK** (当前版本)
- [ ] **.NET MAUI 工作负载**
- [ ] **Windows 10 SDK** (19041+)
- [ ] **WebView2 Runtime**

**验证命令**:
```bash
# 检查 .NET 版本
dotnet --version

# 检查已安装的工作负载
dotnet workload list

# 检查 MAUI 工作负载
dotnet workload list | findstr maui
```

### 性能优化建议

1. **调试模式优化**:
   - 使用 "Windows (Unpackaged)" 配置
   - 启用 Blazor 开发者工具
   - 关闭不必要的诊断工具

2. **发布模式优化**:
   - 使用 Release 配置
   - 启用代码压缩
   - 考虑 AOT 编译

## 🆘 故障排除流程

### 当遇到问题时

1. **第一步：基础检查**
   - 重启 Visual Studio
   - 清理并重建项目
   - 检查输出窗口的错误信息

2. **第二步：环境验证**
   - 运行环境检查清单
   - 验证 .NET 和 MAUI 工作负载
   - 测试标准 MAUI 模板项目

3. **第三步：深度诊断**
   - 使用项目内的测试组件
   - 检查 WebView2 开发者工具
   - 查看 Windows 事件日志

4. **第四步：重置环境**
   ```bash
   # 修复 MAUI 工作负载
   dotnet workload repair
   dotnet workload install maui

   # 清理项目
   dotnet clean
   dotnet restore
   dotnet build
   ```

### 有用的诊断命令

```bash
# 检查 .NET 信息
dotnet --info

# 检查 MAUI 工作负载
dotnet workload list

# 修复工作负载
dotnet workload repair

# 清理 NuGet 缓存
dotnet nuget locals all --clear

# 详细构建输出
dotnet build --verbosity detailed
```

## 📞 获取帮助

如果问题仍然存在：

1. **检查项目文档**: README.md 中的最新状态
2. **查看调试输出**: Visual Studio 输出窗口
3. **测试标准模板**: 创建新的 MAUI 项目验证环境
4. **社区资源**: Microsoft Learn、Stack Overflow

---

**最后更新**: 2025年7月7日
**适用版本**: EduSynapse.MAUI.Clean v1.0
**状态**: ✅ 主要问题已解决
