#!/usr/bin/env python3
"""
EduSynapse AI Backend Server Launcher
智能教学系统后端服务启动器
"""

import os
import sys
import asyncio
import uvicorn
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.config import get_settings, validate_settings
from app.utils.logger import setup_logging, get_logger


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="EduSynapse AI Backend Server",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python start_server.py                    # 使用默认配置启动
  python start_server.py --port 8080       # 指定端口
  python start_server.py --env production  # 生产环境模式
  python start_server.py --workers 4       # 指定工作进程数
        """
    )
    
    parser.add_argument(
        "--host",
        type=str,
        default=None,
        help="服务器主机地址 (默认: 0.0.0.0)"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=None,
        help="服务器端口 (默认: 8000)"
    )
    
    parser.add_argument(
        "--env",
        type=str,
        choices=["development", "production", "testing"],
        default=None,
        help="运行环境 (默认: development)"
    )
    
    parser.add_argument(
        "--workers",
        type=int,
        default=None,
        help="工作进程数 (默认: 1)"
    )
    
    parser.add_argument(
        "--reload",
        action="store_true",
        help="启用自动重载 (仅开发环境)"
    )
    
    parser.add_argument(
        "--no-reload",
        action="store_true",
        help="禁用自动重载"
    )
    
    parser.add_argument(
        "--log-level",
        type=str,
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        default=None,
        help="日志级别"
    )
    
    parser.add_argument(
        "--check-config",
        action="store_true",
        help="检查配置并退出"
    )
    
    parser.add_argument(
        "--version",
        action="version",
        version="EduSynapse AI Backend 1.0.0"
    )
    
    return parser.parse_args()


def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        sys.exit(1)
    
    print(f"✅ Python版本: {sys.version}")
    
    # 检查必需的包
    required_packages = [
        "fastapi", "uvicorn", "pydantic", "langchain", 
        "openai", "pandas", "numpy", "scikit-learn"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少必需的包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        sys.exit(1)
    
    print("✅ 所有必需的包已安装")
    
    # 检查环境变量文件
    env_file = project_root / ".env"
    if not env_file.exists():
        print("⚠️  警告: .env文件不存在")
        print("请复制.env.example为.env并配置相应的环境变量")
        
        # 询问是否继续
        response = input("是否继续启动? (y/N): ").lower()
        if response != 'y':
            sys.exit(1)
    else:
        print("✅ 环境配置文件存在")


def setup_environment(args):
    """设置环境变量"""
    if args.env:
        os.environ["ENVIRONMENT"] = args.env
    
    if args.log_level:
        os.environ["LOG_LEVEL"] = args.log_level


def validate_configuration():
    """验证配置"""
    print("🔧 验证配置...")
    
    try:
        settings = get_settings()
        validate_settings(settings)
        print("✅ 配置验证通过")
        return settings
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
        sys.exit(1)


def print_startup_info(settings, args):
    """打印启动信息"""
    print("\n" + "="*60)
    print("🎓 EduSynapse AI Backend Server")
    print("="*60)
    print(f"📍 主机地址: {args.host or settings.HOST}")
    print(f"🔌 端口: {args.port or settings.PORT}")
    print(f"🌍 环境: {os.getenv('ENVIRONMENT', 'development')}")
    print(f"🔄 自动重载: {'启用' if (args.reload or settings.RELOAD) and not args.no_reload else '禁用'}")
    print(f"👥 工作进程: {args.workers or settings.WORKERS}")
    print(f"📊 日志级别: {args.log_level or settings.LOG_LEVEL}")
    print(f"🤖 AI模型: {settings.OPENAI_MODEL}")
    print(f"💾 数据库: {settings.DATABASE_URL}")
    print("="*60)
    print("🚀 启动中...")
    print("="*60)


def create_directories(settings):
    """创建必要的目录"""
    directories = [
        settings.UPLOAD_DIR,
        "logs",
        "data"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)


async def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()
    
    # 设置环境变量
    setup_environment(args)
    
    # 检查环境
    check_environment()
    
    # 验证配置
    settings = validate_configuration()
    
    # 仅检查配置
    if args.check_config:
        print("✅ 配置检查完成，退出")
        return
    
    # 设置日志
    setup_logging()
    logger = get_logger(__name__)
    
    # 创建必要目录
    create_directories(settings)
    
    # 打印启动信息
    print_startup_info(settings, args)
    
    # 确定运行参数
    host = args.host or settings.HOST
    port = args.port or settings.PORT
    workers = args.workers or settings.WORKERS
    reload = (args.reload or settings.RELOAD) and not args.no_reload
    log_level = (args.log_level or settings.LOG_LEVEL).lower()
    
    # 启动服务器
    try:
        if workers > 1 and reload:
            logger.warning("多进程模式下禁用自动重载")
            reload = False
        
        uvicorn_config = {
            "app": "app.main:app",
            "host": host,
            "port": port,
            "log_level": log_level,
            "reload": reload,
        }
        
        if workers > 1:
            uvicorn_config["workers"] = workers
        
        logger.info(f"🚀 启动EduSynapse AI Backend服务器...")
        logger.info(f"📍 访问地址: http://{host}:{port}")
        logger.info(f"📚 API文档: http://{host}:{port}/docs")
        logger.info(f"📖 ReDoc文档: http://{host}:{port}/redoc")
        
        await uvicorn.run(**uvicorn_config)
        
    except KeyboardInterrupt:
        logger.info("👋 服务器已停止")
    except Exception as e:
        logger.error(f"❌ 服务器启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # 在Windows上设置事件循环策略
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 运行主函数
    asyncio.run(main())
