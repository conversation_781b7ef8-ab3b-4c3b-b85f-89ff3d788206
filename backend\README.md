# 🎓 EduSynapse AI Backend

> **基于LangChain和AutoGen的智能教学系统后端服务**

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com)
[![LangChain](https://img.shields.io/badge/LangChain-0.1+-purple.svg)](https://langchain.com)
[![AutoGen](https://img.shields.io/badge/AutoGen-0.2+-orange.svg)](https://github.com/microsoft/autogen)

## 🌟 核心特性

### 🤖 AI智能教学引擎
- **多AI教师协作** - 苏格拉底式、案例驱动、游戏化三种教学风格
- **WWH教学框架** - What(是什么)、Why(为什么)、How(怎么做)结构化教学
- **动态教学策略** - 根据学习进度自动调整教学方法
- **智能对话系统** - 基于LangChain的自然语言交互

### 📊 五维进度跟踪
- **知识掌握度** - 基于WWH框架的概念理解评估
- **实践完成度** - 代码质量和项目完成情况分析
- **时间投入效率** - 学习时间利用效率评估
- **错误模式分析** - 智能识别和改善学习错误模式
- **能力发展曲线** - 多维度能力发展跟踪

### 🚀 智能计划生成
- **个性化学习计划** - 基于用户偏好和能力水平
- **资源智能整合** - PDF、视频、代码等多模态资源处理
- **自适应调整** - 根据学习进度动态优化计划

## 🏗️ 技术架构

```
EduSynapse AI Backend
├── 🧠 AI Teaching Engine (LangChain + AutoGen)
│   ├── WWH Framework Engine
│   ├── Multi-Agent Teaching System
│   └── Intelligent Plan Generator
├── 📊 Progress Tracking System
│   ├── Five-Dimension Metrics Calculator
│   ├── Error Pattern Analyzer
│   └── Ability Development Tracker
├── 🌐 FastAPI REST Services
│   ├── Teaching APIs
│   ├── Progress APIs
│   └── Resource Processing APIs
└── 💾 Data Management
    ├── SQLAlchemy ORM
    ├── Redis Caching (Optional)
    └── File Storage System
```

## 🚀 快速开始

### 1. 环境要求

- **Python 3.8+**
- **OpenAI API Key** (必需)
- **8GB+ RAM** (推荐)

### 2. 安装依赖

```bash
# 克隆项目
git clone https://github.com/your-org/edusynapse-backend.git
cd edusynapse-backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
```

### 3. 配置环境

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件，填入你的OpenAI API Key
nano .env
```

**必需配置：**
```env
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_API_BASE=https://api.openai.com/v1
```

### 4. 启动服务

```bash
# 开发模式启动
python start_server.py

# 或使用uvicorn直接启动
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 5. 验证安装

访问 http://localhost:8000 查看服务状态

访问 http://localhost:8000/docs 查看API文档

## 📚 API使用指南

### 🚀 生成智能学习计划

```python
import requests

# 生成学习计划
response = requests.post("http://localhost:8000/api/v1/teaching/generate-plan", json={
    "topic": "机器学习",
    "difficulty_level": "intermediate",
    "daily_hours": 2.0,
    "duration_days": 14,
    "learning_style": "balanced",
    "focus_areas": ["theory", "practice"],
    "preferred_teacher_type": "case_driven"
})

plan = response.json()
print(f"计划ID: {plan['plan_id']}")
```

### 💬 AI教师对话

```python
# 开始教学会话
session_response = requests.post("http://localhost:8000/api/v1/teaching/start-session", json={
    "student_id": "student_123",
    "topic": "Python编程",
    "difficulty_level": "beginner"
})

session_id = session_response.json()["session_id"]

# 继续对话
chat_response = requests.post("http://localhost:8000/api/v1/teaching/continue-session", json={
    "student_id": "student_123",
    "user_input": "什么是变量？"
})

ai_reply = chat_response.json()["ai_response"]
print(f"AI教师回复: {ai_reply}")
```

### 📊 进度跟踪

```python
# 跟踪学习进度
progress_response = requests.post("http://localhost:8000/api/v1/progress/track", json={
    "student_id": "student_123",
    "learning_data": {
        "wwh_scores": {"what": 0.8, "why": 0.7, "how": 0.6},
        "concept_assessments": [...],
        "code_submissions": [...],
        "error_history": [...]
    }
})

metrics = progress_response.json()["metrics"]
print(f"综合评分: {metrics['overall_score']}")
```

## 🔧 配置说明

### 核心配置

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `OPENAI_API_KEY` | OpenAI API密钥 | 必需 |
| `OPENAI_MODEL` | 使用的AI模型 | `gpt-4` |
| `HOST` | 服务器主机 | `0.0.0.0` |
| `PORT` | 服务器端口 | `8000` |

### WWH框架权重

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `WWH_WHAT_WEIGHT` | What阶段权重 | `0.3` |
| `WWH_WHY_WEIGHT` | Why阶段权重 | `0.3` |
| `WWH_HOW_WEIGHT` | How阶段权重 | `0.4` |

### 进度跟踪配置

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `MASTERY_THRESHOLD` | 掌握度阈值 | `0.7` |
| `ERROR_PATTERN_MIN_FREQUENCY` | 错误模式最小频率 | `3` |

## 🧪 开发指南

### 项目结构

```
backend/
├── app/
│   ├── ai/                 # AI核心模块
│   │   ├── teaching_engine.py
│   │   └── progress_tracker.py
│   ├── models/             # 数据模型
│   │   ├── learning_models.py
│   │   └── api_models.py
│   ├── core/               # 核心配置
│   │   └── config.py
│   ├── utils/              # 工具函数
│   └── main.py             # 主应用
├── requirements.txt        # 依赖列表
├── start_server.py        # 启动脚本
└── README.md              # 项目文档
```

### 添加新的AI教师

1. 在 `teaching_engine.py` 中创建新的教师代理
2. 实现教师的个性化提示词
3. 在教师选择逻辑中添加新类型
4. 更新API文档

### 扩展进度跟踪维度

1. 在 `progress_tracker.py` 中添加新的计算器类
2. 更新 `FiveDimensionMetrics` 数据模型
3. 实现相应的分析算法
4. 更新API响应模型

## 🚀 部署指南

### Docker部署

```bash
# 构建镜像
docker build -t edusynapse-backend .

# 运行容器
docker run -d \
  --name edusynapse-backend \
  -p 8000:8000 \
  -e OPENAI_API_KEY=your_key \
  edusynapse-backend
```

### 生产环境部署

```bash
# 使用Gunicorn部署
pip install gunicorn

# 启动服务
gunicorn app.main:app \
  --workers 4 \
  --worker-class uvicorn.workers.UvicornWorker \
  --bind 0.0.0.0:8000
```

## 📊 监控和日志

### 健康检查

```bash
curl http://localhost:8000/health
```

### 日志配置

日志级别可通过环境变量 `LOG_LEVEL` 设置：
- `DEBUG` - 详细调试信息
- `INFO` - 一般信息 (默认)
- `WARNING` - 警告信息
- `ERROR` - 错误信息

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [LangChain](https://langchain.com) - 强大的AI应用开发框架
- [AutoGen](https://github.com/microsoft/autogen) - 多代理对话框架
- [FastAPI](https://fastapi.tiangolo.com) - 现代化的Python Web框架
- [OpenAI](https://openai.com) - 提供强大的AI模型支持

---

**🎓 EduSynapse - 让AI驱动的智能教学成为现实！**
