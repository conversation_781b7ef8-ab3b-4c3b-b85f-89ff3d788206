@* 服务端渲染测试 - 不依赖客户端 JavaScript *@
@using Microsoft.AspNetCore.Components.Web

<div style="padding: 20px; background: #f8f9fa; min-height: 100vh;">
    <h1 style="color: #dc3545;">🔧 服务端事件测试</h1>
    
    <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h2>📊 状态信息</h2>
        <p><strong>计数器:</strong> <span style="font-size: 1.8em; color: #28a745;">@count</span></p>
        <p><strong>最后点击时间:</strong> @lastClickTime</p>
        <p><strong>渲染模式:</strong> 服务端渲染</p>
        <p><strong>组件状态:</strong> @componentStatus</p>
    </div>

    <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h2>🧪 事件测试</h2>
        
        <!-- 基础点击测试 -->
        <button style="background: #007bff; color: white; border: none; padding: 15px 30px; border-radius: 5px; margin: 10px; cursor: pointer; font-size: 16px; font-weight: bold;"
                @onclick="HandleBasicClick">
            基础点击测试 (@count)
        </button>
        
        <!-- 鼠标事件测试 -->
        <button style="background: #28a745; color: white; border: none; padding: 15px 30px; border-radius: 5px; margin: 10px; cursor: pointer; font-size: 16px; font-weight: bold;"
                @onmouseenter="HandleMouseEnter"
                @onmouseleave="HandleMouseLeave"
                @onclick="HandleMouseClick">
            鼠标事件测试
        </button>
        
        <!-- 键盘事件测试 -->
        <input style="padding: 10px; border: 2px solid #007bff; border-radius: 5px; margin: 10px; font-size: 16px;"
               @onkeypress="HandleKeyPress"
               @oninput="HandleInput"
               placeholder="输入测试" />
        
        <!-- 表单事件测试 -->
        <select style="padding: 10px; border: 2px solid #007bff; border-radius: 5px; margin: 10px; font-size: 16px;"
                @onchange="HandleSelectChange">
            <option value="">选择测试</option>
            <option value="option1">选项 1</option>
            <option value="option2">选项 2</option>
            <option value="option3">选项 3</option>
        </select>
    </div>

    <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h2>📝 事件日志</h2>
        <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; max-height: 300px; overflow-y: auto; font-family: 'Courier New', monospace; font-size: 13px;">
            @if (eventLog.Count == 0)
            {
                <p style="color: #6c757d; text-align: center; margin: 20px 0;">等待事件触发...</p>
            }
            else
            {
                @foreach (var log in eventLog.TakeLast(15))
                {
                    <div style="margin: 3px 0; padding: 5px; border-radius: 3px; background: rgba(0,123,255,0.1); color: #495057;">
                        @log
                    </div>
                }
            }
        </div>
    </div>

    <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h2>🔍 诊断信息</h2>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
            <div>
                <strong>渲染次数:</strong><br/>
                <span style="color: #007bff;">@renderCount</span>
            </div>
            <div>
                <strong>事件总数:</strong><br/>
                <span style="color: #28a745;">@eventLog.Count</span>
            </div>
            <div>
                <strong>最后事件:</strong><br/>
                <span style="color: #6c757d;">@lastEventType</span>
            </div>
            <div>
                <strong>输入内容:</strong><br/>
                <span style="color: #17a2b8;">@inputValue</span>
            </div>
        </div>
    </div>
</div>

@code {
    private int count = 0;
    private int renderCount = 0;
    private string lastClickTime = "未点击";
    private string componentStatus = "初始化中...";
    private string lastEventType = "无";
    private string inputValue = "";
    private List<string> eventLog = new List<string>();

    protected override void OnInitialized()
    {
        componentStatus = "✅ 服务端组件初始化成功";
        LogEvent("🚀 ServerSideTest 组件初始化");
        System.Diagnostics.Debug.WriteLine("🎉 ServerSideTest 初始化成功");
    }

    protected override void OnAfterRender(bool firstRender)
    {
        renderCount++;
        if (firstRender)
        {
            LogEvent("🎨 首次渲染完成");
            System.Diagnostics.Debug.WriteLine("🎨 ServerSideTest 首次渲染完成");
        }
    }

    private void HandleBasicClick()
    {
        count++;
        lastClickTime = DateTime.Now.ToString("HH:mm:ss.fff");
        lastEventType = "基础点击";
        
        LogEvent($"🖱️ 基础点击 - 计数: {count}");
        System.Diagnostics.Debug.WriteLine($"✅ 基础点击成功，计数: {count}");
        
        StateHasChanged();
    }

    private void HandleMouseEnter()
    {
        lastEventType = "鼠标进入";
        LogEvent("🖱️ 鼠标进入按钮");
        System.Diagnostics.Debug.WriteLine("🖱️ 鼠标进入事件");
    }

    private void HandleMouseLeave()
    {
        lastEventType = "鼠标离开";
        LogEvent("🖱️ 鼠标离开按钮");
        System.Diagnostics.Debug.WriteLine("🖱️ 鼠标离开事件");
    }

    private void HandleMouseClick()
    {
        count++;
        lastEventType = "鼠标点击";
        LogEvent($"🖱️ 鼠标点击 - 计数: {count}");
        System.Diagnostics.Debug.WriteLine($"🖱️ 鼠标点击，计数: {count}");
        StateHasChanged();
    }

    private void HandleKeyPress(KeyboardEventArgs e)
    {
        lastEventType = $"按键: {e.Key}";
        LogEvent($"⌨️ 按键按下: {e.Key}");
        System.Diagnostics.Debug.WriteLine($"⌨️ 按键: {e.Key}");
    }

    private void HandleInput(ChangeEventArgs e)
    {
        inputValue = e.Value?.ToString() ?? "";
        lastEventType = "输入变化";
        LogEvent($"📝 输入内容: {inputValue}");
        System.Diagnostics.Debug.WriteLine($"📝 输入: {inputValue}");
    }

    private void HandleSelectChange(ChangeEventArgs e)
    {
        var selectedValue = e.Value?.ToString() ?? "";
        lastEventType = $"选择: {selectedValue}";
        LogEvent($"📋 选择变化: {selectedValue}");
        System.Diagnostics.Debug.WriteLine($"📋 选择: {selectedValue}");
    }

    private void LogEvent(string message)
    {
        var timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
        var logEntry = $"[{timestamp}] {message}";
        
        eventLog.Add(logEntry);
        
        // 保持日志数量合理
        if (eventLog.Count > 100)
        {
            eventLog.RemoveRange(0, 20);
        }
        
        System.Diagnostics.Debug.WriteLine($"📝 LOG: {logEntry}");
    }
}
