using EduSynapse.MAUI.Models;

namespace EduSynapse.MAUI.Services;

/// <summary>
/// 状态管理服务实现
/// </summary>
public class StateService : IStateService
{
    private LearningPlan? _currentPlan;
    private List<LearningPlanSummary> _learningPlans = new();
    private List<LearningProgress> _currentProgress = new();
    private bool _isApiConnected;
    private bool _isLoading;
    private string? _errorMessage;

    public LearningPlan? CurrentPlan
    {
        get => _currentPlan;
        set
        {
            if (_currentPlan != value)
            {
                _currentPlan = value;
                NotifyStateChanged();
            }
        }
    }

    public List<LearningPlanSummary> LearningPlans
    {
        get => _learningPlans;
        set
        {
            _learningPlans = value;
            NotifyStateChanged();
        }
    }

    public List<LearningProgress> CurrentProgress
    {
        get => _currentProgress;
        set
        {
            _currentProgress = value;
            NotifyStateChanged();
        }
    }

    public bool IsApiConnected
    {
        get => _isApiConnected;
        set
        {
            if (_isApiConnected != value)
            {
                _isApiConnected = value;
                NotifyStateChanged();
            }
        }
    }

    public bool IsLoading
    {
        get => _isLoading;
        set
        {
            if (_isLoading != value)
            {
                _isLoading = value;
                NotifyStateChanged();
            }
        }
    }

    public string? ErrorMessage
    {
        get => _errorMessage;
        set
        {
            if (_errorMessage != value)
            {
                _errorMessage = value;
                NotifyStateChanged();
            }
        }
    }

    public event EventHandler? StateChanged;

    public void NotifyStateChanged()
    {
        StateChanged?.Invoke(this, EventArgs.Empty);
    }

    public void ClearError()
    {
        ErrorMessage = null;
    }

    public void SetError(string message)
    {
        ErrorMessage = message;
    }

    public void Reset()
    {
        CurrentPlan = null;
        LearningPlans.Clear();
        CurrentProgress.Clear();
        IsApiConnected = false;
        IsLoading = false;
        ErrorMessage = null;
        NotifyStateChanged();
    }
}
