"""
EduSynapse AI Teaching API Routes
AI智能教学相关的API路由
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any
from datetime import datetime

try:
    from ..services.ai_teaching_service import (
        teaching_service,
        TeacherType,
        LearningStage,
    )

    AI_SERVICE_AVAILABLE = True
except Exception as e:
    print(f"⚠️  AI服务导入失败，使用备用服务: {e}")
    from ..services.ai_teaching_service_fallback import (
        teaching_service,
        TeacherType,
        LearningStage,
    )

    AI_SERVICE_AVAILABLE = False


# 请求模型
class GenerateAIPlanRequest(BaseModel):
    """AI生成学习计划请求（增强版）"""

    topic: str = Field(..., description="学习主题", min_length=1, max_length=200)
    difficulty_level: str = Field(
        "intermediate", description="难度级别: beginner, intermediate, advanced"
    )
    duration_days: int = Field(14, description="学习天数", ge=1, le=365)
    learner_profile: str = Field(
        "visual", description="学习者画像: visual, auditory, kinesthetic, reading"
    )
    time_per_day: int = Field(120, description="每天学习时间（分钟）", ge=30, le=480)
    learning_goals: List[str] = Field(default=[], description="学习目标")

    class Config:
        schema_extra = {
            "example": {
                "topic": "Python编程基础",
                "difficulty_level": "beginner",
                "duration_days": 21,
                "learner_profile": "visual",
                "time_per_day": 120,
                "learning_goals": ["掌握Python基础语法", "能够编写简单程序"],
            }
        }


class StartAISessionRequest(BaseModel):
    """开始AI教学会话请求"""

    student_id: str = Field(..., description="学生ID", min_length=1)
    topic: str = Field(..., description="学习主题", min_length=1)

    class Config:
        schema_extra = {
            "example": {"student_id": "student_001", "topic": "Python变量和数据类型"}
        }


class ContinueAISessionRequest(BaseModel):
    """继续AI教学会话请求"""

    student_id: str = Field(..., description="学生ID", min_length=1)
    user_input: str = Field(..., description="用户输入", min_length=1, max_length=2000)

    class Config:
        schema_extra = {
            "example": {"student_id": "student_001", "user_input": "什么是变量？"}
        }


# 新增：WWH框架内容生成请求模型
class WWHContentRequest(BaseModel):
    """WWH框架单阶段内容生成请求"""

    topic: str = Field(..., description="学习主题", min_length=1, max_length=200)
    difficulty: str = Field(
        default="intermediate", description="难度级别: beginner, intermediate, advanced"
    )
    learner_profile: str = Field(
        default="visual",
        description="学习者画像: visual, auditory, kinesthetic, reading",
    )
    stage: str = Field(..., description="WWH阶段: what, why, how")
    context: Optional[Dict[str, Any]] = Field(
        default=None, description="上下文信息（如前面阶段的内容）"
    )

    class Config:
        schema_extra = {
            "example": {
                "topic": "机器学习",
                "difficulty": "intermediate",
                "learner_profile": "visual",
                "stage": "what",
                "context": {},
            }
        }


class WWHFullFrameworkRequest(BaseModel):
    """WWH框架完整内容生成请求"""

    topic: str = Field(..., description="学习主题", min_length=1, max_length=200)
    difficulty: str = Field(
        default="intermediate", description="难度级别: beginner, intermediate, advanced"
    )
    learner_profile: str = Field(
        default="visual",
        description="学习者画像: visual, auditory, kinesthetic, reading",
    )
    include_metadata: bool = Field(default=True, description="是否包含元数据")

    class Config:
        schema_extra = {
            "example": {
                "topic": "机器学习",
                "difficulty": "intermediate",
                "learner_profile": "visual",
                "include_metadata": True,
            }
        }


# 响应模型
class GenerateAIPlanResponse(BaseModel):
    """AI生成学习计划响应"""

    success: bool = Field(..., description="是否成功")
    plan_id: str = Field(..., description="计划ID")
    plan: Dict[str, Any] = Field(..., description="学习计划")
    message: str = Field(..., description="响应消息")
    generation_time: Optional[float] = Field(None, description="生成耗时(秒)")


class StartAISessionResponse(BaseModel):
    """开始AI教学会话响应"""

    success: bool = Field(..., description="是否成功")
    session_id: str = Field(..., description="会话ID")
    welcome_message: str = Field(..., description="欢迎消息")
    current_teacher: str = Field(..., description="当前教师类型")
    current_stage: str = Field(..., description="当前学习阶段")


class ContinueAISessionResponse(BaseModel):
    """继续AI教学会话响应"""

    success: bool = Field(..., description="是否成功")
    ai_response: str = Field(..., description="AI教师回复")
    current_teacher: str = Field(..., description="当前教师类型")
    current_stage: str = Field(..., description="当前学习阶段")
    mastery_level: float = Field(..., description="掌握程度", ge=0.0, le=1.0)
    session_length: int = Field(..., description="会话长度")
    suggested_actions: List[str] = Field(..., description="建议操作")


# 创建路由器
router = APIRouter(prefix="/api/ai", tags=["AI Teaching"])


@router.post("/generate-plan", response_model=GenerateAIPlanResponse)
async def generate_ai_learning_plan(request: GenerateAIPlanRequest):
    """
    🚀 AI智能生成学习计划

    基于WWH教学框架和LangChain技术，为用户生成个性化的学习计划
    """
    try:
        import time

        start_time = time.time()

        print(f"Generating AI plan for topic: {request.topic}")

        # 生成学习计划
        plan = await teaching_service.generate_learning_plan(
            topic=request.topic,
            difficulty_level=request.difficulty_level,
            duration_days=request.duration_days,
        )

        generation_time = time.time() - start_time

        response = GenerateAIPlanResponse(
            success=True,
            plan_id=f"ai_plan_{request.topic}_{int(datetime.now().timestamp())}",
            plan=plan,
            message="AI学习计划生成成功！",
            generation_time=generation_time,
        )

        print(f"✅ AI Plan generated successfully for: {request.topic}")
        return response

    except Exception as e:
        print(f"❌ Failed to generate AI plan: {e}")
        raise HTTPException(status_code=500, detail=f"AI计划生成失败: {str(e)}")


@router.post("/start-session", response_model=StartAISessionResponse)
async def start_ai_teaching_session(request: StartAISessionRequest):
    """
    🎭 开始AI教师会话

    启动多AI教师协作的智能教学会话
    """
    try:
        print(f"Starting AI session for student: {request.student_id}")

        # 开始教学会话
        welcome_message = await teaching_service.start_teaching_session(
            student_id=request.student_id, topic=request.topic
        )

        response = StartAISessionResponse(
            success=True,
            session_id=f"ai_session_{request.student_id}_{request.topic}",
            welcome_message=welcome_message,
            current_teacher=TeacherType.CASE_DRIVEN.value,
            current_stage=LearningStage.WHAT.value,
        )

        print(f"✅ AI Session started for student: {request.student_id}")
        return response

    except Exception as e:
        print(f"❌ Failed to start AI session: {e}")
        raise HTTPException(status_code=500, detail=f"AI会话启动失败: {str(e)}")


@router.post("/continue-session", response_model=ContinueAISessionResponse)
async def continue_ai_teaching_session(request: ContinueAISessionRequest):
    """
    💬 继续AI教师对话

    与AI教师进行持续的智能对话学习
    """
    try:
        print(f"Continuing AI session for student: {request.student_id}")

        # 继续教学会话
        ai_response, metadata = await teaching_service.continue_teaching_session(
            student_id=request.student_id, user_input=request.user_input
        )

        response = ContinueAISessionResponse(
            success=True,
            ai_response=ai_response,
            current_teacher=metadata["teacher_type"],
            current_stage=metadata["current_stage"],
            mastery_level=metadata["mastery_level"],
            session_length=metadata["session_length"],
            suggested_actions=["继续提问", "请求解释", "查看示例"],
        )

        print(f"✅ AI Session continued for student: {request.student_id}")
        return response

    except ValueError as e:
        print(f"⚠️ AI Session not found: {e}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        print(f"❌ Failed to continue AI session: {e}")
        raise HTTPException(status_code=500, detail=f"AI对话继续失败: {str(e)}")


@router.get("/teachers")
async def get_available_ai_teachers():
    """获取可用的AI教师类型"""
    return {
        "teachers": [
            {
                "type": "socratic",
                "name": "苏格拉底式教师",
                "description": "通过连续追问引导深入思考",
                "best_for": ["概念理解", "批判性思维", "逻辑推理"],
                "ai_enabled": True,
            },
            {
                "type": "case_driven",
                "name": "案例驱动教师",
                "description": "基于真实项目场景进行教学",
                "best_for": ["实践技能", "项目经验", "应用能力"],
                "ai_enabled": True,
            },
            {
                "type": "gamified",
                "name": "游戏化教师",
                "description": "通过游戏化元素激发学习兴趣",
                "best_for": ["学习动机", "趣味学习", "成就感"],
                "ai_enabled": True,
            },
        ]
    }


@router.get("/wwh-framework")
async def get_wwh_framework_info():
    """获取WWH教学框架信息"""
    return {
        "framework": "WWH Teaching Framework",
        "description": "What-Why-How结构化教学框架",
        "version": "2.0",
        "stages": [
            {
                "stage": "what",
                "name": "是什么",
                "description": "概念定义、核心特征、基本要素",
                "weight": 0.3,
                "focus": "概念理解",
                "enhanced_features": [
                    "个性化内容生成",
                    "学习者画像适配",
                    "难度级别调整",
                    "视觉化比喻",
                    "常见误解澄清",
                ],
            },
            {
                "stage": "why",
                "name": "为什么",
                "description": "历史背景、重要性、实际价值",
                "weight": 0.3,
                "focus": "背景理解",
                "enhanced_features": [
                    "历史发展脉络",
                    "行业相关性分析",
                    "职业发展益处",
                    "成功案例展示",
                    "学习动机激发",
                ],
            },
            {
                "stage": "how",
                "name": "怎么做",
                "description": "学习方法、实践项目、代码示例",
                "weight": 0.4,
                "focus": "实践应用",
                "enhanced_features": [
                    "分步指导",
                    "实践项目设计",
                    "代码示例生成",
                    "练习题目创建",
                    "资源推荐系统",
                ],
            },
        ],
        "ai_powered": True,
        "supported_profiles": ["visual", "auditory", "kinesthetic", "reading"],
        "difficulty_levels": ["beginner", "intermediate", "advanced"],
    }


@router.post("/wwh-content")
async def generate_wwh_content(request: WWHContentRequest):
    """生成WWH框架单阶段内容（增强版）"""
    try:
        if request.stage == "what":
            content = await teaching_service.wwh_engine.generate_what_content(
                topic=request.topic,
                difficulty=request.difficulty,
                learner_profile=request.learner_profile,
                context=request.context,
            )
        elif request.stage == "why":
            # 需要what_content作为上下文
            what_content = (
                request.context.get("what_content", {}) if request.context else {}
            )
            content = await teaching_service.wwh_engine.generate_why_content(
                topic=request.topic,
                what_content=what_content,
                difficulty=request.difficulty,
                learner_profile=request.learner_profile,
                context=request.context,
            )
        elif request.stage == "how":
            # 需要what_content和why_content作为上下文
            what_content = (
                request.context.get("what_content", {}) if request.context else {}
            )
            why_content = (
                request.context.get("why_content", {}) if request.context else {}
            )
            content = await teaching_service.wwh_engine.generate_how_content(
                topic=request.topic,
                what_content=what_content,
                why_content=why_content,
                difficulty=request.difficulty,
                learner_profile=request.learner_profile,
                context=request.context,
            )
        else:
            raise HTTPException(
                status_code=400, detail="Invalid stage. Must be 'what', 'why', or 'how'"
            )

        return {
            "success": True,
            "stage": request.stage,
            "content": content,
            "metadata": {
                "topic": request.topic,
                "difficulty": request.difficulty,
                "learner_profile": request.learner_profile,
                "generated_at": datetime.now().isoformat(),
            },
        }

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Content generation failed: {str(e)}"
        )


@router.post("/wwh-full-framework")
async def generate_full_wwh_framework(request: WWHFullFrameworkRequest):
    """生成完整的WWH框架内容（增强版）"""
    try:
        # 生成What阶段内容
        what_content = await teaching_service.wwh_engine.generate_what_content(
            topic=request.topic,
            difficulty=request.difficulty,
            learner_profile=request.learner_profile,
        )

        # 生成Why阶段内容（基于What阶段）
        why_content = await teaching_service.wwh_engine.generate_why_content(
            topic=request.topic,
            what_content=what_content,
            difficulty=request.difficulty,
            learner_profile=request.learner_profile,
        )

        # 生成How阶段内容（基于What和Why阶段）
        how_content = await teaching_service.wwh_engine.generate_how_content(
            topic=request.topic,
            what_content=what_content,
            why_content=why_content,
            difficulty=request.difficulty,
            learner_profile=request.learner_profile,
        )

        # 构建完整框架
        full_framework = {
            "topic": request.topic,
            "difficulty": request.difficulty,
            "learner_profile": request.learner_profile,
            "framework": {"what": what_content, "why": why_content, "how": how_content},
        }

        if request.include_metadata:
            full_framework["metadata"] = {
                "generated_at": datetime.now().isoformat(),
                "framework_version": "2.0",
                "ai_generated": True,
                "total_estimated_time": _calculate_total_time(
                    what_content, why_content, how_content
                ),
                "learning_path": _generate_learning_path(
                    what_content, why_content, how_content
                ),
            }

        return {"success": True, "framework": full_framework}

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Full framework generation failed: {str(e)}"
        )


def _calculate_total_time(
    what_content: Dict, why_content: Dict, how_content: Dict
) -> str:
    """计算总学习时间"""
    try:
        what_time = int(what_content.get("estimated_time", "30").replace("分钟", ""))
        why_time = int(why_content.get("estimated_time", "30").replace("分钟", ""))
        how_time = int(
            how_content.get("estimated_total_time", "120").replace("分钟", "")
        )
        total_minutes = what_time + why_time + how_time

        if total_minutes >= 60:
            hours = total_minutes // 60
            minutes = total_minutes % 60
            return f"{hours}小时{minutes}分钟" if minutes > 0 else f"{hours}小时"
        else:
            return f"{total_minutes}分钟"
    except:
        return "约3-4小时"


def _generate_learning_path(
    what_content: Dict, why_content: Dict, how_content: Dict
) -> List[Dict]:
    """生成学习路径"""
    path = []

    # What阶段路径
    path.append(
        {
            "stage": "what",
            "title": "概念理解阶段",
            "objectives": what_content.get("learning_objectives", ["理解基本概念"]),
            "estimated_time": what_content.get("estimated_time", "30分钟"),
        }
    )

    # Why阶段路径
    path.append(
        {
            "stage": "why",
            "title": "价值认知阶段",
            "objectives": ["理解学习价值", "建立学习动机"],
            "estimated_time": why_content.get("estimated_time", "30分钟"),
        }
    )

    # How阶段路径
    path.append(
        {
            "stage": "how",
            "title": "实践应用阶段",
            "objectives": ["掌握实践方法", "完成练习项目"],
            "estimated_time": how_content.get("estimated_total_time", "120分钟"),
        }
    )

    return path


@router.get("/status")
async def get_ai_service_status():
    """获取AI服务状态"""
    try:
        # 检查AI服务状态
        wwh_status = getattr(teaching_service.wwh_engine, "ai_enabled", False)
        multi_agent_status = getattr(
            teaching_service.multi_agent_system, "ai_enabled", False
        )

        # 确定运行模式
        if AI_SERVICE_AVAILABLE and (wwh_status or multi_agent_status):
            service_status = "operational"
            mode = "AI模式"
        elif AI_SERVICE_AVAILABLE:
            service_status = "partial"
            mode = "混合模式"
        else:
            service_status = "fallback_mode"
            mode = "备用模式"

        return {
            "ai_service_status": service_status,
            "current_mode": mode,
            "service_available": AI_SERVICE_AVAILABLE,
            "wwh_framework_engine": {
                "status": "enabled" if wwh_status else "fallback",
                "description": "WWH教学框架内容生成引擎",
            },
            "multi_agent_system": {
                "status": "enabled" if multi_agent_status else "fallback",
                "description": "多AI教师协作系统",
            },
            "active_sessions": len(teaching_service.active_sessions),
            "features": {
                "intelligent_plan_generation": True,
                "multi_teacher_collaboration": True,
                "adaptive_teaching_strategy": True,
                "wwh_structured_learning": True,
                "fallback_mode_available": True,
            },
            "mode_info": {
                "AI模式": "完整的AI功能，包括LangChain和AutoGen",
                "混合模式": "部分AI功能可用，其余使用备用模式",
                "备用模式": "使用预设模板和规则提供基础教学服务",
            },
        }

    except Exception as e:
        return {
            "ai_service_status": "error",
            "error": str(e),
            "fallback_available": True,
        }


# 健康检查端点
@router.get("/health")
async def ai_service_health_check():
    """AI服务健康检查"""
    return {
        "status": "healthy",
        "service": "EduSynapse AI Teaching Service",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat(),
        "ai_capabilities": {
            "wwh_framework": teaching_service.wwh_engine.ai_enabled,
            "multi_agent_teaching": teaching_service.multi_agent_system.ai_enabled,
        },
    }
