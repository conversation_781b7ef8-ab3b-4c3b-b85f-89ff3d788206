using System.Text.Json.Serialization;

namespace EduSynapse.MAUI.Models;

/// <summary>
/// 学习偏好设置
/// </summary>
public class LearningPreferences
{
    public string DifficultyLevel { get; set; } = "intermediate"; // beginner, intermediate, advanced
    public double DailyHours { get; set; } = 2.0;
    public string LearningStyle { get; set; } = "balanced"; // visual, auditory, kinesthetic, balanced
    public List<string> FocusAreas { get; set; } = new(); // theory, practice, projects, etc.
    public string PreferredTeacherType { get; set; } = "case_driven"; // socratic, case_driven, gamified
}

/// <summary>
/// AI教师回复
/// </summary>
public class TeacherResponse
{
    public string Content { get; set; } = "";
    public TeacherType TeacherType { get; set; }
    public LearningStage Stage { get; set; }
    public DateTime Timestamp { get; set; }
    public string SuggestedNextAction { get; set; } = "";
    public List<string> KeyPoints { get; set; } = new();
    public string Confidence { get; set; } = "high"; // low, medium, high
}

/// <summary>
/// 聊天消息
/// </summary>
public class ChatMessage
{
    public string Role { get; set; } = ""; // user, assistant, system
    public string Content { get; set; } = "";
    public DateTime Timestamp { get; set; } = DateTime.Now;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 进度分析数据
/// </summary>
public class ProgressAnalysisData
{
    public double KnowledgeMastery { get; set; } = 0; // 0-100
    public double PracticeCompletion { get; set; } = 0; // 0-100
    public double AverageStudyTime { get; set; } = 0; // hours per day
    public string ErrorRateTrend { get; set; } = "stable"; // improving, stable, declining
    public int StudyConsistency { get; set; } = 0; // consecutive days
    public List<string> StrongAreas { get; set; } = new();
    public List<string> WeakAreas { get; set; } = new();
    public Dictionary<string, double> ConceptMastery { get; set; } = new(); // concept -> mastery %
}

/// <summary>
/// 教学策略
/// </summary>
public class TeachingStrategy
{
    public TeacherType RecommendedTeacherType { get; set; }
    public List<string> FocusAreas { get; set; } = new();
    public string DifficultyAdjustment { get; set; } = "maintain"; // increase, decrease, maintain
    public TimeSpan EstimatedTimeToImprove { get; set; }
    public string Reasoning { get; set; } = "";
    public List<string> SpecificActions { get; set; } = new();
    public double ConfidenceScore { get; set; } = 0.8; // 0-1
}

/// <summary>
/// 学习历史
/// </summary>
public class LearningHistory
{
    public string Topic { get; set; } = "";
    public int StudyDays { get; set; } = 0;
    public List<string> CompletedConcepts { get; set; } = new();
    public List<string> PracticeProjects { get; set; } = new();
    public List<string> Difficulties { get; set; } = new();
    public Dictionary<string, double> TimeSpentByArea { get; set; } = new(); // area -> hours
    public List<LearningSession> Sessions { get; set; } = new();
    public double OverallProgress { get; set; } = 0; // 0-100
}

/// <summary>
/// 学习会话
/// </summary>
public class LearningSession
{
    public DateTime Date { get; set; }
    public TimeSpan Duration { get; set; }
    public LearningStage Stage { get; set; }
    public List<string> TopicsCovered { get; set; } = new();
    public int MoodScore { get; set; } = 5; // 1-10
    public string Notes { get; set; } = "";
    public List<string> Achievements { get; set; } = new();
    public List<string> Challenges { get; set; } = new();
}

/// <summary>
/// 智能学习回顾
/// </summary>
public class LearningReview
{
    public string Summary { get; set; } = "";
    public List<string> Achievements { get; set; } = new();
    public List<string> AreasToImprove { get; set; } = new();
    public List<string> NextSteps { get; set; } = new();
    public Dictionary<string, double> SkillProgress { get; set; } = new(); // skill -> progress %
    public DateTime GeneratedAt { get; set; } = DateTime.Now;
    public string ReviewType { get; set; } = "daily"; // daily, weekly, monthly
}

/// <summary>
/// AI生成的学习内容
/// </summary>
public class AIGeneratedContent
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string Type { get; set; } = ""; // explanation, example, exercise, project
    public string Title { get; set; } = "";
    public string Content { get; set; } = "";
    public LearningStage Stage { get; set; }
    public string DifficultyLevel { get; set; } = "intermediate";
    public List<string> Tags { get; set; } = new();
    public DateTime CreatedAt { get; set; } = DateTime.Now;
    public string CreatedBy { get; set; } = ""; // AI teacher type
    public double QualityScore { get; set; } = 0.8; // 0-1
}

/// <summary>
/// 知识图谱节点
/// </summary>
public class KnowledgeNode
{
    public string Id { get; set; } = "";
    public string Name { get; set; } = "";
    public string Description { get; set; } = "";
    public string Type { get; set; } = ""; // concept, skill, tool, method
    public List<string> Prerequisites { get; set; } = new();
    public List<string> Dependencies { get; set; } = new();
    public double MasteryLevel { get; set; } = 0; // 0-100
    public DateTime LastStudied { get; set; }
    public int StudyCount { get; set; } = 0;
}

/// <summary>
/// 学习路径
/// </summary>
public class LearningPath
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string Name { get; set; } = "";
    public string Description { get; set; } = "";
    public List<KnowledgeNode> Nodes { get; set; } = new();
    public List<PathConnection> Connections { get; set; } = new();
    public string DifficultyLevel { get; set; } = "intermediate";
    public TimeSpan EstimatedDuration { get; set; }
    public bool AIGenerated { get; set; } = false;
    public DateTime CreatedAt { get; set; } = DateTime.Now;
}

/// <summary>
/// 路径连接
/// </summary>
public class PathConnection
{
    public string FromNodeId { get; set; } = "";
    public string ToNodeId { get; set; } = "";
    public string RelationType { get; set; } = ""; // prerequisite, builds_on, related_to
    public double Strength { get; set; } = 1.0; // 0-1
}

/// <summary>
/// 学习建议
/// </summary>
public class LearningRecommendation
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string Type { get; set; } = ""; // next_topic, review, practice, break
    public string Title { get; set; } = "";
    public string Description { get; set; } = "";
    public string Reasoning { get; set; } = "";
    public int Priority { get; set; } = 1; // 1-5, 5 is highest
    public TimeSpan EstimatedTime { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.Now;
    public DateTime? ExpiresAt { get; set; }
    public bool Accepted { get; set; } = false;
    public string Source { get; set; } = ""; // AI teacher type or system
}

/// <summary>
/// 错误分析
/// </summary>
public class ErrorAnalysis
{
    public string ErrorType { get; set; } = ""; // conceptual, procedural, careless
    public string Description { get; set; } = "";
    public List<string> RelatedConcepts { get; set; } = new();
    public string SuggestedRemediation { get; set; } = "";
    public int Frequency { get; set; } = 1;
    public DateTime FirstOccurrence { get; set; } = DateTime.Now;
    public DateTime LastOccurrence { get; set; } = DateTime.Now;
    public bool Resolved { get; set; } = false;
}

/// <summary>
/// 教师类型枚举
/// </summary>
public enum TeacherType
{
    Socratic,      // 苏格拉底式
    CaseDriven,    // 案例驱动型
    Gamified       // 游戏化教学
}

/// <summary>
/// 学习阶段枚举
/// </summary>
public enum LearningStage
{
    What,  // 是什么
    Why,   // 为什么
    How    // 怎么做
}

/// <summary>
/// AI对话上下文
/// </summary>
public class AIConversationContext
{
    public string SessionId { get; set; } = Guid.NewGuid().ToString();
    public string Topic { get; set; } = "";
    public TeacherType CurrentTeacher { get; set; }
    public LearningStage CurrentStage { get; set; }
    public List<ChatMessage> Messages { get; set; } = new();
    public Dictionary<string, object> UserState { get; set; } = new();
    public DateTime StartedAt { get; set; } = DateTime.Now;
    public DateTime LastActivity { get; set; } = DateTime.Now;
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// 学习成果评估
/// </summary>
public class LearningAssessment
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string Topic { get; set; } = "";
    public Dictionary<string, double> ConceptScores { get; set; } = new(); // concept -> score (0-100)
    public Dictionary<string, double> SkillScores { get; set; } = new(); // skill -> score (0-100)
    public double OverallScore { get; set; } = 0; // 0-100
    public List<string> Strengths { get; set; } = new();
    public List<string> Weaknesses { get; set; } = new();
    public List<LearningRecommendation> Recommendations { get; set; } = new();
    public DateTime AssessedAt { get; set; } = DateTime.Now;
    public string AssessmentMethod { get; set; } = ""; // ai_analysis, quiz, project, peer_review
}
