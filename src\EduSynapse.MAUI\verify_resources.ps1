# EduSynapse MAUI 资源文件验证脚本

Write-Host "🔍 验证 EduSynapse MAUI 资源文件..." -ForegroundColor Cyan

$projectRoot = $PSScriptRoot
$resourcesPath = Join-Path $projectRoot "Resources"

# 检查必需的资源文件
$requiredFiles = @(
    "AppIcon\appicon.svg",
    "AppIcon\appiconfg.svg", 
    "Splash\splash.svg",
    "Images\edusynapse_bot.svg",
    "Raw\appsettings.json",
    "Styles\Colors.xaml",
    "Styles\Styles.xaml"
)

$allFilesExist = $true

Write-Host "`n📁 检查资源文件:" -ForegroundColor Yellow

foreach ($file in $requiredFiles) {
    $fullPath = Join-Path $resourcesPath $file
    if (Test-Path $fullPath) {
        Write-Host "  ✅ $file" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $file (缺失)" -ForegroundColor Red
        $allFilesExist = $false
    }
}

# 检查项目文件
$projectFile = Join-Path $projectRoot "EduSynapse.MAUI.csproj"
if (Test-Path $projectFile) {
    Write-Host "`n📄 项目文件存在: ✅" -ForegroundColor Green
} else {
    Write-Host "`n📄 项目文件缺失: ❌" -ForegroundColor Red
    $allFilesExist = $false
}

# 检查平台文件
$platformFiles = @(
    "Platforms\Windows\app.manifest",
    "Properties\launchSettings.json"
)

Write-Host "`n🖥️ 检查平台文件:" -ForegroundColor Yellow

foreach ($file in $platformFiles) {
    $fullPath = Join-Path $projectRoot $file
    if (Test-Path $fullPath) {
        Write-Host "  ✅ $file" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $file (缺失)" -ForegroundColor Red
        $allFilesExist = $false
    }
}

# 总结
Write-Host "`n" + "="*50 -ForegroundColor Cyan
if ($allFilesExist) {
    Write-Host "🎉 所有必需的资源文件都存在!" -ForegroundColor Green
    Write-Host "✅ 项目应该可以正常构建和运行" -ForegroundColor Green
} else {
    Write-Host "⚠️ 发现缺失的资源文件" -ForegroundColor Yellow
    Write-Host "💡 建议:" -ForegroundColor Cyan
    Write-Host "  1. 运行 'dotnet build' 查看详细错误" -ForegroundColor White
    Write-Host "  2. 在 Visual Studio 中清理并重新生成项目" -ForegroundColor White
    Write-Host "  3. 检查项目文件中的资源引用" -ForegroundColor White
}

Write-Host "`n🚀 准备启动调试:" -ForegroundColor Cyan
Write-Host "  1. 在 Visual Studio 中选择 'Windows (Unpackaged)'" -ForegroundColor White
Write-Host "  2. 按 F5 开始调试" -ForegroundColor White
Write-Host "  3. 或使用命令行: dotnet run --framework net8.0-windows10.0.19041.0" -ForegroundColor White
