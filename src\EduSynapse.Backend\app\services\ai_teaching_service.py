"""
EduSynapse AI Teaching Service
基于LangChain和AutoGen的智能教学服务
"""

import asyncio
import json
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

try:
    # 尝试导入LangChain，但捕获所有可能的错误
    import langchain
    from langchain.llms import OpenAI
    from langchain.chat_models import ChatOpenAI
    from langchain.prompts import (
        ChatPromptTemplate,
        SystemMessagePromptTemplate,
        HumanMessagePromptTemplate,
    )
    from langchain.schema import BaseMessage, HumanMessage, AIMessage, SystemMessage
    from langchain.memory import ConversationBufferWindowMemory
    from langchain.chains import ConversationChain

    LANGCHAIN_AVAILABLE = True
    print("✅ LangChain已加载")
except (ImportError, TypeError, AttributeError, Exception) as e:
    LANGCHAIN_AVAILABLE = False
    print(f"⚠️  LangChain加载失败: {type(e).__name__}: {e}")
    print("📝 将使用备用模式运行")

try:
    import autogen
    from autogen import AssistantAgent, UserProxyAgent, GroupChat, GroupChatManager

    AUTOGEN_AVAILABLE = True
    print("✅ AutoGen已加载")
except (ImportError, TypeError, AttributeError, Exception) as e:
    AUTOGEN_AVAILABLE = False
    print(f"⚠️  AutoGen加载失败: {type(e).__name__}: {e}")
    print("📝 将使用备用模式运行")

# 总体AI可用性
AI_FEATURES_AVAILABLE = LANGCHAIN_AVAILABLE or AUTOGEN_AVAILABLE

if not AI_FEATURES_AVAILABLE:
    print("📝 AI功能将使用备用模式运行")

from ..core.config import settings
from ..models.learning_plan import LearningPlan


class TeacherType(str, Enum):
    """AI教师类型枚举"""

    SOCRATIC = "socratic"  # 苏格拉底式
    CASE_DRIVEN = "case_driven"  # 案例驱动
    GAMIFIED = "gamified"  # 游戏化


class LearningStage(str, Enum):
    """学习阶段枚举（WWH框架）"""

    WHAT = "what"  # 是什么
    WHY = "why"  # 为什么
    HOW = "how"  # 怎么做


@dataclass
class TeachingContext:
    """教学上下文"""

    student_id: str
    topic: str
    current_stage: LearningStage
    mastery_level: float  # 0-1
    session_history: List[Dict[str, Any]]
    error_patterns: List[str]
    time_spent: timedelta
    last_interaction: datetime


class WWHFrameworkEngine:
    """WWH教学框架引擎 - 增强版"""

    def __init__(self):
        # 初始化AI模型
        if (
            LANGCHAIN_AVAILABLE
            and hasattr(settings, "openai_api_key")
            and settings.openai_api_key
        ):
            try:
                self.llm = ChatOpenAI(
                    model_name="gpt-4",
                    temperature=0.7,
                    openai_api_key=settings.openai_api_key,
                )
                self.ai_enabled = True
                print("✅ WWH框架AI引擎初始化成功")
            except Exception as e:
                print(f"⚠️  OpenAI initialization failed: {e}")
                self.ai_enabled = False
        else:
            self.ai_enabled = False
            print("⚠️  AI features disabled. Using fallback mode.")

        # 初始化教学模板和策略
        self.teaching_strategies = {
            "beginner": {
                "what_focus": "基础概念和简单定义",
                "why_focus": "实际应用价值和基本重要性",
                "how_focus": "入门步骤和基础练习",
                "complexity": "简单",
                "examples": "生活化实例",
            },
            "intermediate": {
                "what_focus": "深入概念和关联知识",
                "why_focus": "历史发展和深层价值",
                "how_focus": "系统方法和进阶技巧",
                "complexity": "中等",
                "examples": "专业案例",
            },
            "advanced": {
                "what_focus": "高级概念和理论框架",
                "why_focus": "前沿发展和创新价值",
                "how_focus": "专家方法和创新应用",
                "complexity": "复杂",
                "examples": "前沿研究",
            },
        }

        # 学习者画像模板
        self.learner_profiles = {
            "visual": "偏好图表、图像和视觉化内容",
            "auditory": "偏好音频、讲解和讨论形式",
            "kinesthetic": "偏好动手实践和体验学习",
            "reading": "偏好文字阅读和文档学习",
        }

    async def generate_what_content(
        self,
        topic: str,
        difficulty: str = "intermediate",
        learner_profile: str = "visual",
        context: Dict[str, Any] = None,
    ) -> Dict[str, Any]:
        """生成What阶段内容 - 概念定义和特征（增强版）"""
        if not self.ai_enabled:
            return self._generate_fallback_what_content(topic, difficulty)

        try:
            # 获取教学策略
            strategy = self.teaching_strategies.get(
                difficulty, self.teaching_strategies["intermediate"]
            )
            profile = self.learner_profiles.get(
                learner_profile, self.learner_profiles["visual"]
            )

            # 构建增强的提示词
            system_prompt = f"""
你是一位专业的AI教学设计师，专门负责WWH框架中的'What'阶段内容生成。

教学策略：
- 难度级别：{difficulty}
- 关注重点：{strategy['what_focus']}
- 复杂度：{strategy['complexity']}
- 示例类型：{strategy['examples']}

学习者画像：{profile}

请生成结构化、个性化的教学内容，确保：
1. 内容深度适合难度级别
2. 表达方式符合学习者偏好
3. 逻辑清晰，层次分明
4. 包含实际应用场景
"""

            human_prompt = f"""
请为主题「{topic}」生成What阶段的完整学习内容。

要求输出JSON格式，包含以下结构：
{{
    "core_definition": "核心定义（简洁准确的一句话定义）",
    "key_features": ["关键特征1", "关键特征2", "关键特征3", "关键特征4", "关键特征5"],
    "basic_elements": ["基本要素1", "基本要素2", "基本要素3"],
    "related_concepts": ["相关概念1", "相关概念2", "相关概念3"],
    "visual_metaphor": "生动的比喻或类比",
    "real_world_examples": ["实际应用例子1", "实际应用例子2"],
    "common_misconceptions": ["常见误解1", "常见误解2"],
    "difficulty_level": "{difficulty}",
    "learner_profile": "{learner_profile}",
    "learning_objectives": ["学习目标1", "学习目标2", "学习目标3"],
    "prerequisite_knowledge": ["前置知识1", "前置知识2"],
    "estimated_time": "预估学习时间（分钟）"
}}

确保内容准确、实用、易于理解。
"""

            prompt = ChatPromptTemplate.from_messages(
                [
                    SystemMessagePromptTemplate.from_template(system_prompt),
                    HumanMessagePromptTemplate.from_template(human_prompt),
                ]
            )

            chain = ConversationChain(llm=self.llm, prompt=prompt)
            response = await chain.arun(
                topic=topic, difficulty=difficulty, learner_profile=learner_profile
            )

            # 解析AI响应
            content = json.loads(response)

            # 添加元数据
            content.update(
                {
                    "generated_at": datetime.now().isoformat(),
                    "ai_generated": True,
                    "framework_stage": "what",
                    "version": "2.0",
                }
            )

            return content

        except Exception as e:
            print(f"AI generation failed: {e}")
            return self._generate_fallback_what_content(topic, difficulty)

    async def generate_why_content(
        self,
        topic: str,
        what_content: Dict,
        difficulty: str = "intermediate",
        learner_profile: str = "visual",
        context: Dict[str, Any] = None,
    ) -> Dict[str, Any]:
        """生成Why阶段内容 - 重要性和背景（增强版）"""
        if not self.ai_enabled:
            return self._generate_fallback_why_content(topic, difficulty)

        try:
            # 获取教学策略
            strategy = self.teaching_strategies.get(
                difficulty, self.teaching_strategies["intermediate"]
            )
            profile = self.learner_profiles.get(
                learner_profile, self.learner_profiles["visual"]
            )

            # 从what_content中提取关键信息
            core_definition = what_content.get("core_definition", f"{topic}的基本概念")
            key_features = what_content.get("key_features", [])

            system_prompt = f"""
你是一位专业的AI教学设计师，专门负责WWH框架中的'Why'阶段内容生成。

教学策略：
- 难度级别：{difficulty}
- 关注重点：{strategy['why_focus']}
- 复杂度：{strategy['complexity']}
- 示例类型：{strategy['examples']}

学习者画像：{profile}

基于What阶段的内容：
- 核心定义：{core_definition}
- 关键特征：{', '.join(key_features[:3]) if key_features else '待补充'}

请生成深入的Why阶段内容，解释学习这个主题的重要性和价值。
"""

            human_prompt = f"""
请为主题「{topic}」生成Why阶段的完整学习内容。

要求输出JSON格式，包含以下结构：
{{
    "historical_background": "历史发展背景和演进过程",
    "importance": "学习此主题的重要性",
    "practical_value": "实际应用价值和意义",
    "learning_motivation": "学习动机和驱动力",
    "real_world_applications": ["现实应用场景1", "现实应用场景2", "现实应用场景3"],
    "industry_relevance": ["相关行业1", "相关行业2", "相关行业3"],
    "career_benefits": ["职业发展益处1", "职业发展益处2"],
    "problem_solving": ["能解决的问题1", "能解决的问题2", "能解决的问题3"],
    "future_trends": "未来发展趋势和前景",
    "success_stories": ["成功案例1", "成功案例2"],
    "cost_of_not_learning": "不学习此主题的潜在损失",
    "difficulty_level": "{difficulty}",
    "learner_profile": "{learner_profile}",
    "estimated_time": "预估学习时间（分钟）"
}}

确保内容具有说服力和启发性，能够激发学习者的学习兴趣。
"""

            prompt = ChatPromptTemplate.from_messages(
                [
                    SystemMessagePromptTemplate.from_template(system_prompt),
                    HumanMessagePromptTemplate.from_template(human_prompt),
                ]
            )

            chain = ConversationChain(llm=self.llm, prompt=prompt)
            response = await chain.arun(
                topic=topic, difficulty=difficulty, learner_profile=learner_profile
            )

            # 解析AI响应
            content = json.loads(response)

            # 添加元数据
            content.update(
                {
                    "generated_at": datetime.now().isoformat(),
                    "ai_generated": True,
                    "framework_stage": "why",
                    "version": "2.0",
                    "based_on_what": {
                        "core_definition": core_definition,
                        "key_features": key_features[:3] if key_features else [],
                    },
                }
            )

            return content

        except Exception as e:
            print(f"AI generation failed: {e}")
            return self._generate_fallback_why_content(topic, difficulty)

    async def generate_how_content(
        self,
        topic: str,
        what_content: Dict,
        why_content: Dict,
        difficulty: str = "intermediate",
        learner_profile: str = "visual",
        context: Dict[str, Any] = None,
    ) -> Dict[str, Any]:
        """生成How阶段内容 - 学习方法和实践（增强版）"""
        if not self.ai_enabled:
            return self._generate_fallback_how_content(topic, difficulty)

        try:
            # 获取教学策略
            strategy = self.teaching_strategies.get(
                difficulty, self.teaching_strategies["intermediate"]
            )
            profile = self.learner_profiles.get(
                learner_profile, self.learner_profiles["visual"]
            )

            # 从前面阶段提取关键信息
            core_definition = what_content.get("core_definition", f"{topic}的基本概念")
            learning_objectives = what_content.get("learning_objectives", [])
            practical_value = why_content.get(
                "practical_value", f"学习{topic}的实际价值"
            )
            real_world_applications = why_content.get("real_world_applications", [])

            system_prompt = f"""
你是一位专业的AI教学设计师，专门负责WWH框架中的'How'阶段内容生成。

教学策略：
- 难度级别：{difficulty}
- 关注重点：{strategy['how_focus']}
- 复杂度：{strategy['complexity']}
- 示例类型：{strategy['examples']}

学习者画像：{profile}

基于前面阶段的内容：
- 核心定义：{core_definition}
- 学习目标：{', '.join(learning_objectives[:3]) if learning_objectives else '待补充'}
- 实际价值：{practical_value}
- 应用场景：{', '.join(real_world_applications[:2]) if real_world_applications else '待补充'}

请生成系统化、实用的How阶段内容，确保学习者能够掌握具体的学习和应用方法。
"""

            human_prompt = f"""
请为主题「{topic}」生成How阶段的完整学习内容。

要求输出JSON格式，包含以下结构：
{{
    "learning_methods": ["学习方法1", "学习方法2", "学习方法3", "学习方法4"],
    "step_by_step_guide": [
        {{"step": 1, "title": "步骤标题", "description": "详细描述", "duration": "预估时间"}},
        {{"step": 2, "title": "步骤标题", "description": "详细描述", "duration": "预估时间"}},
        {{"step": 3, "title": "步骤标题", "description": "详细描述", "duration": "预估时间"}}
    ],
    "practice_projects": [
        {{"name": "项目名称", "description": "项目描述", "difficulty": "难度", "estimated_time": "预估时间"}},
        {{"name": "项目名称", "description": "项目描述", "difficulty": "难度", "estimated_time": "预估时间"}}
    ],
    "code_examples": [
        {{"title": "示例标题", "code": "代码内容", "explanation": "代码解释"}},
        {{"title": "示例标题", "code": "代码内容", "explanation": "代码解释"}}
    ],
    "exercises": [
        {{"type": "练习类型", "question": "练习题目", "hint": "提示", "difficulty": "难度"}},
        {{"type": "练习类型", "question": "练习题目", "hint": "提示", "difficulty": "难度"}}
    ],
    "recommended_resources": [
        {{"type": "资源类型", "title": "资源标题", "url": "链接（如有）", "description": "资源描述"}},
        {{"type": "资源类型", "title": "资源标题", "url": "链接（如有）", "description": "资源描述"}}
    ],
    "common_pitfalls": ["常见陷阱1", "常见陷阱2", "常见陷阱3"],
    "success_metrics": ["成功指标1", "成功指标2", "成功指标3"],
    "next_steps": ["下一步建议1", "下一步建议2"],
    "difficulty_level": "{difficulty}",
    "learner_profile": "{learner_profile}",
    "estimated_total_time": "总预估学习时间"
}}

确保内容实用、可操作，能够指导学习者从入门到精通。
"""

            prompt = ChatPromptTemplate.from_messages(
                [
                    SystemMessagePromptTemplate.from_template(system_prompt),
                    HumanMessagePromptTemplate.from_template(human_prompt),
                ]
            )

            chain = ConversationChain(llm=self.llm, prompt=prompt)
            response = await chain.arun(
                topic=topic, difficulty=difficulty, learner_profile=learner_profile
            )

            # 解析AI响应
            content = json.loads(response)

            # 添加元数据
            content.update(
                {
                    "generated_at": datetime.now().isoformat(),
                    "ai_generated": True,
                    "framework_stage": "how",
                    "version": "2.0",
                    "based_on_previous_stages": {
                        "what_definition": core_definition,
                        "why_value": practical_value,
                        "learning_objectives": (
                            learning_objectives[:3] if learning_objectives else []
                        ),
                    },
                }
            )

            return content

        except Exception as e:
            print(f"AI generation failed: {e}")
            return self._generate_fallback_how_content(topic, difficulty)

    def _generate_fallback_what_content(self, topic: str) -> Dict[str, Any]:
        """What阶段内容生成失败时的备用方案"""
        return {
            "core_definition": f"{topic}的基本定义",
            "key_features": [f"{topic}的特征1", f"{topic}的特征2", f"{topic}的特征3"],
            "basic_elements": [f"{topic}的要素1", f"{topic}的要素2"],
            "related_concepts": [f"相关概念1", f"相关概念2"],
        }

    def _generate_fallback_why_content(self, topic: str) -> Dict[str, Any]:
        """Why阶段内容生成失败时的备用方案"""
        return {
            "historical_background": f"{topic}的发展历史",
            "importance": f"学习{topic}的重要性",
            "practical_value": f"{topic}的实际应用价值",
            "learning_motivation": f"学习{topic}的动机",
        }

    def _generate_fallback_how_content(self, topic: str) -> Dict[str, Any]:
        """How阶段内容生成失败时的备用方案"""
        return {
            "learning_methods": [f"{topic}的学习方法1", f"{topic}的学习方法2"],
            "practice_projects": [f"{topic}实践项目1", f"{topic}实践项目2"],
            "code_examples": [f"# {topic}代码示例\nprint('Hello, {topic}!')"],
            "exercises": [f"{topic}练习题1", f"{topic}练习题2"],
            "recommended_resources": [f"{topic}推荐资源1", f"{topic}推荐资源2"],
        }


class MultiAgentTeachingSystem:
    """多代理教学系统 - 基于AutoGen"""

    def __init__(self):
        if (
            AUTOGEN_AVAILABLE
            and hasattr(settings, "openai_api_key")
            and settings.openai_api_key
        ):
            self.config_list = [
                {
                    "model": "gpt-4",
                    "api_key": settings.openai_api_key,
                }
            ]
            self.ai_enabled = True
            self._initialize_teachers()
        else:
            self.ai_enabled = False
            print("⚠️  Multi-agent system disabled. Using fallback mode.")

    def _initialize_teachers(self):
        """初始化AI教师"""
        if not self.ai_enabled:
            return

        try:
            # 初始化不同类型的AI教师
            self.socratic_teacher = self._create_socratic_teacher()
            self.case_driven_teacher = self._create_case_driven_teacher()
            self.gamified_teacher = self._create_gamified_teacher()
            self.coordinator = self._create_coordinator()
        except Exception as e:
            print(f"Teacher initialization failed: {e}")
            self.ai_enabled = False

    def _create_socratic_teacher(self) -> Optional[Any]:
        """创建苏格拉底式教师代理"""
        if not AUTOGEN_AVAILABLE:
            return None

        return AssistantAgent(
            name="SocraticTeacher",
            system_message="""
            你是一位苏格拉底式教师，擅长通过连续追问引导学生深入思考。
            你的教学特点：
            1. 不直接给出答案，而是通过问题引导学生自己发现真理
            2. 善于发现学生思维中的矛盾和盲点
            3. 循序渐进地深化问题的复杂度
            4. 鼓励学生质疑和批判性思考
            """,
            llm_config={"config_list": self.config_list},
        )

    def _create_case_driven_teacher(self) -> Optional[Any]:
        """创建案例驱动教师代理"""
        if not AUTOGEN_AVAILABLE:
            return None

        return AssistantAgent(
            name="CaseDrivenTeacher",
            system_message="""
            你是一位案例驱动教师，擅长通过真实项目场景进行教学。
            你的教学特点：
            1. 总是从实际应用场景出发
            2. 提供丰富的代码示例和项目案例
            3. 强调理论与实践的结合
            4. 关注解决实际问题的能力
            """,
            llm_config={"config_list": self.config_list},
        )

    def _create_gamified_teacher(self) -> Optional[Any]:
        """创建游戏化教师代理"""
        if not AUTOGEN_AVAILABLE:
            return None

        return AssistantAgent(
            name="GamifiedTeacher",
            system_message="""
            你是一位游戏化教师，擅长通过游戏化元素激发学习兴趣。
            你的教学特点：
            1. 将学习过程游戏化，设置挑战和奖励
            2. 使用积分、等级、成就等激励机制
            3. 创造有趣的学习情境和角色扮演
            4. 鼓励竞争和协作
            """,
            llm_config={"config_list": self.config_list},
        )

    def _create_coordinator(self) -> Optional[Any]:
        """创建教师协调器"""
        if not AUTOGEN_AVAILABLE:
            return None

        return AssistantAgent(
            name="TeacherCoordinator",
            system_message="""
            你是教师协调器，负责根据学生的学习状态选择最适合的教学策略。
            """,
            llm_config={"config_list": self.config_list},
        )

    async def conduct_teaching_session(
        self, context: TeachingContext, user_input: str
    ) -> Tuple[str, TeacherType]:
        """进行教学会话"""

        if not self.ai_enabled:
            return (
                self._fallback_response(user_input, context.topic),
                TeacherType.CASE_DRIVEN,
            )

        # 选择合适的教师
        selected_teacher_type = await self._select_teacher(context)

        try:
            # 这里应该实现真正的AutoGen对话
            # 暂时返回模拟响应
            response = f"作为{selected_teacher_type.value}教师，我来回答你关于{context.topic}的问题：{user_input}"
            return response, selected_teacher_type

        except Exception as e:
            print(f"Teaching session failed: {e}")
            return (
                self._fallback_response(user_input, context.topic),
                selected_teacher_type,
            )

    async def _select_teacher(self, context: TeachingContext) -> TeacherType:
        """根据教学上下文选择合适的教师"""

        # 基于学习阶段的基础选择
        if context.current_stage == LearningStage.WHAT:
            if context.mastery_level < 0.3:
                return TeacherType.SOCRATIC  # 概念理解困难
            else:
                return TeacherType.CASE_DRIVEN  # 需要具体例子

        elif context.current_stage == LearningStage.WHY:
            if len(context.error_patterns) > 3:
                return TeacherType.SOCRATIC  # 需要深入思考
            else:
                return TeacherType.GAMIFIED  # 激发学习动机

        elif context.current_stage == LearningStage.HOW:
            return TeacherType.CASE_DRIVEN  # 实践导向

        return TeacherType.CASE_DRIVEN  # 默认选择

    def _fallback_response(self, user_input: str, topic: str) -> str:
        """AI不可用时的备用响应"""
        return f"关于{topic}的问题：{user_input}，这是一个很好的问题。让我为你提供一些基础信息..."


class IntelligentTeachingService:
    """智能教学服务 - 整合WWH框架和多代理系统"""

    def __init__(self):
        self.wwh_engine = WWHFrameworkEngine()
        self.multi_agent_system = MultiAgentTeachingSystem()
        self.active_sessions: Dict[str, TeachingContext] = {}

    async def generate_learning_plan(
        self,
        topic: str,
        difficulty_level: str = "intermediate",
        duration_days: int = 14,
        learner_profile: str = "visual",
        learning_goals: List[str] = None,
        time_per_day: int = 120,  # 每天学习时间（分钟）
    ) -> Dict[str, Any]:
        """生成智能学习计划（增强版）"""

        print(
            f"🎯 生成学习计划: {topic} (难度: {difficulty_level}, 学习者: {learner_profile})"
        )

        try:
            # 使用增强的WWH框架生成内容
            what_content = await self.wwh_engine.generate_what_content(
                topic=topic,
                difficulty=difficulty_level,
                learner_profile=learner_profile,
            )

            why_content = await self.wwh_engine.generate_why_content(
                topic=topic,
                what_content=what_content,
                difficulty=difficulty_level,
                learner_profile=learner_profile,
            )

            how_content = await self.wwh_engine.generate_how_content(
                topic=topic,
                what_content=what_content,
                why_content=why_content,
                difficulty=difficulty_level,
                learner_profile=learner_profile,
            )

            # 生成智能每日计划
            daily_plans = await self._generate_intelligent_daily_plans(
                duration_days=duration_days,
                what_content=what_content,
                why_content=why_content,
                how_content=how_content,
                time_per_day=time_per_day,
                difficulty_level=difficulty_level,
                learner_profile=learner_profile,
            )

            # 生成学习里程碑
            milestones = self._generate_learning_milestones(
                duration_days=duration_days,
                what_content=what_content,
                why_content=why_content,
                how_content=how_content,
            )

            # 构建增强的学习计划
            plan = {
                "topic": topic,
                "description": f"基于WWH框架的{topic}个性化学习计划",
                "difficulty_level": difficulty_level,
                "learner_profile": learner_profile,
                "duration_days": duration_days,
                "time_per_day_minutes": time_per_day,
                "learning_goals": learning_goals
                or what_content.get("learning_objectives", []),
                # WWH框架内容
                "wwh_framework": {
                    "what": what_content,
                    "why": why_content,
                    "how": how_content,
                },
                # 智能每日计划
                "daily_plans": daily_plans,
                # 学习里程碑
                "milestones": milestones,
                # 学习路径
                "learning_path": self._generate_detailed_learning_path(
                    what_content, why_content, how_content, duration_days
                ),
                # 评估和反馈
                "assessment_strategy": self._generate_assessment_strategy(
                    difficulty_level, learner_profile, how_content
                ),
                # 元数据
                "metadata": {
                    "ai_generated": True,
                    "framework_version": "2.0",
                    "created_at": datetime.now().isoformat(),
                    "estimated_total_hours": (duration_days * time_per_day) / 60,
                    "completion_rate_target": 0.85,
                    "personalization_level": "high",
                },
            }

            print(f"✅ 成功生成学习计划: {topic}")
            return plan

        except Exception as e:
            print(f"❌ 学习计划生成失败: {e}")
            raise

    async def start_teaching_session(self, student_id: str, topic: str) -> str:
        """开始教学会话"""

        context = TeachingContext(
            student_id=student_id,
            topic=topic,
            current_stage=LearningStage.WHAT,
            mastery_level=0.0,
            session_history=[],
            error_patterns=[],
            time_spent=timedelta(),
            last_interaction=datetime.now(),
        )

        self.active_sessions[student_id] = context

        # 生成开场白
        welcome_message = f"""
        🎓 欢迎来到EduSynapse智能教学系统！
        
        我们将一起学习「{topic}」这个主题。
        我会根据你的学习进度，动态调整教学策略，确保最佳的学习效果。
        
        让我们从「What - 是什么」阶段开始吧！
        你对{topic}有什么初步的了解吗？
        """

        return welcome_message.strip()

    async def continue_teaching_session(
        self, student_id: str, user_input: str
    ) -> Tuple[str, Dict[str, Any]]:
        """继续教学会话"""

        if student_id not in self.active_sessions:
            raise ValueError(f"No active session found for student: {student_id}")

        context = self.active_sessions[student_id]

        # 更新上下文
        context.last_interaction = datetime.now()
        context.session_history.append(
            {
                "timestamp": datetime.now().isoformat(),
                "user_input": user_input,
                "stage": context.current_stage.value,
            }
        )

        # 进行教学对话
        response, teacher_type = await self.multi_agent_system.conduct_teaching_session(
            context, user_input
        )

        # 更新会话历史
        context.session_history.append(
            {
                "timestamp": datetime.now().isoformat(),
                "ai_response": response,
                "teacher_type": teacher_type.value,
                "stage": context.current_stage.value,
            }
        )

        # 返回响应和元数据
        metadata = {
            "teacher_type": teacher_type.value,
            "current_stage": context.current_stage.value,
            "mastery_level": context.mastery_level,
            "session_length": len(context.session_history),
        }

        return response, metadata

    def _generate_daily_plans(self, duration_days: int) -> List[Dict[str, Any]]:
        """生成每日学习计划"""
        daily_plans = []

        # 简化版：平均分配WWH阶段
        what_days = duration_days // 3
        why_days = duration_days // 3
        how_days = duration_days - what_days - why_days

        current_day = 1

        # What阶段
        for day in range(what_days):
            daily_plans.append(
                {
                    "day": current_day,
                    "stage": "what",
                    "focus": "概念理解",
                    "estimated_hours": 2.0,
                }
            )
            current_day += 1

        # Why阶段
        for day in range(why_days):
            daily_plans.append(
                {
                    "day": current_day,
                    "stage": "why",
                    "focus": "背景理解",
                    "estimated_hours": 2.0,
                }
            )
            current_day += 1

        # How阶段
        for day in range(how_days):
            daily_plans.append(
                {
                    "day": current_day,
                    "stage": "how",
                    "focus": "实践应用",
                    "estimated_hours": 2.0,
                }
            )
            current_day += 1

        return daily_plans

    async def _generate_intelligent_daily_plans(
        self,
        duration_days: int,
        what_content: Dict[str, Any],
        why_content: Dict[str, Any],
        how_content: Dict[str, Any],
        time_per_day: int = 120,  # 每天学习时间（分钟）
        difficulty_level: str = "intermediate",
        learner_profile: str = "visual",
    ) -> List[Dict[str, Any]]:
        """生成智能每日学习计划（增强版）"""
        daily_plans = []

        # 从WWH内容中提取关键信息
        learning_objectives = what_content.get("learning_objectives", [])
        step_by_step_guide = how_content.get("step_by_step_guide", [])
        practice_projects = how_content.get("practice_projects", [])
        exercises = how_content.get("exercises", [])

        # 计算阶段分配
        # 根据难度调整阶段分配比例
        if difficulty_level == "beginner":
            what_ratio, why_ratio, how_ratio = 0.3, 0.2, 0.5  # 初学者需要更多概念和实践
        elif difficulty_level == "advanced":
            what_ratio, why_ratio, how_ratio = 0.2, 0.2, 0.6  # 高级学习者需要更多实践
        else:  # intermediate
            what_ratio, why_ratio, how_ratio = 0.25, 0.25, 0.5  # 平衡分配

        what_days = max(1, int(duration_days * what_ratio))
        why_days = max(1, int(duration_days * why_ratio))
        how_days = duration_days - what_days - why_days

        current_day = 1

        # What阶段 - 概念理解
        what_topics = [
            "核心概念和定义",
            "关键特征和属性",
            "基本要素和组成",
            "相关概念和关系",
            "常见误解澄清",
        ]

        for day in range(what_days):
            # 为每天分配不同的学习重点
            focus_index = day % len(what_topics)
            focus = what_topics[focus_index]

            # 根据学习者画像调整活动
            if learner_profile == "visual":
                activities = ["概念图绘制", "视频学习", "图表分析"]
            elif learner_profile == "auditory":
                activities = ["概念讲解", "有声阅读", "讨论交流"]
            elif learner_profile == "kinesthetic":
                activities = ["实例操作", "概念演示", "互动练习"]
            else:  # reading
                activities = ["文档阅读", "概念笔记", "术语整理"]

            daily_plans.append(
                {
                    "day": current_day,
                    "stage": "what",
                    "focus": focus,
                    "description": f"理解{focus}，建立基础认知",
                    "learning_objectives": (
                        learning_objectives[focus_index % len(learning_objectives)]
                        if learning_objectives
                        else f"掌握{focus}"
                    ),
                    "activities": activities,
                    "estimated_minutes": time_per_day,
                    "resources": [{"type": "concept", "title": f"{focus}学习资料"}],
                    "completion_criteria": f"能够清晰解释{focus}",
                }
            )
            current_day += 1

        # Why阶段 - 价值理解
        why_topics = [
            "历史背景和发展",
            "重要性和意义",
            "实际应用价值",
            "行业相关性",
            "职业发展益处",
        ]

        for day in range(why_days):
            focus_index = day % len(why_topics)
            focus = why_topics[focus_index]

            # 根据学习者画像调整活动
            if learner_profile == "visual":
                activities = ["案例分析", "发展历程图", "价值可视化"]
            elif learner_profile == "auditory":
                activities = ["专家讲解", "价值讨论", "应用案例分享"]
            elif learner_profile == "kinesthetic":
                activities = ["场景模拟", "价值体验", "应用尝试"]
            else:  # reading
                activities = ["背景阅读", "案例研究", "价值分析"]

            daily_plans.append(
                {
                    "day": current_day,
                    "stage": "why",
                    "focus": focus,
                    "description": f"理解{focus}，建立学习动机",
                    "activities": activities,
                    "estimated_minutes": time_per_day,
                    "resources": [{"type": "background", "title": f"{focus}相关资料"}],
                    "completion_criteria": f"能够解释{focus}并产生学习兴趣",
                }
            )
            current_day += 1

        # How阶段 - 实践应用
        # 使用step_by_step_guide和practice_projects来生成更具体的计划

        # 首先添加基于step_by_step_guide的日计划
        steps_per_day = max(1, len(step_by_step_guide) // (how_days // 2 or 1))
        step_days = min(
            how_days - 1,
            len(step_by_step_guide) // steps_per_day
            + (1 if len(step_by_step_guide) % steps_per_day > 0 else 0),
        )

        for day in range(step_days):
            start_idx = day * steps_per_day
            end_idx = min(start_idx + steps_per_day, len(step_by_step_guide))
            day_steps = (
                step_by_step_guide[start_idx:end_idx] if step_by_step_guide else []
            )

            step_titles = [
                step.get("title", f"步骤{step.get('step', i+1)}")
                for i, step in enumerate(day_steps)
            ]

            # 根据学习者画像调整活动
            if learner_profile == "visual":
                activities = ["步骤演示", "流程图学习", "可视化实践"]
            elif learner_profile == "auditory":
                activities = ["步骤讲解", "方法讨论", "语音指导"]
            elif learner_profile == "kinesthetic":
                activities = ["动手实践", "跟随操作", "实际应用"]
            else:  # reading
                activities = ["步骤文档", "方法笔记", "实践指南"]

            daily_plans.append(
                {
                    "day": current_day,
                    "stage": "how",
                    "focus": "方法学习",
                    "description": f"学习实践方法: {', '.join(step_titles)}",
                    "steps": day_steps,
                    "activities": activities,
                    "estimated_minutes": time_per_day,
                    "resources": [
                        {
                            "type": "tutorial",
                            "title": f"实践指南: {step_titles[0] if step_titles else '基础方法'}",
                        }
                    ],
                    "completion_criteria": "完成所有步骤并理解方法",
                }
            )
            current_day += 1

        # 然后添加基于practice_projects的日计划
        remaining_days = how_days - step_days
        projects_per_day = max(1, len(practice_projects) // (remaining_days or 1))

        for day in range(remaining_days):
            start_idx = day * projects_per_day
            end_idx = min(start_idx + projects_per_day, len(practice_projects))
            day_projects = (
                practice_projects[start_idx:end_idx] if practice_projects else []
            )

            project_names = [
                project.get("name", f"项目{i+1}")
                for i, project in enumerate(day_projects)
            ]

            # 如果没有项目，使用练习题
            if not day_projects and exercises:
                exercise_count = min(3, len(exercises))
                day_exercises = exercises[:exercise_count]
                exercise_types = [ex.get("type", "练习") for ex in day_exercises]

                daily_plans.append(
                    {
                        "day": current_day,
                        "stage": "how",
                        "focus": "练习巩固",
                        "description": f"通过练习巩固所学: {', '.join(exercise_types)}",
                        "exercises": day_exercises,
                        "estimated_minutes": time_per_day,
                        "resources": [{"type": "exercise", "title": "练习题集"}],
                        "completion_criteria": "完成所有练习并掌握技能",
                    }
                )
            else:
                daily_plans.append(
                    {
                        "day": current_day,
                        "stage": "how",
                        "focus": "项目实践",
                        "description": f"实践项目: {', '.join(project_names)}",
                        "projects": day_projects,
                        "estimated_minutes": time_per_day,
                        "resources": [
                            {
                                "type": "project",
                                "title": f"项目指南: {project_names[0] if project_names else '实践项目'}",
                            }
                        ],
                        "completion_criteria": "完成项目并应用所学知识",
                    }
                )

            current_day += 1

        # 如果还有剩余天数，添加复习和总结
        if current_day <= duration_days:
            daily_plans.append(
                {
                    "day": current_day,
                    "stage": "review",
                    "focus": "复习与总结",
                    "description": "复习所学内容，总结知识点，巩固技能",
                    "activities": ["知识回顾", "问题解答", "技能评估"],
                    "estimated_minutes": time_per_day,
                    "resources": [{"type": "summary", "title": "学习总结"}],
                    "completion_criteria": "能够综合应用所学知识解决问题",
                }
            )

        return daily_plans

    def _generate_learning_milestones(
        self,
        duration_days: int,
        what_content: Dict[str, Any],
        why_content: Dict[str, Any],
        how_content: Dict[str, Any],
    ) -> List[Dict[str, Any]]:
        """生成学习里程碑"""

        # 从内容中提取关键信息
        learning_objectives = what_content.get("learning_objectives", [])
        step_by_step_guide = how_content.get("step_by_step_guide", [])
        practice_projects = how_content.get("practice_projects", [])

        # 计算里程碑数量（通常是3-5个）
        milestone_count = min(5, max(3, duration_days // 5))

        milestones = []

        # 第一个里程碑：概念理解
        milestones.append(
            {
                "id": 1,
                "title": "概念掌握",
                "description": "理解核心概念和基本原理",
                "target_day": max(1, duration_days // milestone_count),
                "completion_criteria": [
                    "能够准确解释核心定义",
                    "理解关键特征和基本要素",
                    "识别相关概念和关系",
                ],
                "assessment_method": "概念测验",
                "stage": "what",
            }
        )

        # 第二个里程碑：价值认知
        milestones.append(
            {
                "id": 2,
                "title": "价值认知",
                "description": "理解学习价值和应用场景",
                "target_day": max(2, 2 * duration_days // milestone_count),
                "completion_criteria": [
                    "理解学习主题的重要性",
                    "能够解释实际应用价值",
                    "识别行业相关性和职业益处",
                ],
                "assessment_method": "案例分析",
                "stage": "why",
            }
        )

        # 中间里程碑：方法掌握
        if milestone_count >= 4:
            milestones.append(
                {
                    "id": 3,
                    "title": "方法掌握",
                    "description": "掌握基本方法和技术",
                    "target_day": max(3, 3 * duration_days // milestone_count),
                    "completion_criteria": [
                        "理解基本步骤和方法",
                        "能够按照指导完成简单任务",
                        "掌握基础技术和工具",
                    ],
                    "assessment_method": "方法演示",
                    "stage": "how",
                }
            )

        # 倒数第二个里程碑：实践应用
        milestones.append(
            {
                "id": milestone_count - 1,
                "title": "实践应用",
                "description": "应用所学知识解决实际问题",
                "target_day": max(
                    4, (milestone_count - 1) * duration_days // milestone_count
                ),
                "completion_criteria": ["完成实践项目", "解决实际问题", "应用所学技能"],
                "assessment_method": "项目评估",
                "stage": "how",
            }
        )

        # 最后一个里程碑：综合掌握
        milestones.append(
            {
                "id": milestone_count,
                "title": "综合掌握",
                "description": "综合应用所学知识和技能",
                "target_day": duration_days,
                "completion_criteria": [
                    "能够综合应用所有概念和方法",
                    "独立完成复杂任务",
                    "解决实际问题并评估结果",
                ],
                "assessment_method": "综合评估",
                "stage": "review",
            }
        )

        return milestones

    def _generate_detailed_learning_path(
        self,
        what_content: Dict[str, Any],
        why_content: Dict[str, Any],
        how_content: Dict[str, Any],
        duration_days: int,
    ) -> List[Dict[str, Any]]:
        """生成详细学习路径"""

        # 从内容中提取关键信息
        learning_objectives = what_content.get("learning_objectives", [])
        step_by_step_guide = how_content.get("step_by_step_guide", [])

        # 构建学习路径
        path = []

        # 第一阶段：概念理解
        path.append(
            {
                "phase": 1,
                "title": "概念理解阶段",
                "description": "掌握核心概念和基本原理",
                "duration_percentage": 25,
                "target_days": max(1, int(duration_days * 0.25)),
                "key_activities": [
                    "学习核心定义和特征",
                    "理解基本要素和组成",
                    "识别相关概念和关系",
                ],
                "resources": [
                    {"type": "concept", "title": "核心概念学习资料"},
                    {"type": "visual", "title": "概念图谱"},
                ],
                "stage": "what",
            }
        )

        # 第二阶段：价值认知
        path.append(
            {
                "phase": 2,
                "title": "价值认知阶段",
                "description": "理解学习价值和应用场景",
                "duration_percentage": 25,
                "target_days": max(1, int(duration_days * 0.25)),
                "key_activities": [
                    "学习历史背景和发展",
                    "理解重要性和实际价值",
                    "探索行业相关性和职业益处",
                ],
                "resources": [
                    {"type": "background", "title": "背景资料"},
                    {"type": "case", "title": "应用案例"},
                ],
                "stage": "why",
            }
        )

        # 第三阶段：方法学习
        path.append(
            {
                "phase": 3,
                "title": "方法学习阶段",
                "description": "掌握基本方法和技术",
                "duration_percentage": 25,
                "target_days": max(1, int(duration_days * 0.25)),
                "key_activities": [
                    "学习基本步骤和方法",
                    "掌握核心技术和工具",
                    "完成基础练习",
                ],
                "resources": [
                    {"type": "tutorial", "title": "方法指南"},
                    {"type": "exercise", "title": "基础练习"},
                ],
                "stage": "how",
            }
        )

        # 第四阶段：实践应用
        path.append(
            {
                "phase": 4,
                "title": "实践应用阶段",
                "description": "应用所学知识解决实际问题",
                "duration_percentage": 25,
                "target_days": max(1, int(duration_days * 0.25)),
                "key_activities": ["完成实践项目", "解决实际问题", "综合应用所学知识"],
                "resources": [
                    {"type": "project", "title": "实践项目"},
                    {"type": "assessment", "title": "综合评估"},
                ],
                "stage": "how",
            }
        )

        return path

    def _generate_assessment_strategy(
        self, difficulty_level: str, learner_profile: str, how_content: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成评估策略"""

        # 根据难度级别调整评估策略
        if difficulty_level == "beginner":
            complexity = "基础"
            focus = "概念理解和基本应用"
        elif difficulty_level == "advanced":
            complexity = "高级"
            focus = "深入理解和创新应用"
        else:  # intermediate
            complexity = "中级"
            focus = "综合理解和实际应用"

        # 根据学习者画像调整评估方式
        if learner_profile == "visual":
            methods = ["可视化项目", "图表分析", "概念图评估"]
        elif learner_profile == "auditory":
            methods = ["口头答辩", "讨论评估", "讲解演示"]
        elif learner_profile == "kinesthetic":
            methods = ["实践项目", "动手操作", "实际应用"]
        else:  # reading
            methods = ["书面测验", "案例分析", "研究报告"]

        # 构建评估策略
        assessment = {
            "difficulty_level": difficulty_level,
            "complexity": complexity,
            "focus": focus,
            "methods": methods,
            "formative_assessments": [
                {
                    "type": "概念检查",
                    "description": "检查核心概念理解",
                    "frequency": "每个阶段结束",
                },
                {
                    "type": "进度跟踪",
                    "description": "跟踪学习进度和里程碑完成情况",
                    "frequency": "每周",
                },
            ],
            "summative_assessments": [
                {
                    "type": "项目评估",
                    "description": "评估实践项目完成质量",
                    "weight": 0.5,
                },
                {
                    "type": "综合测验",
                    "description": "评估整体知识掌握程度",
                    "weight": 0.3,
                },
                {"type": "应用演示", "description": "评估实际应用能力", "weight": 0.2},
            ],
            "success_criteria": [
                "掌握核心概念和原理",
                "能够应用所学知识解决问题",
                "完成所有实践项目",
                "通过综合评估",
            ],
        }

        return assessment


# 全局服务实例
teaching_service = IntelligentTeachingService()
