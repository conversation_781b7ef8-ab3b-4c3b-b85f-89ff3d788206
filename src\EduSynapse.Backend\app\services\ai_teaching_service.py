"""
EduSynapse AI Teaching Service
基于LangChain和AutoGen的智能教学服务
"""

import asyncio
import json
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

try:
    # 尝试导入LangChain，但捕获所有可能的错误
    import langchain
    from langchain.llms import OpenAI
    from langchain.chat_models import ChatOpenAI
    from langchain.prompts import (
        ChatPromptTemplate,
        SystemMessagePromptTemplate,
        HumanMessagePromptTemplate,
    )
    from langchain.schema import BaseMessage, HumanMessage, AIMessage, SystemMessage
    from langchain.memory import ConversationBufferWindowMemory
    from langchain.chains import ConversationChain

    LANGCHAIN_AVAILABLE = True
    print("✅ LangChain已加载")
except (ImportError, TypeError, AttributeError, Exception) as e:
    LANGCHAIN_AVAILABLE = False
    print(f"⚠️  LangChain加载失败: {type(e).__name__}: {e}")
    print("📝 将使用备用模式运行")

try:
    import autogen
    from autogen import AssistantAgent, UserProxyAgent, GroupChat, GroupChatManager

    AUTOGEN_AVAILABLE = True
    print("✅ AutoGen已加载")
except (ImportError, TypeError, AttributeError, Exception) as e:
    AUTOGEN_AVAILABLE = False
    print(f"⚠️  AutoGen加载失败: {type(e).__name__}: {e}")
    print("📝 将使用备用模式运行")

# 总体AI可用性
AI_FEATURES_AVAILABLE = LANGCHAIN_AVAILABLE or AUTOGEN_AVAILABLE

if not AI_FEATURES_AVAILABLE:
    print("📝 AI功能将使用备用模式运行")

from ..core.config import settings
from ..models.learning_plan import LearningPlan


class TeacherType(str, Enum):
    """AI教师类型枚举"""

    SOCRATIC = "socratic"  # 苏格拉底式
    CASE_DRIVEN = "case_driven"  # 案例驱动
    GAMIFIED = "gamified"  # 游戏化


class LearningStage(str, Enum):
    """学习阶段枚举（WWH框架）"""

    WHAT = "what"  # 是什么
    WHY = "why"  # 为什么
    HOW = "how"  # 怎么做


@dataclass
class TeachingContext:
    """教学上下文"""

    student_id: str
    topic: str
    current_stage: LearningStage
    mastery_level: float  # 0-1
    session_history: List[Dict[str, Any]]
    error_patterns: List[str]
    time_spent: timedelta
    last_interaction: datetime


class WWHFrameworkEngine:
    """WWH教学框架引擎"""

    def __init__(self):
        if (
            LANGCHAIN_AVAILABLE
            and hasattr(settings, "openai_api_key")
            and settings.openai_api_key
        ):
            try:
                self.llm = ChatOpenAI(
                    model_name="gpt-4",
                    temperature=0.7,
                    openai_api_key=settings.openai_api_key,
                )
                self.ai_enabled = True
            except Exception as e:
                print(f"⚠️  OpenAI initialization failed: {e}")
                self.ai_enabled = False
        else:
            self.ai_enabled = False
            print("⚠️  AI features disabled. Using fallback mode.")

    async def generate_what_content(
        self, topic: str, difficulty: str
    ) -> Dict[str, Any]:
        """生成What阶段内容 - 概念定义和特征"""
        if not self.ai_enabled:
            return self._generate_fallback_what_content(topic)

        try:
            prompt = ChatPromptTemplate.from_messages(
                [
                    SystemMessagePromptTemplate.from_template(
                        "你是一位专业的教学设计师，专门负责WWH框架中的'What'阶段。"
                        "你需要为学习主题生成清晰的概念定义、核心特征和关键要素。"
                        "难度级别：{difficulty}"
                    ),
                    HumanMessagePromptTemplate.from_template(
                        "请为主题'{topic}'生成What阶段的学习内容，包括：\n"
                        "1. 核心定义（简洁准确）\n"
                        "2. 关键特征（3-5个要点）\n"
                        "3. 基本要素（组成部分）\n"
                        "4. 相关概念（关联知识）\n"
                        "请以JSON格式返回，确保内容适合{difficulty}难度级别。"
                    ),
                ]
            )

            chain = ConversationChain(llm=self.llm, prompt=prompt)
            response = await chain.arun(topic=topic, difficulty=difficulty)

            return json.loads(response)
        except Exception as e:
            print(f"AI generation failed: {e}")
            return self._generate_fallback_what_content(topic)

    async def generate_why_content(
        self, topic: str, what_content: Dict
    ) -> Dict[str, Any]:
        """生成Why阶段内容 - 重要性和背景"""
        if not self.ai_enabled:
            return self._generate_fallback_why_content(topic)

        try:
            prompt = ChatPromptTemplate.from_messages(
                [
                    SystemMessagePromptTemplate.from_template(
                        "你是一位专业的教学设计师，专门负责WWH框架中的'Why'阶段。"
                        "你需要解释概念的重要性、历史背景和实际价值。"
                        "基于已有的What阶段内容：{what_content}"
                    ),
                    HumanMessagePromptTemplate.from_template(
                        "请为主题'{topic}'生成Why阶段的学习内容，包括：\n"
                        "1. 历史背景（发展脉络）\n"
                        "2. 重要性说明（为什么要学）\n"
                        "3. 实际应用价值（解决什么问题）\n"
                        "4. 学习动机（激发兴趣）\n"
                        "请以JSON格式返回，与What阶段内容形成逻辑连贯。"
                    ),
                ]
            )

            chain = ConversationChain(llm=self.llm, prompt=prompt)
            response = await chain.arun(
                topic=topic, what_content=json.dumps(what_content, ensure_ascii=False)
            )

            return json.loads(response)
        except Exception as e:
            print(f"AI generation failed: {e}")
            return self._generate_fallback_why_content(topic)

    async def generate_how_content(
        self, topic: str, what_content: Dict, why_content: Dict
    ) -> Dict[str, Any]:
        """生成How阶段内容 - 实践方法和步骤"""
        if not self.ai_enabled:
            return self._generate_fallback_how_content(topic)

        try:
            prompt = ChatPromptTemplate.from_messages(
                [
                    SystemMessagePromptTemplate.from_template(
                        "你是一位专业的教学设计师，专门负责WWH框架中的'How'阶段。"
                        "你需要提供具体的实践方法、操作步骤和代码示例。"
                        "基于已有内容：\nWhat: {what_content}\nWhy: {why_content}"
                    ),
                    HumanMessagePromptTemplate.from_template(
                        "请为主题'{topic}'生成How阶段的学习内容，包括：\n"
                        "1. 学习方法（具体步骤）\n"
                        "2. 实践项目（动手练习）\n"
                        "3. 代码示例（可运行代码）\n"
                        "4. 练习题目（巩固理解）\n"
                        "5. 推荐资源（进一步学习）\n"
                        "请以JSON格式返回，确保内容实用可操作。"
                    ),
                ]
            )

            chain = ConversationChain(llm=self.llm, prompt=prompt)
            response = await chain.arun(
                topic=topic,
                what_content=json.dumps(what_content, ensure_ascii=False),
                why_content=json.dumps(why_content, ensure_ascii=False),
            )

            return json.loads(response)
        except Exception as e:
            print(f"AI generation failed: {e}")
            return self._generate_fallback_how_content(topic)

    def _generate_fallback_what_content(self, topic: str) -> Dict[str, Any]:
        """What阶段内容生成失败时的备用方案"""
        return {
            "core_definition": f"{topic}的基本定义",
            "key_features": [f"{topic}的特征1", f"{topic}的特征2", f"{topic}的特征3"],
            "basic_elements": [f"{topic}的要素1", f"{topic}的要素2"],
            "related_concepts": [f"相关概念1", f"相关概念2"],
        }

    def _generate_fallback_why_content(self, topic: str) -> Dict[str, Any]:
        """Why阶段内容生成失败时的备用方案"""
        return {
            "historical_background": f"{topic}的发展历史",
            "importance": f"学习{topic}的重要性",
            "practical_value": f"{topic}的实际应用价值",
            "learning_motivation": f"学习{topic}的动机",
        }

    def _generate_fallback_how_content(self, topic: str) -> Dict[str, Any]:
        """How阶段内容生成失败时的备用方案"""
        return {
            "learning_methods": [f"{topic}的学习方法1", f"{topic}的学习方法2"],
            "practice_projects": [f"{topic}实践项目1", f"{topic}实践项目2"],
            "code_examples": [f"# {topic}代码示例\nprint('Hello, {topic}!')"],
            "exercises": [f"{topic}练习题1", f"{topic}练习题2"],
            "recommended_resources": [f"{topic}推荐资源1", f"{topic}推荐资源2"],
        }


class MultiAgentTeachingSystem:
    """多代理教学系统 - 基于AutoGen"""

    def __init__(self):
        if (
            AUTOGEN_AVAILABLE
            and hasattr(settings, "openai_api_key")
            and settings.openai_api_key
        ):
            self.config_list = [
                {
                    "model": "gpt-4",
                    "api_key": settings.openai_api_key,
                }
            ]
            self.ai_enabled = True
            self._initialize_teachers()
        else:
            self.ai_enabled = False
            print("⚠️  Multi-agent system disabled. Using fallback mode.")

    def _initialize_teachers(self):
        """初始化AI教师"""
        if not self.ai_enabled:
            return

        try:
            # 初始化不同类型的AI教师
            self.socratic_teacher = self._create_socratic_teacher()
            self.case_driven_teacher = self._create_case_driven_teacher()
            self.gamified_teacher = self._create_gamified_teacher()
            self.coordinator = self._create_coordinator()
        except Exception as e:
            print(f"Teacher initialization failed: {e}")
            self.ai_enabled = False

    def _create_socratic_teacher(self) -> Optional[Any]:
        """创建苏格拉底式教师代理"""
        if not AUTOGEN_AVAILABLE:
            return None

        return AssistantAgent(
            name="SocraticTeacher",
            system_message="""
            你是一位苏格拉底式教师，擅长通过连续追问引导学生深入思考。
            你的教学特点：
            1. 不直接给出答案，而是通过问题引导学生自己发现真理
            2. 善于发现学生思维中的矛盾和盲点
            3. 循序渐进地深化问题的复杂度
            4. 鼓励学生质疑和批判性思考
            """,
            llm_config={"config_list": self.config_list},
        )

    def _create_case_driven_teacher(self) -> Optional[Any]:
        """创建案例驱动教师代理"""
        if not AUTOGEN_AVAILABLE:
            return None

        return AssistantAgent(
            name="CaseDrivenTeacher",
            system_message="""
            你是一位案例驱动教师，擅长通过真实项目场景进行教学。
            你的教学特点：
            1. 总是从实际应用场景出发
            2. 提供丰富的代码示例和项目案例
            3. 强调理论与实践的结合
            4. 关注解决实际问题的能力
            """,
            llm_config={"config_list": self.config_list},
        )

    def _create_gamified_teacher(self) -> Optional[Any]:
        """创建游戏化教师代理"""
        if not AUTOGEN_AVAILABLE:
            return None

        return AssistantAgent(
            name="GamifiedTeacher",
            system_message="""
            你是一位游戏化教师，擅长通过游戏化元素激发学习兴趣。
            你的教学特点：
            1. 将学习过程游戏化，设置挑战和奖励
            2. 使用积分、等级、成就等激励机制
            3. 创造有趣的学习情境和角色扮演
            4. 鼓励竞争和协作
            """,
            llm_config={"config_list": self.config_list},
        )

    def _create_coordinator(self) -> Optional[Any]:
        """创建教师协调器"""
        if not AUTOGEN_AVAILABLE:
            return None

        return AssistantAgent(
            name="TeacherCoordinator",
            system_message="""
            你是教师协调器，负责根据学生的学习状态选择最适合的教学策略。
            """,
            llm_config={"config_list": self.config_list},
        )

    async def conduct_teaching_session(
        self, context: TeachingContext, user_input: str
    ) -> Tuple[str, TeacherType]:
        """进行教学会话"""

        if not self.ai_enabled:
            return (
                self._fallback_response(user_input, context.topic),
                TeacherType.CASE_DRIVEN,
            )

        # 选择合适的教师
        selected_teacher_type = await self._select_teacher(context)

        try:
            # 这里应该实现真正的AutoGen对话
            # 暂时返回模拟响应
            response = f"作为{selected_teacher_type.value}教师，我来回答你关于{context.topic}的问题：{user_input}"
            return response, selected_teacher_type

        except Exception as e:
            print(f"Teaching session failed: {e}")
            return (
                self._fallback_response(user_input, context.topic),
                selected_teacher_type,
            )

    async def _select_teacher(self, context: TeachingContext) -> TeacherType:
        """根据教学上下文选择合适的教师"""

        # 基于学习阶段的基础选择
        if context.current_stage == LearningStage.WHAT:
            if context.mastery_level < 0.3:
                return TeacherType.SOCRATIC  # 概念理解困难
            else:
                return TeacherType.CASE_DRIVEN  # 需要具体例子

        elif context.current_stage == LearningStage.WHY:
            if len(context.error_patterns) > 3:
                return TeacherType.SOCRATIC  # 需要深入思考
            else:
                return TeacherType.GAMIFIED  # 激发学习动机

        elif context.current_stage == LearningStage.HOW:
            return TeacherType.CASE_DRIVEN  # 实践导向

        return TeacherType.CASE_DRIVEN  # 默认选择

    def _fallback_response(self, user_input: str, topic: str) -> str:
        """AI不可用时的备用响应"""
        return f"关于{topic}的问题：{user_input}，这是一个很好的问题。让我为你提供一些基础信息..."


class IntelligentTeachingService:
    """智能教学服务 - 整合WWH框架和多代理系统"""

    def __init__(self):
        self.wwh_engine = WWHFrameworkEngine()
        self.multi_agent_system = MultiAgentTeachingSystem()
        self.active_sessions: Dict[str, TeachingContext] = {}

    async def generate_learning_plan(
        self,
        topic: str,
        difficulty_level: str = "intermediate",
        duration_days: int = 14,
    ) -> Dict[str, Any]:
        """生成智能学习计划"""

        print(f"Generating learning plan for topic: {topic}")

        try:
            # 生成WWH框架内容
            what_content = await self.wwh_engine.generate_what_content(
                topic, difficulty_level
            )
            why_content = await self.wwh_engine.generate_why_content(
                topic, what_content
            )
            how_content = await self.wwh_engine.generate_how_content(
                topic, what_content, why_content
            )

            # 构建学习计划
            plan = {
                "topic": topic,
                "description": f"AI生成的{topic}学习计划",
                "difficulty_level": difficulty_level,
                "duration_days": duration_days,
                "wwh_framework": {
                    "what": what_content,
                    "why": why_content,
                    "how": how_content,
                },
                "daily_plans": self._generate_daily_plans(duration_days),
                "ai_generated": True,
                "created_at": datetime.now().isoformat(),
            }

            print(f"Successfully generated learning plan for: {topic}")
            return plan

        except Exception as e:
            print(f"Failed to generate learning plan: {e}")
            raise

    async def start_teaching_session(self, student_id: str, topic: str) -> str:
        """开始教学会话"""

        context = TeachingContext(
            student_id=student_id,
            topic=topic,
            current_stage=LearningStage.WHAT,
            mastery_level=0.0,
            session_history=[],
            error_patterns=[],
            time_spent=timedelta(),
            last_interaction=datetime.now(),
        )

        self.active_sessions[student_id] = context

        # 生成开场白
        welcome_message = f"""
        🎓 欢迎来到EduSynapse智能教学系统！
        
        我们将一起学习「{topic}」这个主题。
        我会根据你的学习进度，动态调整教学策略，确保最佳的学习效果。
        
        让我们从「What - 是什么」阶段开始吧！
        你对{topic}有什么初步的了解吗？
        """

        return welcome_message.strip()

    async def continue_teaching_session(
        self, student_id: str, user_input: str
    ) -> Tuple[str, Dict[str, Any]]:
        """继续教学会话"""

        if student_id not in self.active_sessions:
            raise ValueError(f"No active session found for student: {student_id}")

        context = self.active_sessions[student_id]

        # 更新上下文
        context.last_interaction = datetime.now()
        context.session_history.append(
            {
                "timestamp": datetime.now().isoformat(),
                "user_input": user_input,
                "stage": context.current_stage.value,
            }
        )

        # 进行教学对话
        response, teacher_type = await self.multi_agent_system.conduct_teaching_session(
            context, user_input
        )

        # 更新会话历史
        context.session_history.append(
            {
                "timestamp": datetime.now().isoformat(),
                "ai_response": response,
                "teacher_type": teacher_type.value,
                "stage": context.current_stage.value,
            }
        )

        # 返回响应和元数据
        metadata = {
            "teacher_type": teacher_type.value,
            "current_stage": context.current_stage.value,
            "mastery_level": context.mastery_level,
            "session_length": len(context.session_history),
        }

        return response, metadata

    def _generate_daily_plans(self, duration_days: int) -> List[Dict[str, Any]]:
        """生成每日学习计划"""
        daily_plans = []

        # 简化版：平均分配WWH阶段
        what_days = duration_days // 3
        why_days = duration_days // 3
        how_days = duration_days - what_days - why_days

        current_day = 1

        # What阶段
        for day in range(what_days):
            daily_plans.append(
                {
                    "day": current_day,
                    "stage": "what",
                    "focus": "概念理解",
                    "estimated_hours": 2.0,
                }
            )
            current_day += 1

        # Why阶段
        for day in range(why_days):
            daily_plans.append(
                {
                    "day": current_day,
                    "stage": "why",
                    "focus": "背景理解",
                    "estimated_hours": 2.0,
                }
            )
            current_day += 1

        # How阶段
        for day in range(how_days):
            daily_plans.append(
                {
                    "day": current_day,
                    "stage": "how",
                    "focus": "实践应用",
                    "estimated_hours": 2.0,
                }
            )
            current_day += 1

        return daily_plans


# 全局服务实例
teaching_service = IntelligentTeachingService()
