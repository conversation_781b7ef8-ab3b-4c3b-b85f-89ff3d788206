# EduSynapse MAUI 故障排除指南

## Visual Studio 2022 调试问题

### 问题：AppxManifest 配置错误

**错误信息**：
```
Improper project configuration: no AppxManifest is specified, but WindowsPackageType is not set to MSIX.
```

**解决方案**：

#### 方法 1：使用 Windows (Unpackaged) 配置
1. 在 Visual Studio 中，点击调试目标下拉菜单
2. 选择 "Windows (Unpackaged)" 而不是 "Windows Machine"
3. 按 F5 启动调试

#### 方法 2：清理和重建项目
1. 在 Visual Studio 中右键点击项目
2. 选择 "清理"
3. 然后选择 "重新生成"
4. 尝试再次运行

#### 方法 3：检查 MAUI 工作负载
确保已安装 .NET MAUI 工作负载：
```bash
dotnet workload install maui
```

#### 方法 4：使用命令行运行
```bash
cd src/EduSynapse.MAUI
dotnet build
dotnet run --framework net8.0-windows10.0.19041.0
```

### 其他常见问题

#### 1. NuGet 包还原失败
```bash
dotnet restore
```

#### 2. WebView2 运行时缺失
- 下载并安装 Microsoft Edge WebView2 Runtime
- 或者在项目中添加 WebView2 NuGet 包

#### 3. Windows SDK 版本问题
确保安装了 Windows 10 SDK (10.0.19041.0 或更高版本)

#### 4. Visual Studio 版本要求
- Visual Studio 2022 17.8 或更高版本
- 确保安装了 ".NET Multi-platform App UI development" 工作负载

### 调试配置

项目支持两种调试配置：

1. **Windows Machine** (打包应用)
   - 使用 MSIX 打包
   - 适合发布和分发

2. **Windows (Unpackaged)** (未打包应用)
   - 直接运行，无需打包
   - 适合开发和调试
   - **推荐用于开发阶段**

### 性能优化建议

1. **调试模式**：
   - 使用 "Windows (Unpackaged)" 配置
   - 启用热重载功能

2. **发布模式**：
   - 使用 "Windows Machine" 配置
   - 启用 AOT 编译（如果需要）

### 环境检查清单

在开始调试前，请确认：

- [ ] Visual Studio 2022 (17.8+)
- [ ] .NET 8.0 SDK
- [ ] .NET MAUI 工作负载
- [ ] Windows 10 SDK (19041+)
- [ ] WebView2 Runtime

### 获取帮助

如果问题仍然存在：

1. 检查 Visual Studio 输出窗口的详细错误信息
2. 查看 Windows 事件查看器中的应用程序日志
3. 尝试创建一个新的 MAUI 项目来验证环境配置

### 有用的命令

```bash
# 检查 .NET 版本
dotnet --version

# 检查已安装的工作负载
dotnet workload list

# 修复工作负载
dotnet workload repair

# 清理 NuGet 缓存
dotnet nuget locals all --clear
```
