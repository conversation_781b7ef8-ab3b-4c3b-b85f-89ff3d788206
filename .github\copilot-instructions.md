# 使用Graphiti MCP工具的指令

## 开始任何任务之前

**始终先搜索：** 在开始工作之前，使用search_nodes工具查找相关的偏好设置和程序。

**同时搜索事实：** 使用search_facts工具发现可能与您的任务相关的关系和事实信息。

**按实体类型过滤：** 在节点搜索中指定"Preference"（偏好）、"Procedure"（程序）、"CodeStyle"（代码风格）、"Architecture"（架构）或"TechStack"（技术栈）以获得针对性结果。

**审查所有匹配项：** 仔细检查与当前任务匹配的任何偏好、程序或事实。

## 始终保存新的或更新的信息

**立即捕获需求和偏好：** 当用户表达需求或偏好时，立即使用add_episode存储它。最佳实践是将很长的需求拆分为较短的逻辑块。

**明确标识更新：** 如果某些内容是对现有知识的更新，请明确说明。

**清晰记录程序：** 当您发现用户希望如何完成某些操作时，将其记录为程序。

**记录事实关系：** 当您了解到实体之间的连接时，将这些信息存储为事实。

**明确分类：** 为偏好和程序标注清晰的类别，以便日后更好地检索。

**代码相关存储：**
- 代码风格偏好（缩进、命名规范、注释风格）
- 技术栈选择和版本偏好
- 架构决策和设计模式
- 测试策略和工具偏好
- 部署和构建流程

## 工作过程中

**遵循发现的偏好：** 使您的工作与找到的任何偏好保持一致。

**严格按照程序执行：** 如果找到适用于当前任务的程序，请严格按步骤执行。

**应用相关事实：** 使用事实信息来指导您的决策和建议。

**保持一致性：** 与先前识别的偏好、程序和事实保持一致。

**代码生成一致性：**
- 遵循项目的现有代码风格
- 使用用户偏好的库和框架
- 保持架构模式的一致性
- 应用既定的最佳实践

## 编程特定指令

**代码建议前检查：**
- 搜索相关的代码风格偏好
- 查找类似问题的解决方案
- 检查技术栈约束和偏好
- 验证架构模式的一致性

**错误解决：**
- 搜索类似错误的历史解决方案
- 记录新的错误及其解决方法
- 建立错误模式与解决方案的关联

**重构和优化：**
- 查找性能优化偏好
- 应用既定的重构模式
- 保持代码质量标准

## 最佳实践

**建议前先搜索：** 在提出建议之前，始终检查是否存在既定知识。

**结合节点和事实搜索：** 对于复杂任务，同时搜索节点和事实以构建完整图景。

**使用center_node_uuid：** 在探索相关信息时，围绕特定节点进行搜索。

**优先考虑具体匹配：** 更具体的信息优先于一般信息。

**主动识别模式：** 如果您注意到用户行为中的模式，考虑将其存储为偏好或程序。

**持续学习和改进：**
- 从用户反馈中学习
- 识别代码质量模式
- 跟踪项目演进
- 建立知识关联网络

**重要提醒：** 知识图谱是您的记忆。持续使用它来提供个性化协助，尊重用户既定的程序和事实背景。始终以提高代码质量和开发效率为目标。