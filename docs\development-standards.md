# EduSynapse 开发规范和最佳实践

## 📋 文档信息

- **文档版本**: v1.0
- **创建日期**: 2025-07-06
- **最后更新**: 2025-07-06
- **维护人**: 技术负责人
- **适用范围**: EduSynapse 项目全体开发人员

---

## 📖 目录

1. [代码规范](#代码规范)
2. [项目结构](#项目结构)
3. [命名约定](#命名约定)
4. [注释规范](#注释规范)
5. [错误处理](#错误处理)
6. [性能优化](#性能优化)
7. [安全规范](#安全规范)
8. [测试规范](#测试规范)
9. [Git 规范](#git-规范)
10. [代码审查](#代码审查)

---

## 代码规范

### 🎯 基本原则

1. **可读性优先** - 代码应该易于理解和维护
2. **一致性** - 整个项目保持统一的编码风格
3. **简洁性** - 避免不必要的复杂性
4. **可测试性** - 代码应该易于测试
5. **性能考虑** - 在保证可读性的前提下优化性能

### 📝 C# 编码规范

#### 基本格式
```csharp
// ✅ 正确示例
public class StudentService : IStudentService
{
    private readonly ILogger<StudentService> _logger;
    private readonly HttpClient _httpClient;

    public StudentService(ILogger<StudentService> logger, HttpClient httpClient)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
    }

    public async Task<List<Student>> GetAllStudentsAsync()
    {
        try
        {
            _logger.LogInformation("开始获取所有学生信息");

            var response = await _httpClient.GetFromJsonAsync<ApiResponse<List<Student>>>("api/students");

            if (response?.Success == true && response.Data != null)
            {
                _logger.LogInformation("成功获取 {Count} 个学生信息", response.Data.Count);
                return response.Data;
            }

            _logger.LogWarning("获取学生信息失败: {Message}", response?.Message);
            return new List<Student>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取学生信息时发生异常");
            throw;
        }
    }
}
```

#### 代码格式要求
- **缩进**: 使用 4 个空格，不使用 Tab
- **行长度**: 每行不超过 120 个字符
- **大括号**: 使用 Allman 风格 (大括号独占一行)
- **空行**: 方法之间、逻辑块之间使用空行分隔

### 🌐 Blazor 组件规范

#### 组件结构
```razor
@* 组件文件头部注释 *@
@*
    组件名称: StudentCard
    功能描述: 显示学生基本信息的卡片组件
    创建人: [开发者姓名]
    创建时间: 2024-01-15
*@

@using MudBlazor
@inject ILogger<StudentCard> Logger
@inject ISnackbar Snackbar

<MudCard Class="ma-2" Style="max-width: 300px;">
    <MudCardHeader>
        <CardHeaderAvatar>
            <MudAvatar Color="Color.Primary">
                @GetInitials(Student.Name)
            </MudAvatar>
        </CardHeaderAvatar>
        <CardHeaderContent>
            <MudText Typo="Typo.body1">@Student.Name</MudText>
            <MudText Typo="Typo.body2" Class="text-muted">@Student.Class</MudText>
        </CardHeaderContent>
    </MudCardHeader>

    <MudCardContent>
        <MudText Typo="Typo.body2">
            <strong>年龄:</strong> @Student.Age
        </MudText>
        <MudText Typo="Typo.body2">
            <strong>邮箱:</strong> @Student.Email
        </MudText>
    </MudCardContent>

    <MudCardActions>
        <MudButton Size="Size.Small"
                   Color="Color.Primary"
                   StartIcon="@Icons.Material.Filled.Edit"
                   OnClick="HandleEdit">
            编辑
        </MudButton>
        <MudButton Size="Size.Small"
                   Color="Color.Error"
                   StartIcon="@Icons.Material.Filled.Delete"
                   OnClick="HandleDelete">
            删除
        </MudButton>
    </MudCardActions>
</MudCard>

@code {
    /// <summary>
    /// 学生信息数据
    /// </summary>
    [Parameter, EditorRequired]
    public Student Student { get; set; } = new();

    /// <summary>
    /// 编辑按钮点击事件
    /// </summary>
    [Parameter]
    public EventCallback<Student> OnEdit { get; set; }

    /// <summary>
    /// 删除按钮点击事件
    /// </summary>
    [Parameter]
    public EventCallback<Student> OnDelete { get; set; }

    /// <summary>
    /// 获取姓名首字母
    /// </summary>
    /// <param name="name">姓名</param>
    /// <returns>首字母</returns>
    private string GetInitials(string name)
    {
        if (string.IsNullOrWhiteSpace(name))
            return "?";

        return name.Trim().Substring(0, 1).ToUpper();
    }

    /// <summary>
    /// 处理编辑按钮点击
    /// </summary>
    private async Task HandleEdit()
    {
        try
        {
            Logger.LogInformation("编辑学生信息: {StudentId}", Student.Id);
            await OnEdit.InvokeAsync(Student);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "编辑学生信息时发生错误");
            Snackbar.Add("编辑失败，请重试", Severity.Error);
        }
    }

    /// <summary>
    /// 处理删除按钮点击
    /// </summary>
    private async Task HandleDelete()
    {
        try
        {
            Logger.LogInformation("删除学生信息: {StudentId}", Student.Id);
            await OnDelete.InvokeAsync(Student);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "删除学生信息时发生错误");
            Snackbar.Add("删除失败，请重试", Severity.Error);
        }
    }
}
```

#### Blazor 组件最佳实践
1. **组件职责单一** - 每个组件只负责一个明确的功能
2. **参数验证** - 使用 `[EditorRequired]` 标记必需参数
3. **错误处理** - 在事件处理方法中添加异常处理
4. **性能优化** - 合理使用 `StateHasChanged()` 和 `@key` 指令
5. **可访问性** - 添加适当的 ARIA 标签和语义化标记

---

## 项目结构

### 📁 标准项目结构

```
EduSynapse.MAUI/
├── 📁 Components/                    # Blazor 组件
│   ├── Layout/                      # 布局组件
│   │   ├── MainLayout.razor         # 主布局
│   │   ├── NavMenu.razor           # 导航菜单
│   │   └── LoadingLayout.razor     # 加载布局
│   ├── Pages/                       # 页面组件
│   │   ├── Students/               # 学生管理页面
│   │   │   ├── StudentList.razor   # 学生列表
│   │   │   ├── StudentDetail.razor # 学生详情
│   │   │   └── StudentForm.razor   # 学生表单
│   │   ├── Courses/                # 课程管理页面
│   │   └── Dashboard/              # 仪表板页面
│   ├── Shared/                     # 共享组件
│   │   ├── ConfirmDialog.razor     # 确认对话框
│   │   ├── LoadingSpinner.razor    # 加载指示器
│   │   └── ErrorBoundary.razor     # 错误边界
│   └── Forms/                      # 表单组件
│       ├── StudentForm.razor       # 学生表单
│       └── CourseForm.razor        # 课程表单
├── 📁 Services/                     # 业务服务
│   ├── Interfaces/                 # 服务接口
│   │   ├── IStudentService.cs      # 学生服务接口
│   │   ├── ICourseService.cs       # 课程服务接口
│   │   └── IAuthService.cs         # 认证服务接口
│   ├── Implementation/             # 服务实现
│   │   ├── StudentService.cs       # 学生服务实现
│   │   ├── CourseService.cs        # 课程服务实现
│   │   └── AuthService.cs          # 认证服务实现
│   └── Common/                     # 通用服务
│       ├── ApiService.cs           # API 服务
│       ├── CacheService.cs         # 缓存服务
│       └── NotificationService.cs  # 通知服务
├── 📁 Models/                       # 数据模型
│   ├── Entities/                   # 实体模型
│   │   ├── Student.cs              # 学生实体
│   │   ├── Course.cs               # 课程实体
│   │   └── User.cs                 # 用户实体
│   ├── DTOs/                       # 数据传输对象
│   │   ├── StudentDto.cs           # 学生 DTO
│   │   ├── CourseDto.cs            # 课程 DTO
│   │   └── UserDto.cs              # 用户 DTO
│   ├── ViewModels/                 # 视图模型
│   │   ├── StudentViewModel.cs     # 学生视图模型
│   │   └── CourseViewModel.cs      # 课程视图模型
│   └── Common/                     # 通用模型
│       ├── ApiResponse.cs          # API 响应模型
│       ├── PagedResult.cs          # 分页结果模型
│       └── ErrorModel.cs           # 错误模型
├── 📁 Utils/                        # 工具类
│   ├── Extensions/                 # 扩展方法
│   │   ├── StringExtensions.cs     # 字符串扩展
│   │   └── DateTimeExtensions.cs   # 日期时间扩展
│   ├── Helpers/                    # 帮助类
│   │   ├── ValidationHelper.cs     # 验证帮助类
│   │   └── FormatHelper.cs         # 格式化帮助类
│   └── Constants/                  # 常量定义
│       ├── ApiConstants.cs         # API 常量
│       └── AppConstants.cs         # 应用常量
├── 📁 wwwroot/                      # 静态资源
│   ├── css/                        # 样式文件
│   │   ├── app.css                 # 应用样式
│   │   └── custom.css              # 自定义样式
│   ├── js/                         # JavaScript 文件
│   │   └── app.js                  # 应用脚本
│   ├── images/                     # 图片资源
│   └── fonts/                      # 字体文件
├── 📁 Platforms/                    # 平台特定代码
│   ├── Windows/                    # Windows 平台
│   ├── Android/                    # Android 平台
│   └── iOS/                        # iOS 平台
├── 📄 App.xaml                     # 应用程序配置
├── 📄 App.xaml.cs                  # 应用程序代码
├── 📄 AppShell.xaml                # 应用程序外壳
├── 📄 MauiProgram.cs               # 程序入口点
└── 📄 appsettings.json             # 应用配置
```

### 📋 文件组织原则

1. **按功能分组** - 相关的文件放在同一个文件夹中
2. **层次清晰** - 文件夹层次不超过 4 层
3. **命名一致** - 文件和文件夹命名保持一致的风格
4. **职责分离** - 不同职责的代码分别放在不同的文件夹中

---

## 命名约定

### 🏷️ 通用命名规则

#### 文件和文件夹命名
```
✅ 正确示例:
- StudentService.cs
- StudentList.razor
- IStudentService.cs
- student-management/
- api-responses/

❌ 错误示例:
- studentservice.cs
- Student_List.razor
- IstudentService.cs
- StudentManagement/
- API_Responses/
```

#### 类和接口命名
```csharp
// ✅ 正确示例
public class StudentService { }
public interface IStudentService { }
public class ApiResponse<T> { }
public enum UserRole { }

// ❌ 错误示例
public class studentService { }
public interface StudentService { }
public class apiResponse<T> { }
public enum userRole { }
```

#### 方法和属性命名
```csharp
// ✅ 正确示例
public async Task<List<Student>> GetAllStudentsAsync() { }
public string StudentName { get; set; }
public bool IsActive { get; set; }

// ❌ 错误示例
public async Task<List<Student>> getAllStudents() { }
public string studentName { get; set; }
public bool isActive { get; set; }
```

#### 变量和参数命名
```csharp
// ✅ 正确示例
private readonly ILogger<StudentService> _logger;
private string studentName;
public void ProcessStudent(Student student, bool isNewStudent) { }

// ❌ 错误示例
private readonly ILogger<StudentService> logger;
private string StudentName;
public void ProcessStudent(Student s, bool b) { }
```

### 🎯 Blazor 特定命名

#### 组件命名
```razor
@* ✅ 正确示例 *@
StudentCard.razor
StudentList.razor
ConfirmDialog.razor

@* ❌ 错误示例 *@
studentCard.razor
Student_List.razor
confirmDialog.razor
```

#### 参数和事件命名
```csharp
// ✅ 正确示例
[Parameter] public Student StudentData { get; set; }
[Parameter] public EventCallback<Student> OnStudentSelected { get; set; }

// ❌ 错误示例
[Parameter] public Student student { get; set; }
[Parameter] public EventCallback<Student> StudentSelected { get; set; }
```

---

## 注释规范

### 📝 XML 文档注释

#### 类注释
```csharp
/// <summary>
/// 学生服务类，提供学生信息的 CRUD 操作
/// </summary>
/// <remarks>
/// 该服务类封装了与学生相关的所有业务逻辑，包括数据验证、API 调用等
/// </remarks>
public class StudentService : IStudentService
{
    // 类实现
}
```

#### 方法注释
```csharp
/// <summary>
/// 异步获取所有学生信息
/// </summary>
/// <param name="includeInactive">是否包含非活跃学生，默认为 false</param>
/// <returns>学生信息列表，如果没有数据则返回空列表</returns>
/// <exception cref="HttpRequestException">当 API 请求失败时抛出</exception>
/// <exception cref="JsonException">当响应数据格式错误时抛出</exception>
public async Task<List<Student>> GetAllStudentsAsync(bool includeInactive = false)
{
    // 方法实现
}
```

#### 属性注释
```csharp
/// <summary>
/// 获取或设置学生姓名
/// </summary>
/// <value>学生的完整姓名，不能为空或仅包含空白字符</value>
[Required(ErrorMessage = "学生姓名不能为空")]
[StringLength(50, ErrorMessage = "学生姓名长度不能超过50个字符")]
public string Name { get; set; } = "";
```

### 💬 代码注释

#### 单行注释
```csharp
// 验证学生信息的完整性
if (string.IsNullOrWhiteSpace(student.Name))
{
    throw new ArgumentException("学生姓名不能为空", nameof(student));
}

// TODO: 添加邮箱格式验证
// HACK: 临时解决方案，需要在下个版本中优化
// NOTE: 这里使用缓存来提高性能
```

#### 多行注释
```csharp
/*
 * 复杂的业务逻辑处理
 * 1. 首先验证用户权限
 * 2. 然后检查数据完整性
 * 3. 最后执行业务操作
 *
 * 注意：这个方法可能会抛出多种异常，调用时需要适当处理
 */
```

### 🌐 Blazor 组件注释

```razor
@*
    学生信息卡片组件

    功能说明：
    - 显示学生的基本信息（姓名、年龄、班级等）
    - 提供编辑和删除操作按钮
    - 支持自定义样式和事件处理

    使用示例：
    <StudentCard Student="@selectedStudent"
                 OnEdit="HandleEdit"
                 OnDelete="HandleDelete" />
*@

<MudCard>
    @* 卡片头部 - 显示学生头像和基本信息 *@
    <MudCardHeader>
        <!-- 组件内容 -->
    </MudCardHeader>

    @* 卡片内容 - 显示详细信息 *@
    <MudCardContent>
        <!-- 组件内容 -->
    </MudCardContent>
</MudCard>
```