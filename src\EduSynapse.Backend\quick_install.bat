@echo off
echo ========================================
echo  🎓 EduSynapse AI后端 - 快速安装
echo ========================================

echo 🔍 检查Python版本...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python未安装或不在PATH中
    pause
    exit /b 1
)

echo.
echo 📦 升级pip...
python -m pip install --upgrade pip

echo.
echo 🚀 安装核心依赖包...
echo 📋 使用requirements-core.txt (只包含必需包)

echo   安装兼容版本的Pydantic...
python -m pip install "pydantic>=2.0.0,<2.6.0" "pydantic-settings>=2.0.0,<2.6.0"

echo   安装其他核心依赖...
python -m pip install -r requirements-core.txt

if %errorlevel% neq 0 (
    echo.
    echo ⚠️  核心依赖安装失败，尝试智能安装脚本...
    python install_dependencies.py
) else (
    echo ✅ 核心依赖安装成功！
)

echo.
echo 🔧 尝试安装AI增强包...
echo 📝 这些包是可选的，安装失败不影响基本功能

echo   安装LangChain...
python -m pip install "langchain>=0.1.0" --no-deps
python -m pip install "langchain-openai>=0.0.2" --no-deps
python -m pip install "langchain-community>=0.0.10" --no-deps

echo   安装AutoGen...
python -m pip install "pyautogen>=0.2.7" --no-deps

echo   安装数据处理包...
python -m pip install "pandas>=2.0.0" "numpy>=1.24.0" "scikit-learn>=1.3.0"

echo   安装其他AI工具...
python -m pip install "tiktoken>=0.5.0" "structlog>=23.0.0"

echo.
echo ========================================
echo 🎉 安装完成！
echo ========================================
echo.
echo 📋 下一步:
echo   1. 配置.env文件 (可选，用于AI功能)
echo   2. 运行: python main.py
echo   3. 访问: http://localhost:8000
echo.
echo 💡 提示:
echo   - 没有OpenAI API Key也可以运行基础功能
echo   - AI功能不可用时会自动切换到备用模式
echo   - 查看 /api/ai/status 了解AI服务状态
echo.
pause
