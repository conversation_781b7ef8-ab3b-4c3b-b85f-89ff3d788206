﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="EduSynapse.MAUI.MainPage"
             Title="EduSynapse - 智能学习系统">

    <ScrollView>
        <VerticalStackLayout Padding="40" Spacing="30">

            <!-- 标题区域 -->
            <Frame BackgroundColor="#2196F3" Padding="30" CornerRadius="15" HasShadow="True">
                <StackLayout>
                    <Label Text="🎓 EduSynapse"
                           FontSize="32"
                           FontAttributes="Bold"
                           TextColor="White"
                           HorizontalOptions="Center" />
                    <Label Text="智能学习系统"
                           FontSize="18"
                           TextColor="White"
                           HorizontalOptions="Center"
                           Margin="0,5,0,0" />
                </StackLayout>
            </Frame>

            <!-- 状态信息 -->
            <Frame BackgroundColor="#E8F5E8" Padding="20" CornerRadius="10">
                <StackLayout>
                    <Label Text="✅ 系统状态"
                           FontSize="18"
                           FontAttributes="Bold"
                           TextColor="#2E7D32" />
                    <Label Text="应用程序已成功启动并正常运行"
                           FontSize="14"
                           TextColor="#388E3C"
                           Margin="0,5,0,0" />
                    <Label Text="第一阶段：基础框架测试完成"
                           FontSize="14"
                           TextColor="#388E3C" />
                </StackLayout>
            </Frame>

            <!-- 功能按钮 -->
            <Button x:Name="GetStartedBtn"
                    Text="🚀 开始使用 EduSynapse"
                    FontSize="16"
                    BackgroundColor="#4CAF50"
                    TextColor="White"
                    Clicked="OnGetStartedClicked"
                    CornerRadius="10"
                    Padding="15"
                    HorizontalOptions="Fill" />

            <Button x:Name="FunctionTestBtn"
                    Text="🧪 功能测试中心"
                    FontSize="16"
                    BackgroundColor="#2196F3"
                    TextColor="White"
                    Clicked="OnFunctionTestClicked"
                    CornerRadius="10"
                    Padding="15"
                    HorizontalOptions="Fill"
                    Margin="0,10,0,0" />

            <Button x:Name="LearningPlanBtn"
                    Text="📚 创建学习计划"
                    FontSize="16"
                    BackgroundColor="#FF9800"
                    TextColor="White"
                    Clicked="OnLearningPlanClicked"
                    CornerRadius="10"
                    Padding="15"
                    HorizontalOptions="Fill"
                    Margin="0,10,0,0" />

            <Button x:Name="LearningPlanListBtn"
                    Text="📋 我的学习计划"
                    FontSize="16"
                    BackgroundColor="#4CAF50"
                    TextColor="White"
                    Clicked="OnLearningPlanListClicked"
                    CornerRadius="10"
                    Padding="15"
                    HorizontalOptions="Fill"
                    Margin="0,10,0,0" />

            <Button x:Name="DataVerificationBtn"
                    Text="🔍 数据验证中心"
                    FontSize="16"
                    BackgroundColor="#9C27B0"
                    TextColor="White"
                    Clicked="OnDataVerificationClicked"
                    CornerRadius="10"
                    Padding="15"
                    HorizontalOptions="Fill"
                    Margin="0,10,0,0" />

            <!-- 版本信息 -->
            <Frame BackgroundColor="#F5F5F5" Padding="15" CornerRadius="8">
                <StackLayout>
                    <Label Text="📋 版本信息"
                           FontSize="16"
                           FontAttributes="Bold"
                           TextColor="#666" />
                    <Label Text="版本: 1.0.0 (第一阶段测试版)"
                           FontSize="12"
                           TextColor="#666" />
                    <Label Text="框架: .NET MAUI 9.0"
                           FontSize="12"
                           TextColor="#666" />
                    <Label Text="状态: 基础功能正常"
                           FontSize="12"
                           TextColor="#666" />
                </StackLayout>
            </Frame>

        </VerticalStackLayout>
    </ScrollView>

</ContentPage>
