"""
EduSynapse Logging Utilities
日志系统工具
"""

import logging
import logging.handlers
import sys
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
import structlog
from structlog.stdlib import LoggerFactory


class JSONFormatter(logging.Formatter):
    """JSON格式化器"""
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录为JSON"""
        log_entry = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        
        # 添加异常信息
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        # 添加额外字段
        if hasattr(record, 'extra_fields'):
            log_entry.update(record.extra_fields)
        
        return json.dumps(log_entry, ensure_ascii=False)


class StructuredLogger:
    """结构化日志器"""
    
    def __init__(self, name: str):
        self.logger = structlog.get_logger(name)
    
    def info(self, message: str, **kwargs):
        """记录信息日志"""
        self.logger.info(message, **kwargs)
    
    def debug(self, message: str, **kwargs):
        """记录调试日志"""
        self.logger.debug(message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """记录警告日志"""
        self.logger.warning(message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """记录错误日志"""
        self.logger.error(message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """记录严重错误日志"""
        self.logger.critical(message, **kwargs)
    
    def log_ai_interaction(self, 
                          student_id: str, 
                          teacher_type: str, 
                          stage: str, 
                          input_text: str, 
                          response_text: str,
                          processing_time: float):
        """记录AI交互日志"""
        self.info(
            "AI interaction completed",
            student_id=student_id,
            teacher_type=teacher_type,
            stage=stage,
            input_length=len(input_text),
            response_length=len(response_text),
            processing_time=processing_time,
            event_type="ai_interaction"
        )
    
    def log_progress_update(self, 
                           student_id: str, 
                           metrics: Dict[str, float],
                           insights: Dict[str, Any]):
        """记录进度更新日志"""
        self.info(
            "Progress metrics updated",
            student_id=student_id,
            overall_score=metrics.get('overall_score', 0),
            knowledge_mastery=metrics.get('knowledge_mastery', 0),
            practice_completion=metrics.get('practice_completion', 0),
            insights_count=len(insights.get('recommendations', [])),
            event_type="progress_update"
        )
    
    def log_plan_generation(self, 
                           topic: str, 
                           duration_days: int,
                           difficulty: str,
                           generation_time: float,
                           success: bool):
        """记录计划生成日志"""
        self.info(
            "Learning plan generation completed",
            topic=topic,
            duration_days=duration_days,
            difficulty=difficulty,
            generation_time=generation_time,
            success=success,
            event_type="plan_generation"
        )
    
    def log_error_pattern(self, 
                         student_id: str, 
                         error_type: str,
                         frequency: int,
                         severity: float):
        """记录错误模式日志"""
        self.warning(
            "Error pattern detected",
            student_id=student_id,
            error_type=error_type,
            frequency=frequency,
            severity=severity,
            event_type="error_pattern"
        )


def setup_logging(log_level: str = "INFO", 
                 log_format: str = "json",
                 log_file: Optional[str] = None) -> None:
    """设置日志系统"""
    
    # 创建日志目录
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # 配置structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer() if log_format == "json" else structlog.dev.ConsoleRenderer(),
        ],
        context_class=dict,
        logger_factory=LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # 配置标准库logging
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[]
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    if log_format == "json":
        console_handler.setFormatter(JSONFormatter())
    else:
        console_handler.setFormatter(
            logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        )
    
    # 文件处理器
    if log_file:
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5
        )
        if log_format == "json":
            file_handler.setFormatter(JSONFormatter())
        else:
            file_handler.setFormatter(
                logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
            )
        logging.getLogger().addHandler(file_handler)
    
    # 应用级别日志文件
    app_log_file = log_dir / "edusynapse.log"
    app_handler = logging.handlers.RotatingFileHandler(
        app_log_file,
        maxBytes=50 * 1024 * 1024,  # 50MB
        backupCount=10
    )
    app_handler.setFormatter(JSONFormatter())
    app_handler.setLevel(logging.INFO)
    
    # AI交互日志文件
    ai_log_file = log_dir / "ai_interactions.log"
    ai_handler = logging.handlers.RotatingFileHandler(
        ai_log_file,
        maxBytes=20 * 1024 * 1024,  # 20MB
        backupCount=5
    )
    ai_handler.setFormatter(JSONFormatter())
    ai_handler.addFilter(lambda record: hasattr(record, 'extra_fields') and 
                        record.extra_fields.get('event_type') == 'ai_interaction')
    
    # 错误日志文件
    error_log_file = log_dir / "errors.log"
    error_handler = logging.handlers.RotatingFileHandler(
        error_log_file,
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5
    )
    error_handler.setFormatter(JSONFormatter())
    error_handler.setLevel(logging.ERROR)
    
    # 添加处理器到根日志器
    root_logger = logging.getLogger()
    root_logger.addHandler(console_handler)
    root_logger.addHandler(app_handler)
    root_logger.addHandler(ai_handler)
    root_logger.addHandler(error_handler)
    
    # 设置第三方库日志级别
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("fastapi").setLevel(logging.INFO)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("openai").setLevel(logging.WARNING)
    logging.getLogger("langchain").setLevel(logging.INFO)


def get_logger(name: str) -> StructuredLogger:
    """获取结构化日志器"""
    return StructuredLogger(name)


class LoggingMiddleware:
    """日志中间件"""
    
    def __init__(self, app):
        self.app = app
        self.logger = get_logger("middleware")
    
    async def __call__(self, scope, receive, send):
        """处理请求日志"""
        if scope["type"] == "http":
            start_time = datetime.now()
            
            # 记录请求开始
            self.logger.info(
                "Request started",
                method=scope["method"],
                path=scope["path"],
                query_string=scope.get("query_string", b"").decode(),
                client=scope.get("client"),
                event_type="request_start"
            )
            
            # 包装send函数以捕获响应
            async def send_wrapper(message):
                if message["type"] == "http.response.start":
                    status_code = message["status"]
                    processing_time = (datetime.now() - start_time).total_seconds()
                    
                    # 记录请求完成
                    self.logger.info(
                        "Request completed",
                        method=scope["method"],
                        path=scope["path"],
                        status_code=status_code,
                        processing_time=processing_time,
                        event_type="request_complete"
                    )
                
                await send(message)
            
            await self.app(scope, receive, send_wrapper)
        else:
            await self.app(scope, receive, send)


class PerformanceLogger:
    """性能日志器"""
    
    def __init__(self, name: str):
        self.logger = get_logger(name)
    
    def log_function_performance(self, 
                                func_name: str, 
                                execution_time: float,
                                memory_usage: Optional[float] = None,
                                **kwargs):
        """记录函数性能"""
        log_data = {
            "function": func_name,
            "execution_time": execution_time,
            "event_type": "performance"
        }
        
        if memory_usage is not None:
            log_data["memory_usage"] = memory_usage
        
        log_data.update(kwargs)
        
        if execution_time > 5.0:  # 超过5秒的慢查询
            self.logger.warning("Slow function execution", **log_data)
        else:
            self.logger.info("Function execution completed", **log_data)
    
    def log_ai_model_performance(self, 
                                model_name: str,
                                prompt_tokens: int,
                                completion_tokens: int,
                                response_time: float,
                                cost: Optional[float] = None):
        """记录AI模型性能"""
        self.logger.info(
            "AI model performance",
            model=model_name,
            prompt_tokens=prompt_tokens,
            completion_tokens=completion_tokens,
            total_tokens=prompt_tokens + completion_tokens,
            response_time=response_time,
            cost=cost,
            event_type="ai_performance"
        )


def performance_monitor(func):
    """性能监控装饰器"""
    import functools
    import time
    
    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_time = time.time()
        logger = PerformanceLogger(func.__module__)
        
        try:
            result = await func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.log_function_performance(func.__name__, execution_time)
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.logger.error(
                "Function execution failed",
                function=func.__name__,
                execution_time=execution_time,
                error=str(e),
                event_type="performance_error"
            )
            raise
    
    @functools.wraps(func)
    def sync_wrapper(*args, **kwargs):
        start_time = time.time()
        logger = PerformanceLogger(func.__module__)
        
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.log_function_performance(func.__name__, execution_time)
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.logger.error(
                "Function execution failed",
                function=func.__name__,
                execution_time=execution_time,
                error=str(e),
                event_type="performance_error"
            )
            raise
    
    # 根据函数类型返回相应的包装器
    import asyncio
    if asyncio.iscoroutinefunction(func):
        return async_wrapper
    else:
        return sync_wrapper
