# 调试运行脚本

Write-Host "🔍 EduSynapse MAUI 调试启动脚本" -ForegroundColor Cyan

# 清理之前的日志
$logPath = "$env:LOCALAPPDATA\Packages\*EduSynapse*\LocalState\error.log"
if (Test-Path $logPath) {
    Remove-Item $logPath -Force
    Write-Host "✅ 清理了之前的错误日志" -ForegroundColor Green
}

# 构建项目
Write-Host "🔨 构建项目..." -ForegroundColor Yellow
$buildResult = dotnet build --verbosity quiet 2>&1

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 构建失败:" -ForegroundColor Red
    Write-Host $buildResult
    exit 1
}

Write-Host "✅ 构建成功" -ForegroundColor Green

# 启动应用
Write-Host "🚀 启动应用..." -ForegroundColor Yellow
Write-Host "💡 如果出现 Just-In-Time Debugger 对话框:" -ForegroundColor Cyan
Write-Host "   1. 点击 'No' 或 'Cancel'" -ForegroundColor White
Write-Host "   2. 检查控制台输出的错误信息" -ForegroundColor White
Write-Host "   3. 查看错误日志文件" -ForegroundColor White

# 使用 Start-Process 启动，这样可以捕获输出
$process = Start-Process -FilePath ".\bin\Debug\net8.0-windows10.0.19041.0\win10-x64\EduSynapse.MAUI.exe" -PassThru -WindowStyle Normal

# 等待一段时间
Start-Sleep -Seconds 5

# 检查进程状态
if ($process.HasExited) {
    Write-Host "❌ 应用已退出，退出代码: $($process.ExitCode)" -ForegroundColor Red
    
    # 检查错误日志
    $logPath = "$env:LOCALAPPDATA\Packages\*EduSynapse*\LocalState\error.log"
    if (Test-Path $logPath) {
        Write-Host "📋 错误日志内容:" -ForegroundColor Yellow
        Get-Content $logPath | Write-Host
    }
} else {
    Write-Host "✅ 应用正在运行 (PID: $($process.Id))" -ForegroundColor Green
    Write-Host "💡 如果应用窗口没有显示，请检查任务栏" -ForegroundColor Cyan
}

Write-Host "`n🔍 Debug Info:" -ForegroundColor Cyan
Write-Host "  - App Data Directory: $env:LOCALAPPDATA\Packages\*EduSynapse*\LocalState\" -ForegroundColor White
Write-Host "  - Error Log: error.log" -ForegroundColor White
Write-Host "  - Debug Output: Check Visual Studio Output Window or Console" -ForegroundColor White
