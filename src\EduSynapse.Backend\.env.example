# EduSynapse Backend Environment Variables

# Application Settings
DEBUG=True
SECRET_KEY=your-secret-key-here
HOST=0.0.0.0
PORT=8000

# Database Configuration
DATABASE_URL=sqlite:///./edusynapse.db

# OpenAI API Configuration
OPENAI_API_BASE=https://api.openai.com/v1
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000
OPENAI_TEMPERATURE=0.7

# Anthropic Claude API Configuration (Optional)
ANTHROPIC_API_KEY=your-anthropic-api-key-here
ANTHROPIC_MODEL=claude-3-sonnet-20240229

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/edusynapse.log

# CORS Settings
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5000,http://localhost:8080

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Cache Settings
CACHE_TTL=3600
