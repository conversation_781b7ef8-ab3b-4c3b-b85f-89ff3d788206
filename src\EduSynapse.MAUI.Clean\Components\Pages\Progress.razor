@page "/progress"
@page "/progress/{PlanId:int}"
@using EduSynapse.MAUI.ViewModels
@using EduSynapse.MAUI.Models
@using MudBlazor
@using Microsoft.AspNetCore.Components
@inject ProgressViewModel ViewModel
@inject NavigationManager Navigation

<PageTitle>学习进度 - EduSynapse</PageTitle>

<div class="fade-in">
    <!-- 页面标题 -->
    <div class="d-flex justify-space-between align-center mb-6">
        <MudText Typo="Typo.h4">
            <MudIcon Icon="@Icons.Material.Filled.TrendingUp" Class="mr-2" />
            学习进度跟踪
        </MudText>
        
        @if (ViewModel.CurrentPlan != null)
        {
            <MudChip T="string" Color="Color.Primary" Variant="Variant.Filled">
                @ViewModel.CurrentPlan.Topic
            </MudChip>
        }
    </div>

    @if (ViewModel.CurrentPlan == null)
    {
        <!-- 无计划提�?-->
        <MudCard Elevation="2" Class="pa-8 text-center">
            <MudIcon Icon="@Icons.Material.Filled.School" Size="Size.Large" Color="Color.Secondary" Class="mb-4" />
            <MudText Typo="Typo.h6" Color="Color.Secondary" Class="mb-2">请先选择学习计划</MudText>
            <MudText Typo="Typo.body2" Color="Color.Secondary" Class="mb-4">
                选择一个学习计划来开始记录您的学习进度
            </MudText>
            <MudButton Variant="Variant.Filled" 
                       Color="Color.Primary"
                       StartIcon="@Icons.Material.Filled.List"
                       OnClick="@(() => Navigation.NavigateTo("/plans"))">
                选择学习计划
            </MudButton>
        </MudCard>
    }
    else
    {
        <!-- 进度概览 -->
        @if (ViewModel.ProgressStats != null)
        {
            <MudGrid Class="mb-6">
                <MudItem xs="12" sm="6" md="3">
                    <MudCard Elevation="2" Class="pa-4 text-center">
                        <MudProgressCircular Color="Color.Primary" 
                                             Size="Size.Large" 
                                             Value="@ViewModel.ProgressStats.CompletionPercentage" 
                                             Class="mb-2">
                            <div class="progress-text">@ViewModel.ProgressStats.CompletionPercentage.ToString("F0")%</div>
                        </MudProgressCircular>
                        <MudText Typo="Typo.body2" Color="Color.Secondary">完成进度</MudText>
                        <MudText Typo="Typo.caption" Color="Color.Secondary">
                            @ViewModel.ProgressStats.CompletedDays / @ViewModel.ProgressStats.TotalDays �?                        </MudText>
                    </MudCard>
                </MudItem>
                
                <MudItem xs="12" sm="6" md="3">
                    <MudCard Elevation="2" Class="pa-4 text-center">
                        <MudText Typo="Typo.h4" Color="Color.Success" Class="mb-2">
                            @ViewModel.ProgressStats.AverageMastery.Overall.ToString("F1")%
                        </MudText>
                        <MudText Typo="Typo.body2" Color="Color.Secondary">总体掌握度</MudText>
                        <div class="d-flex justify-center mt-2">
                            <MudChip T="string" Size="Size.Small" Color="Color.Info" Class="mx-1">
                                W: @ViewModel.ProgressStats.AverageMastery.What.ToString("F0")%
                            </MudChip>
                            <MudChip T="string" Size="Size.Small" Color="Color.Warning" Class="mx-1">
                                W: @ViewModel.ProgressStats.AverageMastery.Why.ToString("F0")%
                            </MudChip>
                            <MudChip T="string" Size="Size.Small" Color="Color.Success" Class="mx-1">
                                H: @ViewModel.ProgressStats.AverageMastery.How.ToString("F0")%
                            </MudChip>
                        </div>
                    </MudCard>
                </MudItem>
                
                <MudItem xs="12" sm="6" md="3">
                    <MudCard Elevation="2" Class="pa-4 text-center">
                        <MudIcon Icon="@Icons.Material.Filled.AccessTime" Size="Size.Large" Color="Color.Info" Class="mb-2" />
                        <MudText Typo="Typo.h5" Color="Color.Info">@TimeSpan.FromMinutes(ViewModel.ProgressStats.TotalTimeSpent).ToString(@"hh\:mm")</MudText>
                        <MudText Typo="Typo.body2" Color="Color.Secondary">总学习时长</MudText>
                        <MudText Typo="Typo.caption" Color="Color.Secondary">
                            平均 @ViewModel.ProgressStats.AverageDailyTime.ToString("F0") 分钟/天
                        </MudText>
                    </MudCard>
                </MudItem>
                
                <MudItem xs="12" sm="6" md="3">
                    <MudCard Elevation="2" Class="pa-4 text-center">
                        <MudIcon Icon="@Icons.Material.Filled.LocalFireDepartment" Size="Size.Large" Color="Color.Error" Class="mb-2" />
                        <MudText Typo="Typo.h4" Color="Color.Error">@ViewModel.ProgressStats.StreakDays</MudText>
                        <MudText Typo="Typo.body2" Color="Color.Secondary">连续学习天数</MudText>
                        @if (ViewModel.ProgressStats.LastStudyDate.HasValue)
                        {
                            <MudText Typo="Typo.caption" Color="Color.Secondary">
                                最后学习: @ViewModel.ProgressStats.LastStudyDate.Value.ToString("MM/dd")
                            </MudText>
                        }
                    </MudCard>
                </MudItem>
            </MudGrid>
        }

        <!-- 主要内容区域 -->
        <MudTabs Elevation="2" Rounded="true" ApplyEffectsToContainer="true" PanelClass="pa-6">
            <!-- 记录进度 -->
            <MudTabPanel Text="记录进度" Icon="@Icons.Material.Filled.Edit">
                <ProgressRecordForm />
            </MudTabPanel>
            
            <!-- 进度历史 -->
            <MudTabPanel Text="进度历史" Icon="@Icons.Material.Filled.History">
                <ProgressHistory />
            </MudTabPanel>
            
            <!-- 学习日历 -->
            <MudTabPanel Text="学习日历" Icon="@Icons.Material.Filled.CalendarMonth">
                <ProgressCalendar />
            </MudTabPanel>
            
            <!-- 学习分析 -->
            <MudTabPanel Text="学习分析" Icon="@Icons.Material.Filled.Analytics">
                <ProgressAnalysis />
            </MudTabPanel>
        </MudTabs>
    }
</div>

@code {
    [Parameter] public int? PlanId { get; set; }

    protected override async Task OnInitializedAsync()
    {
        if (PlanId.HasValue)
        {
            // 如果URL中指定了计划ID，加载对应计�?            // 这里需要实现加载特定计划的逻辑
        }
        
        await ViewModel.InitializeAsync();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (PlanId.HasValue && (ViewModel.CurrentPlan?.Id != PlanId.Value))
        {
            // 参数变化时重新加�?            await ViewModel.InitializeAsync();
        }
    }
}
