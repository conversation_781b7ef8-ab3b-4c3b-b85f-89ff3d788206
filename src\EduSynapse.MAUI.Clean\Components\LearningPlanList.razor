@page "/learning-plan-list"
@using EduSynapse.MAUI.Services
@using EduSynapse.MAUI.Models
@using Microsoft.AspNetCore.Components
@inject StorageService StorageService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation

<div class="container-fluid p-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="text-primary mb-2">📚 我的学习计划</h1>
                    <p class="text-muted">管理您的所有学习计划</p>
                </div>
                <button class="btn btn-primary" @onclick="CreateNewPlan">
                    ➕ 创建新计划
                </button>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@totalPlans</h4>
                            <p class="mb-0">总计划数</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-book fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@activePlans</h4>
                            <p class="mb-0">进行中</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-play fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@pausedPlans</h4>
                            <p class="mb-0">已暂停</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-pause fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">@completedPlans</h4>
                            <p class="mb-0">已完成</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">状态筛选</label>
                            <select @bind="selectedStatus" @bind:after="FilterPlans" class="form-select">
                                <option value="">全部状态</option>
                                <option value="active">进行中</option>
                                <option value="paused">已暂停</option>
                                <option value="completed">已完成</option>
                                <option value="cancelled">已取消</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">难度筛选</label>
                            <select @bind="selectedDifficulty" @bind:after="FilterPlans" class="form-select">
                                <option value="">全部难度</option>
                                <option value="easy">初级</option>
                                <option value="medium">中级</option>
                                <option value="hard">高级</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">搜索</label>
                            <div class="input-group">
                                <input @bind="searchText"
                                       @oninput="OnSearchInput"
                                       @onkeypress="OnSearchKeyPress"
                                       class="form-control"
                                       placeholder="搜索学习主题..." />
                                <button class="btn btn-outline-secondary" type="button" @onclick="FilterPlans">
                                    🔍
                                </button>
                                @if (!string.IsNullOrEmpty(searchText))
                                {
                                    <button class="btn btn-outline-danger" type="button" @onclick="ClearSearch">
                                        ✖️
                                    </button>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 学习计划列表 -->
    <div class="row">
        <div class="col-12">
            @if (isLoading)
            {
                <div class="text-center p-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载学习计划...</p>
                </div>
            }
            else if (filteredPlans == null || !filteredPlans.Any())
            {
                <div class="card">
                    <div class="card-body text-center p-5">
                        <i class="fas fa-book-open fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">@(allPlans?.Any() == true ? "没有找到匹配的学习计划" : "还没有创建任何学习计划")</h5>
                        <p class="text-muted">@(allPlans?.Any() == true ? "尝试调整筛选条件" : "点击上方按钮创建您的第一个学习计划")</p>
                        @if (allPlans?.Any() != true)
                        {
                            <button class="btn btn-primary mt-3" @onclick="CreateNewPlan">
                                ➕ 创建学习计划
                            </button>
                        }
                    </div>
                </div>
            }
            else
            {
                <div class="row">
                    @foreach (var plan in filteredPlans)
                    {
                        <div class="col-lg-6 col-xl-4 mb-4">
                            <div class="card h-100 @GetPlanCardClass(plan.Status)">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0 text-truncate" title="@plan.Topic">@plan.Topic</h6>
                                    <span class="badge @GetStatusBadgeClass(plan.Status)">
                                        @plan.StatusDisplayName
                                    </span>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <small class="text-muted">学习周期</small>
                                            <div class="fw-bold">@plan.DurationDays 天</div>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">每日时间</small>
                                            <div class="fw-bold">@plan.TargetHoursPerDay 小时</div>
                                        </div>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <small class="text-muted">难度级别</small>
                                            <div>
                                                <span class="badge @GetDifficultyBadgeClass(plan.DifficultyLevel)">
                                                    @plan.DifficultyDisplayName
                                                </span>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">创建时间</small>
                                            <div class="small">@plan.CreatedAt.ToString("MM-dd")</div>
                                        </div>
                                    </div>

                                    @if (!string.IsNullOrEmpty(plan.Description))
                                    {
                                        <div class="mb-3">
                                            <small class="text-muted">学习目标</small>
                                            <p class="small text-truncate" title="@plan.Description">@plan.Description</p>
                                        </div>
                                    }

                                    <!-- 进度条 (模拟) -->
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between align-items-center mb-1">
                                            <small class="text-muted">学习进度</small>
                                            <small class="text-muted">@GetProgressPercentage(plan)%</small>
                                        </div>
                                        <div class="progress" style="height: 6px;">
                                            <div class="progress-bar @GetProgressBarClass(plan.Status)" 
                                                 role="progressbar" 
                                                 style="width: @GetProgressPercentage(plan)%">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer bg-transparent">
                                    <div class="btn-group w-100" role="group">
                                        @if (plan.Status == "active")
                                        {
                                            <button class="btn btn-sm btn-primary" @onclick="() => StartLearning(plan)">
                                                🚀 开始学习
                                            </button>
                                            <button class="btn btn-sm btn-outline-info" @onclick="() => ViewProgress(plan)">
                                                📊 查看进度
                                            </button>
                                        }
                                        else if (plan.Status == "paused")
                                        {
                                            <button class="btn btn-sm btn-success" @onclick="() => ResumePlan(plan)">
                                                ▶️ 继续
                                            </button>
                                            <button class="btn btn-sm btn-outline-primary" @onclick="() => EditPlan(plan)">
                                                ✏️ 编辑
                                            </button>
                                        }
                                        else if (plan.Status == "completed")
                                        {
                                            <button class="btn btn-sm btn-outline-info" @onclick="() => ViewProgress(plan)">
                                                📊 查看进度
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" @onclick="() => ArchivePlan(plan)">
                                                📦 归档
                                            </button>
                                        }
                                        else
                                        {
                                            <button class="btn btn-sm btn-outline-primary" @onclick="() => EditPlan(plan)">
                                                ✏️ 编辑
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" @onclick="() => DeletePlan(plan)">
                                                🗑️ 删除
                                            </button>
                                        }

                                        <!-- 查看详情按钮 - 所有状态都显示 -->
                                        <button class="btn btn-sm btn-outline-secondary" @onclick="() => ViewPlanDetail(plan)">
                                            📋 查看详情
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
        </div>
    </div>

    <!-- 学习进度记录模态框 -->
    @if (showProgressModal && selectedPlanForProgress != null)
    {
        <div class="modal fade show" style="display: block; background-color: rgba(0,0,0,0.5);" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <div>
                            <h5 class="modal-title">📝 记录学习进度 - @selectedPlanForProgress.Topic</h5>
                            <small class="text-muted">📅 @DateTime.Today.ToString("yyyy年MM月dd日 dddd")</small>
                        </div>
                        <button type="button" class="btn-close" @onclick="CloseProgressModal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info mb-3">
                            <strong>📝 今日学习记录</strong><br/>
                            记录您在 <strong>@DateTime.Today.ToString("MM月dd日")</strong> 的学习情况。如果今天已有记录，新的记录将覆盖之前的数据。
                        </div>
                        <form @onsubmit="SaveProgressRecord" @onsubmit:preventDefault="true">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">学习时间（小时）*</label>
                                        <input @bind="todayRecord.StudyHours"
                                               type="number"
                                               class="form-control"
                                               min="0"
                                               max="24"
                                               step="0.5"
                                               required />
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">学习状态</label>
                                        <select @bind="todayRecord.Status" class="form-select">
                                            <option value="completed">✅ 已完成</option>
                                            <option value="partial">⚠️ 部分完成</option>
                                            <option value="skipped">❌ 未学习</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">学习内容</label>
                                        <input @bind="todayRecord.Content"
                                               type="text"
                                               class="form-control"
                                               placeholder="今天学习了什么内容..." />
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">心情评分（1-10）</label>
                                        <input @bind="todayRecord.MoodScore"
                                               type="number"
                                               class="form-control"
                                               min="1"
                                               max="10" />
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">学习笔记</label>
                                <textarea @bind="todayRecord.Notes"
                                          class="form-control"
                                          rows="3"
                                          placeholder="记录今天的学习心得、遇到的问题或收获..."></textarea>
                            </div>
                        </form>

                        <!-- 显示最近的学习记录 -->
                        @if (recentProgressRecords != null && recentProgressRecords.Any())
                        {
                            <hr />
                            <h6>📊 最近学习记录</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>日期</th>
                                            <th>时间</th>
                                            <th>状态</th>
                                            <th>内容</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var record in recentProgressRecords.Take(5))
                                        {
                                            <tr class="@(record.Date.Date == DateTime.Today ? "table-warning" : "")">
                                                <td>@record.Date.ToString("MM-dd")</td>
                                                <td>@record.StudyHours.ToString("F1")h</td>
                                                <td>
                                                    <span class="badge @GetProgressStatusBadgeClass(record.Status)">
                                                        @GetProgressStatusDisplayName(record.Status)
                                                    </span>
                                                </td>
                                                <td class="text-truncate" style="max-width: 150px;" title="@record.Content">
                                                    @record.Content
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" @onclick="CloseProgressModal">
                            取消
                        </button>
                        <button type="button" class="btn btn-primary" @onclick="SaveProgressRecord" disabled="@isSavingProgress">
                            @if (isSavingProgress)
                            {
                                <span class="spinner-border spinner-border-sm me-2"></span>
                                <span>保存中...</span>
                            }
                            else
                            {
                                <span>💾 保存记录</span>
                            }
                        </button>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- 操作结果提示 -->
    @if (!string.IsNullOrEmpty(resultMessage))
    {
        <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1050;">
            <div class="toast show" role="alert">
                <div class="toast-header">
                    <strong class="me-auto">@(isSuccess ? "✅ 成功" : "❌ 错误")</strong>
                    <button type="button" class="btn-close" @onclick="ClearResult"></button>
                </div>
                <div class="toast-body">
                    @resultMessage
                </div>
            </div>
        </div>
    }
</div>

@code {
    private List<LearningPlan>? allPlans = null;
    private List<LearningPlan>? filteredPlans = null;
    private bool isLoading = true;
    private bool isSuccess = false;
    private string resultMessage = "";

    // 筛选条件
    private string selectedStatus = "";
    private string selectedDifficulty = "";
    private string searchText = "";

    // 统计数据
    private int totalPlans => allPlans?.Count ?? 0;
    private int activePlans => allPlans?.Count(p => p.Status == "active") ?? 0;
    private int pausedPlans => allPlans?.Count(p => p.Status == "paused") ?? 0;
    private int completedPlans => allPlans?.Count(p => p.Status == "completed") ?? 0;

    // 进度记录相关
    private bool showProgressModal = false;
    private LearningPlan? selectedPlanForProgress = null;
    private ProgressRecord todayRecord = new();
    private bool isSavingProgress = false;
    private List<ProgressRecord>? recentProgressRecords = null;

    protected override async Task OnInitializedAsync()
    {
        await LoadPlans();
    }

    private async Task LoadPlans()
    {
        isLoading = true;
        try
        {
            allPlans = await StorageService.LoadAsync<List<LearningPlan>>("learning_plans") ?? new List<LearningPlan>();
            FilterPlans();
            System.Diagnostics.Debug.WriteLine($"✅ 加载学习计划: {allPlans.Count} 个");
        }
        catch (Exception ex)
        {
            allPlans = new List<LearningPlan>();
            filteredPlans = new List<LearningPlan>();
            ShowMessage($"加载学习计划失败: {ex.Message}", false);
            System.Diagnostics.Debug.WriteLine($"❌ 加载学习计划失败: {ex}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private void FilterPlans()
    {
        if (allPlans == null)
        {
            filteredPlans = new List<LearningPlan>();
            return;
        }

        filteredPlans = allPlans.Where(plan =>
        {
            // 状态筛选
            if (!string.IsNullOrEmpty(selectedStatus) && plan.Status != selectedStatus)
                return false;

            // 难度筛选
            if (!string.IsNullOrEmpty(selectedDifficulty) && plan.DifficultyLevel != selectedDifficulty)
                return false;

            // 搜索文本
            if (!string.IsNullOrEmpty(searchText) &&
                !plan.Topic.Contains(searchText, StringComparison.OrdinalIgnoreCase) &&
                !(plan.Description?.Contains(searchText, StringComparison.OrdinalIgnoreCase) ?? false))
                return false;

            return true;
        }).OrderByDescending(p => p.CreatedAt).ToList();
    }

    private async Task OnSearchInput(ChangeEventArgs e)
    {
        searchText = e.Value?.ToString() ?? "";

        // 延迟搜索，避免频繁触发
        await Task.Delay(300);
        FilterPlans();
    }

    private async Task OnSearchKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            FilterPlans();
        }
    }

    private void ClearSearch()
    {
        searchText = "";
        FilterPlans();
    }

    private async Task SavePlans()
    {
        try
        {
            await StorageService.SaveAsync("learning_plans", allPlans);
        }
        catch (Exception ex)
        {
            ShowMessage($"保存学习计划失败: {ex.Message}", false);
        }
    }

    private void CreateNewPlan()
    {
        try
        {
            Navigation.NavigateTo("/learning-plan-creator");
        }
        catch (Exception ex)
        {
            ShowMessage($"导航失败: {ex.Message}", false);
        }
    }

    private async Task StartLearning(LearningPlan plan)
    {
        selectedPlanForProgress = plan;
        await LoadProgressRecords(plan);
        InitializeProgressRecord(plan);
        showProgressModal = true;
        ShowMessage($"开始记录学习进度: {plan.Topic}", true);
    }

    private async Task PausePlan(LearningPlan plan)
    {
        plan.Status = "paused";
        plan.UpdatedAt = DateTime.Now;
        await SavePlans();
        FilterPlans();
        ShowMessage($"已暂停学习计划: {plan.Topic}", true);
    }

    private async Task ResumePlan(LearningPlan plan)
    {
        plan.Status = "active";
        plan.UpdatedAt = DateTime.Now;
        await SavePlans();
        FilterPlans();
        ShowMessage($"已恢复学习计划: {plan.Topic}", true);
    }

    private void EditPlan(LearningPlan plan)
    {
        // TODO: 导航到编辑页面
        ShowMessage($"编辑功能开发中: {plan.Topic}", true);
    }

    private async Task ViewProgress(LearningPlan plan)
    {
        try
        {
            var storageKey = $"progress_records_{plan.Id}";
            var progressRecords = await StorageService.LoadAsync<List<ProgressRecord>>(storageKey) ?? new List<ProgressRecord>();

            if (progressRecords.Any())
            {
                var totalHours = progressRecords.Sum(r => r.StudyHours);
                var totalDays = progressRecords.Count;
                var avgHours = totalDays > 0 ? totalHours / totalDays : 0;

                ShowMessage($"📊 {plan.Topic} 学习统计：共 {totalDays} 天，累计 {totalHours:F1} 小时，平均每天 {avgHours:F1} 小时", true);
            }
            else
            {
                ShowMessage($"📊 {plan.Topic} 还没有学习记录", true);
            }
        }
        catch (Exception ex)
        {
            ShowMessage($"查看进度失败: {ex.Message}", false);
        }
    }

    private void ViewPlanDetail(LearningPlan plan)
    {
        try
        {
            Navigation.NavigateTo($"/learning-plan-detail/{plan.Id}");
        }
        catch (Exception ex)
        {
            ShowMessage($"导航到详情页面失败: {ex.Message}", false);
        }
    }

    private async Task ArchivePlan(LearningPlan plan)
    {
        plan.Status = "archived";
        plan.UpdatedAt = DateTime.Now;
        await SavePlans();
        FilterPlans();
        ShowMessage($"已归档学习计划: {plan.Topic}", true);
    }

    private async Task DeletePlan(LearningPlan plan)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", $"确定要删除学习计划 '{plan.Topic}' 吗？"))
        {
            allPlans?.Remove(plan);
            await SavePlans();
            FilterPlans();
            ShowMessage($"已删除学习计划: {plan.Topic}", true);
        }
    }

    private void ShowMessage(string message, bool success)
    {
        resultMessage = message;
        isSuccess = success;

        // 3秒后自动清除消息
        Task.Delay(3000).ContinueWith(_ =>
        {
            resultMessage = "";
            InvokeAsync(StateHasChanged);
        });
    }

    private void ClearResult()
    {
        resultMessage = "";
    }

    // UI 辅助方法
    private string GetPlanCardClass(string status)
    {
        return status switch
        {
            "active" => "border-primary",
            "paused" => "border-warning",
            "completed" => "border-success",
            "cancelled" => "border-secondary",
            _ => ""
        };
    }

    private string GetStatusBadgeClass(string status)
    {
        return status switch
        {
            "active" => "bg-primary",
            "paused" => "bg-warning",
            "completed" => "bg-success",
            "cancelled" => "bg-secondary",
            _ => "bg-secondary"
        };
    }

    private string GetDifficultyBadgeClass(string difficulty)
    {
        return difficulty switch
        {
            "easy" => "bg-success",
            "medium" => "bg-warning",
            "hard" => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private string GetProgressBarClass(string status)
    {
        return status switch
        {
            "active" => "bg-primary",
            "paused" => "bg-warning",
            "completed" => "bg-success",
            _ => "bg-secondary"
        };
    }

    private int GetProgressPercentage(LearningPlan plan)
    {
        // 模拟进度计算 - 基于创建时间和当前时间
        if (plan.Status == "completed") return 100;
        if (plan.Status == "cancelled") return 0;

        var daysPassed = (DateTime.Now - plan.CreatedAt).Days;
        var progress = Math.Min((daysPassed * 100) / plan.DurationDays, 95);
        return Math.Max(progress, 5); // 最少显示5%
    }

    // 进度记录相关方法
    private async Task LoadProgressRecords(LearningPlan plan)
    {
        try
        {
            var storageKey = $"progress_records_{plan.Id}";
            recentProgressRecords = await StorageService.LoadAsync<List<ProgressRecord>>(storageKey) ?? new List<ProgressRecord>();
            recentProgressRecords = recentProgressRecords.OrderByDescending(r => r.Date).ToList();

            System.Diagnostics.Debug.WriteLine($"✅ 加载进度记录: {recentProgressRecords.Count} 条");
        }
        catch (Exception ex)
        {
            recentProgressRecords = new List<ProgressRecord>();
            System.Diagnostics.Debug.WriteLine($"❌ 加载进度记录失败: {ex}");
        }
    }

    private void InitializeProgressRecord(LearningPlan plan)
    {
        // 检查今天是否已有记录
        var todayExisting = recentProgressRecords?.FirstOrDefault(r => r.Date.Date == DateTime.Today);

        if (todayExisting != null)
        {
            // 如果今天已有记录，加载现有数据
            todayRecord = new ProgressRecord
            {
                Date = todayExisting.Date,
                StudyHours = todayExisting.StudyHours,
                Status = todayExisting.Status,
                Content = todayExisting.Content,
                MoodScore = todayExisting.MoodScore,
                Notes = todayExisting.Notes,
                PlanId = plan.Id
            };
        }
        else
        {
            // 创建新的今日记录
            todayRecord = new ProgressRecord
            {
                Date = DateTime.Today,
                StudyHours = 0,
                Status = "completed",
                Content = "",
                MoodScore = 8,
                Notes = "",
                PlanId = plan.Id
            };
        }
    }

    private async Task SaveProgressRecord()
    {
        if (selectedPlanForProgress == null) return;

        isSavingProgress = true;
        try
        {
            todayRecord.Date = DateTime.Today;
            todayRecord.PlanId = selectedPlanForProgress.Id;

            // 加载现有进度记录
            var storageKey = $"progress_records_{selectedPlanForProgress.Id}";
            var progressRecords = await StorageService.LoadAsync<List<ProgressRecord>>(storageKey) ?? new List<ProgressRecord>();

            // 移除今天的旧记录（如果存在）
            progressRecords.RemoveAll(r => r.Date.Date == DateTime.Today);

            // 添加新记录
            progressRecords.Add(todayRecord);

            // 保存到存储
            await StorageService.SaveAsync(storageKey, progressRecords);

            showProgressModal = false;
            ShowMessage($"✅ 今日学习记录保存成功！学习时间: {todayRecord.StudyHours}小时，状态: {GetProgressStatusDisplayName(todayRecord.Status)}", true);
            System.Diagnostics.Debug.WriteLine($"✅ 保存学习记录: {todayRecord.StudyHours}小时，内容: {todayRecord.Content}");
        }
        catch (Exception ex)
        {
            ShowMessage($"保存记录失败: {ex.Message}", false);
            System.Diagnostics.Debug.WriteLine($"❌ 保存记录失败: {ex}");
        }
        finally
        {
            isSavingProgress = false;
        }
    }

    private void CloseProgressModal()
    {
        showProgressModal = false;
        selectedPlanForProgress = null;
        recentProgressRecords = null;
    }

    private string GetProgressStatusBadgeClass(string status)
    {
        return status switch
        {
            "completed" => "bg-success",
            "partial" => "bg-warning",
            "skipped" => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private string GetProgressStatusDisplayName(string status)
    {
        return status switch
        {
            "completed" => "已完成",
            "partial" => "部分完成",
            "skipped" => "未学习",
            _ => "未知"
        };
    }

    // 进度记录数据模型
    public class ProgressRecord
    {
        public DateTime Date { get; set; }
        public double StudyHours { get; set; }
        public string Status { get; set; } = "completed";
        public string Content { get; set; } = "";
        public int MoodScore { get; set; } = 8;
        public string Notes { get; set; } = "";
        public int PlanId { get; set; }
    }
}
