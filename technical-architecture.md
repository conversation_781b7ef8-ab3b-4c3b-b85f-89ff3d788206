# EduSynapse 技术架构设计文档

## 📋 文档信息
| 项目 | EduSynapse 智能学习系统 |
|------|------------------------|
| 版本 | 2.0 |
| 日期 | 2025-07-06 |
| 作者 | 技术负责人 |
| 状态 | 已更新 |

**相关文档:**
- [项目需求文档](docs/project-requirements.md)
- [技术栈学习指南](technology-stack-guide.md)
- [开发规范](docs/development-standards.md)
- [团队协作指南](docs/team-collaboration.md)

**更新说明:**
- v2.0 (2025-07-06): 调整为 .NET MAUI Blazor Hybrid 主导架构
- v1.0 (2025-07-05): 初始版本

---

## 1. 整体架构概览

### 1.1 架构原则
- **单用户设计**：针对个人使用优化，无需复杂的多用户管理
- **本地优先**：核心数据存储在本地，保护隐私
- **模块化设计**：前后端分离，便于维护和扩展
- **跨平台兼容**：基于MAUI，未来可扩展到其他平台

### 1.2 技术栈选择

#### 前端技术栈
```markdown
- **框架**: .NET MAUI Blazor Hybrid
- **UI库**: MudBlazor (Material Design)
- **语言**: C# 12.0
- **运行时**: .NET 8.0
- **平台**: Windows 10/11 (主要), macOS/Linux (未来)
```

#### 后端技术栈
```markdown
- **框架**: FastAPI (Python)
- **AI框架**: LangChain + AutoGen
- **数据库**: SQLite (开发) / SQL Server Express (生产)
- **ORM**: SQLAlchemy
- **异步**: asyncio + uvicorn
```

---

## 2. 系统架构设计

### 2.1 分层架构
```mermaid
graph TB
    subgraph "前端层 (MAUI Blazor)"
        A[用户界面组件]
        B[业务逻辑服务]
        C[数据绑定层]
    end
    
    subgraph "API通信层"
        D[HTTP Client]
        E[数据传输对象]
    end
    
    subgraph "后端服务层 (FastAPI)"
        F[API路由层]
        G[业务逻辑层]
        H[AI服务层]
    end
    
    subgraph "数据层"
        I[数据访问层]
        J[SQLite/SQL Server]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
    G --> I
    I --> J
```

### 2.2 核心模块设计

#### 2.2.1 前端模块 (MAUI Blazor)
```csharp
// 项目结构
EduSynapse.MAUI/
├── Components/              // Blazor组件
│   ├── Layout/             // 布局组件
│   ├── LearningPlan/       // 学习计划相关
│   ├── Progress/           // 进度跟踪
│   └── AITeacher/          // AI教师交互
├── Services/               // 业务服务
│   ├── ApiService.cs       // API通信
│   ├── StateService.cs     // 状态管理
│   └── StorageService.cs   // 本地存储
├── Models/                 // 数据模型
├── Platforms/              // 平台特定代码
└── wwwroot/               // 静态资源
```

#### 2.2.2 后端模块 (Python FastAPI)
```python
# 项目结构
backend/
├── app/
│   ├── api/                # API路由
│   │   ├── learning_plan.py
│   │   ├── ai_teacher.py
│   │   └── progress.py
│   ├── core/               # 核心配置
│   ├── models/             # 数据模型
│   ├── services/           # 业务服务
│   │   ├── wwh_engine.py   # WWH教学引擎
│   │   ├── ai_teacher.py   # AI教师服务
│   │   └── progress_tracker.py
│   └── database/           # 数据库相关
├── requirements.txt
└── main.py
```

---

## 3. 数据库设计

### 3.1 核心数据表

#### 学习计划表 (learning_plans)
```sql
CREATE TABLE learning_plans (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    topic VARCHAR(200) NOT NULL,
    description TEXT,
    wwh_structure TEXT, -- JSON格式存储WWH框架
    duration_days INTEGER,
    difficulty_level VARCHAR(20),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 学习进度表 (learning_progress)
```sql
CREATE TABLE learning_progress (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    plan_id INTEGER,
    day_number INTEGER,
    what_mastery DECIMAL(5,2), -- What阶段掌握度
    why_mastery DECIMAL(5,2),  -- Why阶段掌握度
    how_mastery DECIMAL(5,2),  -- How阶段掌握度
    time_spent INTEGER,        -- 学习时长(分钟)
    notes TEXT,
    completed_at DATETIME,
    FOREIGN KEY (plan_id) REFERENCES learning_plans(id)
);
```

#### AI教师交互记录表 (ai_interactions)
```sql
CREATE TABLE ai_interactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    plan_id INTEGER,
    teacher_type VARCHAR(50), -- 教师类型
    question TEXT,
    answer TEXT,
    feedback_score INTEGER,   -- 用户反馈评分
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (plan_id) REFERENCES learning_plans(id)
);
```

### 3.2 数据访问层设计
```python
# models/learning_plan.py
from sqlalchemy import Column, Integer, String, Text, DateTime, Decimal
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

class LearningPlan(Base):
    __tablename__ = "learning_plans"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    topic = Column(String(200), nullable=False)
    description = Column(Text)
    wwh_structure = Column(Text)  # JSON格式
    duration_days = Column(Integer)
    difficulty_level = Column(String(20))
    created_at = Column(DateTime)
    updated_at = Column(DateTime)
```

---

## 4. AI服务架构

### 4.1 WWH教学引擎
```python
# services/wwh_engine.py
from langchain.chains import LLMChain
from langchain.prompts import PromptTemplate

class WWHEngine:
    def __init__(self):
        self.what_template = PromptTemplate(...)
        self.why_template = PromptTemplate(...)
        self.how_template = PromptTemplate(...)
    
    async def generate_plan(self, topic: str, style: str) -> dict:
        """生成WWH结构化学习计划"""
        what_content = await self._generate_what(topic)
        why_content = await self._generate_why(topic, what_content)
        how_content = await self._generate_how(topic, what_content, why_content)
        
        return {
            "what": what_content,
            "why": why_content,
            "how": how_content,
            "daily_breakdown": self._create_daily_plan(...)
        }
```

### 4.2 多AI教师系统
```python
# services/ai_teacher.py
from autogen import AssistantAgent, UserProxyAgent

class AITeacherSystem:
    def __init__(self):
        self.teachers = {
            "socratic": self._create_socratic_teacher(),
            "practical": self._create_practical_teacher(),
            "gamified": self._create_gamified_teacher()
        }
    
    def _create_socratic_teacher(self):
        return AssistantAgent(
            name="苏格拉底式教师",
            system_message="你是一位善于提问引导思考的教师...",
            llm_config={"model": "gpt-4"}
        )
```

---

## 5. 前后端通信协议

### 5.1 API接口设计
```python
# API端点定义
@app.post("/api/learning-plan/generate")
async def generate_learning_plan(request: PlanGenerationRequest):
    """生成学习计划"""
    pass

@app.get("/api/learning-plan/{plan_id}")
async def get_learning_plan(plan_id: int):
    """获取学习计划详情"""
    pass

@app.post("/api/ai-teacher/chat")
async def chat_with_teacher(request: ChatRequest):
    """与AI教师对话"""
    pass
```

### 5.2 数据传输对象
```csharp
// Models/DTOs.cs
public class PlanGenerationRequest
{
    public string Topic { get; set; }
    public string Style { get; set; }
    public int DurationDays { get; set; }
    public string DifficultyLevel { get; set; }
}

public class LearningPlanResponse
{
    public int Id { get; set; }
    public string Topic { get; set; }
    public WWHStructure Structure { get; set; }
    public List<DailyPlan> DailyPlans { get; set; }
}
```

---

## 6. 部署架构

### 6.1 开发环境
- **前端**: Visual Studio 2022 + MAUI工作负载
- **后端**: VS Code + Python扩展
- **数据库**: SQLite Browser (开发调试)

### 6.2 生产部署
- **应用打包**: MAUI发布为Windows应用包
- **后端服务**: 打包为独立可执行文件
- **数据库**: SQLite文件或SQL Server Express
- **安装程序**: 一键安装脚本，自动配置环境

---

## 7. 性能和安全考虑

### 7.1 性能优化
- **前端**: Blazor组件懒加载，虚拟化长列表
- **后端**: 异步处理，连接池，缓存机制
- **数据库**: 索引优化，查询优化

### 7.2 安全措施
- **数据加密**: 敏感数据本地加密存储
- **API安全**: JWT令牌认证（如需要）
- **代码执行**: 沙箱环境隔离用户代码

---

## 8. 扩展性设计

### 8.1 插件化架构
- AI教师可插拔设计
- 学习资源适配器模式
- 自定义评估策略

### 8.2 未来扩展方向
- 移动端适配 (Android/iOS)
- 云端同步功能
- 多用户支持
- 学习社区功能

---

## 9. 开发经验和教训

### 9.1 重大问题分析：原始版本启动失败的根本原因

#### **问题症状**
- `System.Reflection.TargetInvocationException: Exception has been thrown by the target of an invocation.`
- 应用启动时立即崩溃，无法显示任何界面

#### **根本原因分析**

1. **主要原因：SDK 配置错误**
   ```xml
   <!-- ❌ 错误配置 -->
   <Project Sdk="Microsoft.NET.Sdk">
     <PackageReference Include="Microsoft.AspNetCore.Components.WebView.Maui" Version="9.0.0" />

   <!-- ✅ 正确配置 -->
   <Project Sdk="Microsoft.NET.Sdk.Razor">
     <PackageReference Include="Microsoft.AspNetCore.Components.WebView.Maui" Version="9.0.0" />
   ```
   - **问题**：使用 `Microsoft.NET.Sdk` 而不是 `Microsoft.NET.Sdk.Razor`
   - **后果**：缺少 `StaticWebAssetsPrepareForRun` 构建目标
   - **影响**：Blazor WebView 无法正确处理静态资源

2. **次要原因：复杂依赖链冲突**
   - 同时引入 Blazor WebView + MudBlazor + HTTP 客户端 + JSON 序列化
   - 服务注册顺序问题导致依赖注入容器初始化失败
   - 复杂的组件依赖链在启动时解析失败

3. **掩盖原因：异常信息不明确**
   - `TargetInvocationException` 掩盖了真实的底层错误
   - 缺少详细的异常处理和日志记录

#### **解决策略：逐步构建方法**

**阶段化开发策略证明有效：**
1. **第一阶段**：纯 MAUI 基础功能 ✅
2. **第二阶段**：添加 Blazor WebView ✅
3. **第三阶段**：添加 MudBlazor UI 组件库
4. **第四阶段**：添加业务服务
5. **第五阶段**：迁移业务逻辑

#### **关键教训**

1. **SDK 选择至关重要**
   - `Microsoft.NET.Sdk.Razor` 是包含 Blazor 功能的必需 SDK
   - 不同 SDK 提供不同的构建目标和功能

2. **依赖添加顺序很重要**
   - 从最简单的配置开始
   - 逐步添加复杂依赖
   - 每个阶段都要验证功能正常

3. **异常处理和调试**
   - 添加详细的异常日志记录
   - 使用 Debug.WriteLine 跟踪初始化过程
   - 记录完整的异常链（包括 InnerException）

### 9.2 已知问题和解决方案

1. **TargetInvocationException 问题** ✅ 已解决
   - 问题：应用启动时出现反射调用异常
   - 根本原因：SDK 配置错误 + 复杂依赖冲突
   - 解决方案：使用正确的 Razor SDK + 逐步构建策略

2. **Blazor WebView 依赖问题** ✅ 已解决
   - 问题：`StaticWebAssetsPrepareForRun` 目标不存在
   - 根本原因：使用了错误的 SDK
   - 解决方案：切换到 `Microsoft.NET.Sdk.Razor`

### 9.3 最佳实践总结

1. **项目初始化**
   - 使用官方模板作为起点
   - 选择正确的 SDK 类型
   - 从最简单的配置开始

2. **依赖管理**
   - 逐步添加依赖包
   - 每次添加后验证功能
   - 记录每个阶段的配置

3. **调试策略**
   - 添加详细的日志记录
   - 使用异常处理包装关键操作
   - 保留每个工作版本的备份

4. **架构设计**
   - 采用分层架构
   - 使用依赖注入
   - 保持组件的松耦合
