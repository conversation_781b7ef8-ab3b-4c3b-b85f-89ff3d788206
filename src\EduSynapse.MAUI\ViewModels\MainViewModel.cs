using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using EduSynapse.MAUI.Services;
using EduSynapse.MAUI.Models;

namespace EduSynapse.MAUI.ViewModels;

/// <summary>
/// 主页面ViewModel
/// </summary>
public partial class MainViewModel : BaseViewModel
{
    [ObservableProperty]
    private bool _isApiConnected;

    [ObservableProperty]
    private string _apiStatus = "检查中...";

    [ObservableProperty]
    private string _currentPage = "dashboard";

    [ObservableProperty]
    private List<LearningPlanSummary> _recentPlans = new();

    [ObservableProperty]
    private int _totalPlans;

    [ObservableProperty]
    private int _activePlans;

    [ObservableProperty]
    private int _completedPlans;

    public MainViewModel(
        IApiService apiService,
        IStateService stateService,
        IStorageService storageService)
        : base(apiService, stateService, storageService)
    {
        Title = "EduSynapse 智能学习系统";
    }

    public override async Task InitializeAsync()
    {
        await CheckApiConnectionAsync();
        await LoadDashboardDataAsync();
    }

    /// <summary>
    /// 检查API连接状态
    /// </summary>
    [RelayCommand]
    private async Task CheckApiConnectionAsync()
    {
        try
        {
            var health = await _apiService.CheckHealthAsync();
            IsApiConnected = health != null;
            _stateService.IsApiConnected = IsApiConnected;

            if (IsApiConnected && health != null)
            {
                ApiStatus = $"已连接 - {health.AiProvider} ({health.Version})";
            }
            else
            {
                ApiStatus = "连接失败 - 请检查后端服务";
            }
        }
        catch (Exception ex)
        {
            IsApiConnected = false;
            ApiStatus = $"连接错误: {ex.Message}";
            _stateService.IsApiConnected = false;
        }
    }

    /// <summary>
    /// 加载仪表板数据
    /// </summary>
    [RelayCommand]
    private async Task LoadDashboardDataAsync()
    {
        if (!IsApiConnected) return;

        await ExecuteAsync(async () =>
        {
            // 获取学习计划列表
            var plansResponse = await _apiService.GetLearningPlansAsync(limit: 5);
            if (plansResponse != null)
            {
                RecentPlans = plansResponse.Plans;
                _stateService.LearningPlans = plansResponse.Plans;

                // 统计数据
                TotalPlans = plansResponse.Total;
                ActivePlans = plansResponse.Plans.Count(p => p.Status == "active");
                CompletedPlans = plansResponse.Plans.Count(p => p.Status == "completed");
            }
        }, "加载仪表板数据失败");
    }

    /// <summary>
    /// 导航到指定页面
    /// </summary>
    [RelayCommand]
    private void NavigateToPage(string page)
    {
        CurrentPage = page;
    }

    /// <summary>
    /// 创建新的学习计划
    /// </summary>
    [RelayCommand]
    private void CreateNewPlan()
    {
        CurrentPage = "create-plan";
    }

    /// <summary>
    /// 查看学习计划详情
    /// </summary>
    [RelayCommand]
    private async Task ViewPlanDetailsAsync(LearningPlanSummary planSummary)
    {
        if (planSummary == null) return;

        await ExecuteAsync(async () =>
        {
            var plan = await _apiService.GetLearningPlanAsync(planSummary.Id);
            if (plan != null)
            {
                _stateService.CurrentPlan = plan;
                CurrentPage = "plan-details";
            }
        }, "获取学习计划详情失败");
    }

    /// <summary>
    /// 重新启动应用
    /// </summary>
    [RelayCommand]
    private async Task RestartAppAsync()
    {
        _stateService.Reset();
        await InitializeAsync();
    }

    protected override void OnStateChanged(object? sender, EventArgs e)
    {
        base.OnStateChanged(sender, e);
        
        // 同步API连接状态
        if (IsApiConnected != _stateService.IsApiConnected)
        {
            IsApiConnected = _stateService.IsApiConnected;
        }
    }
}
