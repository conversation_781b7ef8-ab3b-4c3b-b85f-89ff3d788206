"""
最简单的FastAPI测试应用
"""

from fastapi import FastAPI
import uvicorn

print("🚀 创建FastAPI应用...")
app = FastAPI(title="Test App", version="1.0.0")


@app.get("/")
async def root():
    print("📍 根路径被访问")
    return {"message": "Hello World", "status": "working"}


@app.get("/health")
async def health():
    print("🏥 健康检查被访问")
    return {"status": "healthy"}


@app.on_event("startup")
async def startup_event():
    print("✅ 应用启动完成")


if __name__ == "__main__":
    # 动态选择可用端口
    default_port = 8000
    backup_port = 8001

    # 检查端口是否可用
    def is_port_available(port):
        import socket

        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            s.bind(("0.0.0.0", port))
            s.close()
            return True
        except OSError:
            return False

    # 选择可用端口
    port = default_port if is_port_available(default_port) else backup_port
    if port != default_port:
        print(f"⚠️ 端口 {default_port} 已被占用，使用备用端口 {port}")

    print("🔧 启动uvicorn服务器...")
    print(f"📍 地址: http://localhost:{port}")
    print(f"📚 文档: http://localhost:{port}/docs")

    uvicorn.run(app, host="0.0.0.0", port=port, log_level="info")
