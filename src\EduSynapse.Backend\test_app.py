"""
最简单的FastAPI测试应用
"""

from fastapi import FastAPI
import uvicorn

print("🚀 创建FastAPI应用...")
app = FastAPI(title="Test App", version="1.0.0")


@app.get("/")
async def root():
    print("📍 根路径被访问")
    return {"message": "Hello World", "status": "working"}


@app.get("/health")
async def health():
    print("🏥 健康检查被访问")
    return {"status": "healthy"}


@app.on_event("startup")
async def startup_event():
    print("✅ 应用启动完成")


if __name__ == "__main__":
    print("🔧 启动uvicorn服务器...")
    print("📍 地址: http://localhost:8001")
    print("📚 文档: http://localhost:8001/docs")
    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")
