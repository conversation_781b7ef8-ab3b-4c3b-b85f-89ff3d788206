# EduSynapse MAUI 编译检查脚本

Write-Host "🔍 检查 EduSynapse MAUI 项目编译状态..." -ForegroundColor Cyan

$projectRoot = $PSScriptRoot
$projectFile = Join-Path $projectRoot "EduSynapse.MAUI.csproj"

# 检查项目文件是否存在
if (-not (Test-Path $projectFile)) {
    Write-Host "❌ 项目文件不存在: $projectFile" -ForegroundColor Red
    exit 1
}

Write-Host "📁 项目文件: $projectFile" -ForegroundColor Green

# 清理项目
Write-Host "`n🧹 清理项目..." -ForegroundColor Yellow
try {
    dotnet clean $projectFile --verbosity quiet
    Write-Host "✅ 项目清理完成" -ForegroundColor Green
} catch {
    Write-Host "⚠️ 项目清理失败: $($_.Exception.Message)" -ForegroundColor Yellow
}

# 还原 NuGet 包
Write-Host "`n📦 还原 NuGet 包..." -ForegroundColor Yellow
try {
    dotnet restore $projectFile --verbosity quiet
    Write-Host "✅ NuGet 包还原完成" -ForegroundColor Green
} catch {
    Write-Host "❌ NuGet 包还原失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 编译项目
Write-Host "`n🔨 编译项目..." -ForegroundColor Yellow
$buildOutput = dotnet build $projectFile --no-restore --verbosity normal 2>&1

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ 项目编译成功!" -ForegroundColor Green
    
    # 显示编译摘要
    $buildOutput | Where-Object { $_ -match "Build succeeded|warning|error" } | ForEach-Object {
        if ($_ -match "warning") {
            Write-Host "⚠️ $_" -ForegroundColor Yellow
        } elseif ($_ -match "error") {
            Write-Host "❌ $_" -ForegroundColor Red
        } else {
            Write-Host "✅ $_" -ForegroundColor Green
        }
    }
} else {
    Write-Host "❌ 项目编译失败!" -ForegroundColor Red
    Write-Host "`n编译错误详情:" -ForegroundColor Yellow
    
    # 显示错误和警告
    $buildOutput | Where-Object { $_ -match "error|warning" } | ForEach-Object {
        if ($_ -match "error") {
            Write-Host "❌ $_" -ForegroundColor Red
        } elseif ($_ -match "warning") {
            Write-Host "⚠️ $_" -ForegroundColor Yellow
        }
    }
    
    Write-Host "`n💡 常见解决方案:" -ForegroundColor Cyan
    Write-Host "  1. 检查 Blazor 组件中的绑定语法" -ForegroundColor White
    Write-Host "  2. 确保所有 @bind-Value 使用正确的语法" -ForegroundColor White
    Write-Host "  3. 检查 MudBlazor 组件的属性名称" -ForegroundColor White
    Write-Host "  4. 验证所有必需的 using 语句" -ForegroundColor White
    Write-Host "  5. 确保 ViewModel 属性类型匹配" -ForegroundColor White
    
    exit 1
}

# 检查特定的绑定问题
Write-Host "`n🔍 检查常见绑定问题..." -ForegroundColor Yellow

$razorFiles = Get-ChildItem -Path $projectRoot -Filter "*.razor" -Recurse
$bindingIssues = @()

foreach ($file in $razorFiles) {
    $content = Get-Content $file.FullName -Raw
    
    # 检查旧的绑定语法
    if ($content -match 'bind-Value(?!=")') {
        $bindingIssues += "❌ $($file.Name): 发现 'bind-Value' 语法，应使用 '@bind-Value' 或 'Value/ValueChanged'"
    }
    
    # 检查其他常见问题
    if ($content -match '@bind-[A-Za-z]+(?!=")') {
        $matches = [regex]::Matches($content, '@bind-([A-Za-z]+)(?!=")') 
        foreach ($match in $matches) {
            if ($match.Groups[1].Value -notin @("Value", "SelectedValue", "IsVisible", "Text")) {
                $bindingIssues += "⚠️ $($file.Name): 检查绑定属性 '@bind-$($match.Groups[1].Value)'"
            }
        }
    }
}

if ($bindingIssues.Count -eq 0) {
    Write-Host "✅ 未发现绑定语法问题" -ForegroundColor Green
} else {
    Write-Host "发现潜在的绑定问题:" -ForegroundColor Yellow
    foreach ($issue in $bindingIssues) {
        Write-Host "  $issue" -ForegroundColor Yellow
    }
}

Write-Host "`n🚀 准备运行项目:" -ForegroundColor Cyan
Write-Host "  dotnet run --framework net8.0-windows10.0.19041.0" -ForegroundColor White
Write-Host "  或在 Visual Studio 中选择 'Windows (Unpackaged)' 并按 F5" -ForegroundColor White
