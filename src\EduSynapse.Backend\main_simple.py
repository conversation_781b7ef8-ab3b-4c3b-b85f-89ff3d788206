"""
EduSynapse Backend - Simplified Main Application
简化版主应用，用于测试基本功能
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from datetime import datetime
import uvicorn

# 创建FastAPI应用
app = FastAPI(
    title="EduSynapse AI Backend",
    description="基于AI的智能教学系统后端API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 根路径
@app.get("/")
async def root():
    """根路径信息"""
    return {
        "message": "🎓 欢迎使用 EduSynapse AI智能教学系统 API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health",
        "ai_status": "/api/ai/status",
        "timestamp": datetime.now().isoformat(),
        "features": [
            "🤖 AI智能教学引擎",
            "📚 WWH教学框架",
            "🎭 多AI教师协作",
            "📊 智能学习计划生成",
            "📈 学习进度跟踪",
            "🔍 智能分析"
        ],
        "status": "running"
    }

# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "backend_mode": "simplified"
    }

# 尝试导入AI教学路由
try:
    from app.api.ai_teaching import router as ai_teaching_router
    app.include_router(ai_teaching_router)
    ai_routes_loaded = True
    print("✅ AI教学路由加载成功")
except Exception as e:
    ai_routes_loaded = False
    print(f"⚠️  AI教学路由加载失败: {e}")

# 尝试导入学习计划路由
try:
    from app.api.learning_plan import router as learning_plan_router
    app.include_router(learning_plan_router)
    learning_plan_routes_loaded = True
    print("✅ 学习计划路由加载成功")
except Exception as e:
    learning_plan_routes_loaded = False
    print(f"⚠️  学习计划路由加载失败: {e}")

# 尝试导入进度路由
try:
    from app.api.progress import router as progress_router
    app.include_router(progress_router)
    progress_routes_loaded = True
    print("✅ 进度路由加载成功")
except Exception as e:
    progress_routes_loaded = False
    print(f"⚠️  进度路由加载失败: {e}")

# 路由状态端点
@app.get("/api/routes/status")
async def routes_status():
    """路由加载状态"""
    return {
        "ai_teaching_routes": ai_routes_loaded,
        "learning_plan_routes": learning_plan_routes_loaded,
        "progress_routes": progress_routes_loaded,
        "total_routes": len(app.routes),
        "available_endpoints": [
            {"path": route.path, "methods": list(route.methods)} 
            for route in app.routes 
            if hasattr(route, 'path') and hasattr(route, 'methods')
        ]
    }

if __name__ == "__main__":
    print("🚀 启动EduSynapse简化版后端服务...")
    print("📍 访问地址: http://localhost:8000")
    print("📚 API文档: http://localhost:8000/docs")
    print("🔍 路由状态: http://localhost:8000/api/routes/status")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        reload=False
    )
