@page "/learning-goals"
@using EduSynapse.MAUI.Services
@using EduSynapse.MAUI.Models
@inject StorageService StorageService
@inject IJSRuntime JSRuntime

<div class="container-fluid p-4">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-3">🎯 学习目标</h2>
            <p class="text-muted">设定和管理您的学习目标，让学习更有方向</p>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center p-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载目标数据...</p>
        </div>
    }
    else
    {
        <!-- 目标概览 -->
        <div class="row mb-4">
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h3>@totalGoals</h3>
                        <p class="mb-0">总目标数</p>
                        <small>@activeGoals 个进行中</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h3>@completedGoals</h3>
                        <p class="mb-0">已完成</p>
                        <small>完成率 @GetCompletionRate().ToString("F0")%</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h3>@GetAverageProgress().ToString("F0")%</h3>
                        <p class="mb-0">平均进度</p>
                        <small>所有目标</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h3>@urgentGoals</h3>
                        <p class="mb-0">紧急目标</p>
                        <small>需要关注</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 创建新目标 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">➕ 创建新目标</h5>
                        <button class="btn btn-outline-primary btn-sm" @onclick="ToggleCreateForm">
                            @(showCreateForm ? "取消" : "新建目标")
                        </button>
                    </div>
                    @if (showCreateForm)
                    {
                        <div class="card-body">
                            <form @onsubmit="CreateGoal" @onsubmit:preventDefault="true">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">目标名称 *</label>
                                            <input @bind="newGoal.Name" type="text" class="form-control" placeholder="输入目标名称..." required />
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">目标类型</label>
                                            <select @bind="newGoal.Type" class="form-select">
                                                <option value="daily">每日目标</option>
                                                <option value="weekly">每周目标</option>
                                                <option value="monthly">每月目标</option>
                                                <option value="custom">自定义目标</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">目标数值</label>
                                            <input @bind="newGoal.TargetValue" type="number" class="form-control" min="1" step="0.5" />
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">单位</label>
                                            <select @bind="newGoal.Unit" class="form-select">
                                                <option value="hours">小时</option>
                                                <option value="days">天</option>
                                                <option value="plans">个计划</option>
                                                <option value="sessions">次学习</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">截止日期</label>
                                            <input @bind="newGoalDeadlineString" type="date" class="form-control" />
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">目标描述</label>
                                    <textarea @bind="newGoal.Description" class="form-control" rows="2" placeholder="描述您的目标..."></textarea>
                                </div>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary" disabled="@isSaving">
                                        @if (isSaving)
                                        {
                                            <span class="spinner-border spinner-border-sm me-2"></span>
                                            <span>创建中...</span>
                                        }
                                        else
                                        {
                                            <span>🎯 创建目标</span>
                                        }
                                    </button>
                                    <button type="button" class="btn btn-secondary" @onclick="ClearForm">
                                        清空表单
                                    </button>
                                </div>
                            </form>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- 目标列表 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">📋 我的目标</h5>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn @(selectedFilter == "all" ? "btn-primary" : "btn-outline-primary") btn-sm" 
                                    @onclick="() => FilterGoals(\"all\")">
                                全部
                            </button>
                            <button type="button" class="btn @(selectedFilter == "active" ? "btn-primary" : "btn-outline-primary") btn-sm" 
                                    @onclick="() => FilterGoals(\"active\")">
                                进行中
                            </button>
                            <button type="button" class="btn @(selectedFilter == "completed" ? "btn-primary" : "btn-outline-primary") btn-sm" 
                                    @onclick="() => FilterGoals(\"completed\")">
                                已完成
                            </button>
                            <button type="button" class="btn @(selectedFilter == "urgent" ? "btn-primary" : "btn-outline-primary") btn-sm" 
                                    @onclick="() => FilterGoals(\"urgent\")">
                                紧急
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        @if (filteredGoals.Any())
                        {
                            <div class="row">
                                @foreach (var goal in filteredGoals)
                                {
                                    <div class="col-lg-6 col-md-12 mb-4">
                                        <div class="card h-100 @GetGoalCardClass(goal)">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between align-items-start mb-3">
                                                    <h6 class="card-title mb-0">@goal.Name</h6>
                                                    <span class="badge @GetGoalStatusBadge(goal)">
                                                        @GetGoalStatusText(goal)
                                                    </span>
                                                </div>
                                                
                                                <p class="card-text text-muted">@goal.Description</p>
                                                
                                                <div class="goal-progress mb-3">
                                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                                        <small class="text-muted">进度</small>
                                                        <small class="text-muted">@goal.CurrentValue / @goal.TargetValue @goal.Unit</small>
                                                    </div>
                                                    <div class="progress">
                                                        <div class="progress-bar @GetProgressBarClass(goal)" 
                                                             style="width: @goal.ProgressPercentage%">
                                                            @goal.ProgressPercentage.ToString("F0")%
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="goal-info">
                                                    <div class="row text-center">
                                                        <div class="col-4">
                                                            <small class="text-muted">类型</small>
                                                            <div>@GetGoalTypeDisplayName(goal.Type)</div>
                                                        </div>
                                                        <div class="col-4">
                                                            <small class="text-muted">截止日期</small>
                                                            <div>@goal.Deadline.ToString("MM-dd")</div>
                                                        </div>
                                                        <div class="col-4">
                                                            <small class="text-muted">剩余天数</small>
                                                            <div class="@GetRemainingDaysClass(goal)">
                                                                @GetRemainingDays(goal)
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="goal-actions mt-3">
                                                    <div class="btn-group w-100" role="group">
                                                        @if (goal.Status == "active")
                                                        {
                                                            <button class="btn btn-outline-success btn-sm" @onclick="() => UpdateGoalProgress(goal)">
                                                                📈 更新进度
                                                            </button>
                                                            <button class="btn btn-outline-warning btn-sm" @onclick="() => PauseGoal(goal)">
                                                                ⏸️ 暂停
                                                            </button>
                                                        }
                                                        else if (goal.Status == "paused")
                                                        {
                                                            <button class="btn btn-outline-primary btn-sm" @onclick="() => ResumeGoal(goal)">
                                                                ▶️ 恢复
                                                            </button>
                                                        }
                                                        <button class="btn btn-outline-danger btn-sm" @onclick="() => DeleteGoal(goal)">
                                                            🗑️ 删除
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                        else
                        {
                            <div class="text-center p-4">
                                <i class="fas fa-target fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">@(selectedFilter == "all" ? "还没有设定学习目标" : $"没有{GetFilterDisplayName(selectedFilter)}的目标")</h6>
                                <p class="text-muted">@(selectedFilter == "all" ? "点击上方按钮创建您的第一个学习目标" : "尝试切换其他筛选条件")</p>
                                @if (selectedFilter == "all")
                                {
                                    <button class="btn btn-primary mt-2" @onclick="ToggleCreateForm">
                                        🎯 创建目标
                                    </button>
                                }
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- 操作结果提示 -->
    @if (!string.IsNullOrEmpty(resultMessage))
    {
        <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1050;">
            <div class="toast show" role="alert">
                <div class="toast-header">
                    <strong class="me-auto">@(isSuccess ? "✅ 成功" : "❌ 错误")</strong>
                    <button type="button" class="btn-close" @onclick="ClearResult"></button>
                </div>
                <div class="toast-body">
                    @resultMessage
                </div>
            </div>
        </div>
    }
</div>

<style>
    .card:hover {
        transform: translateY(-2px);
        transition: all 0.3s ease;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .goal-progress .progress {
        height: 8px;
    }
    
    .goal-info {
        background-color: #f8f9fa;
        border-radius: 0.375rem;
        padding: 0.75rem;
        margin: 0.75rem 0;
    }
    
    .btn-group .btn {
        font-size: 0.875rem;
    }
</style>

@code {
    private bool isLoading = false;
    private bool isSuccess = false;
    private string resultMessage = "";
    private bool isSaving = false;
    private bool showCreateForm = false;
    private string selectedFilter = "all";
    private string newGoalDeadlineString = DateTime.Today.AddDays(7).ToString("yyyy-MM-dd");

    private List<LearningGoal> allGoals = new();
    private List<LearningGoal> filteredGoals = new();
    private LearningGoal newGoal = new();

    private int totalGoals => allGoals.Count;
    private int activeGoals => allGoals.Count(g => g.Status == "active");
    private int completedGoals => allGoals.Count(g => g.Status == "completed");
    private int urgentGoals => allGoals.Count(g => g.Status == "active" && GetRemainingDays(g) <= 3);

    protected override async Task OnInitializedAsync()
    {
        await LoadGoals();
    }

    private async Task LoadGoals()
    {
        isLoading = true;
        try
        {
            allGoals = await StorageService.LoadAsync<List<LearningGoal>>("learning_goals") ?? new List<LearningGoal>();

            // 更新目标进度
            await UpdateGoalsProgress();

            FilterGoals(selectedFilter);

            System.Diagnostics.Debug.WriteLine($"✅ 加载目标: {totalGoals} 个目标");
        }
        catch (Exception ex)
        {
            ShowMessage($"加载目标失败: {ex.Message}", false);
            System.Diagnostics.Debug.WriteLine($"❌ 加载目标失败: {ex}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task UpdateGoalsProgress()
    {
        try
        {
            // 加载学习数据
            var allPlans = await StorageService.LoadAsync<List<LearningPlan>>("learning_plans") ?? new List<LearningPlan>();
            var allRecords = new List<ProgressRecord>();

            foreach (var plan in allPlans)
            {
                var storageKey = $"progress_records_{plan.Id}";
                var records = await StorageService.LoadAsync<List<ProgressRecord>>(storageKey) ?? new List<ProgressRecord>();
                allRecords.AddRange(records);
            }

            // 更新每个目标的进度
            foreach (var goal in allGoals)
            {
                var relevantRecords = GetRelevantRecords(goal, allRecords, allPlans);
                goal.CurrentValue = CalculateCurrentValue(goal, relevantRecords, allPlans);
                goal.ProgressPercentage = Math.Min(100, (goal.CurrentValue / goal.TargetValue) * 100);

                // 检查是否完成
                if (goal.CurrentValue >= goal.TargetValue && goal.Status == "active")
                {
                    goal.Status = "completed";
                    goal.CompletedDate = DateTime.Now;
                }

                // 检查是否过期
                if (goal.Deadline < DateTime.Today && goal.Status == "active")
                {
                    goal.Status = "expired";
                }
            }

            // 保存更新后的目标
            await StorageService.SaveAsync("learning_goals", allGoals);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ 更新目标进度失败: {ex}");
        }
    }

    private List<ProgressRecord> GetRelevantRecords(LearningGoal goal, List<ProgressRecord> allRecords, List<LearningPlan> allPlans)
    {
        var startDate = goal.Type switch
        {
            "daily" => DateTime.Today,
            "weekly" => DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek),
            "monthly" => new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1),
            _ => goal.CreatedDate
        };

        return allRecords.Where(r => r.Date >= startDate && r.Date <= DateTime.Today).ToList();
    }

    private double CalculateCurrentValue(LearningGoal goal, List<ProgressRecord> records, List<LearningPlan> plans)
    {
        return goal.Unit switch
        {
            "hours" => records.Sum(r => r.StudyHours),
            "days" => records.Where(r => r.StudyHours > 0).Select(r => r.Date.Date).Distinct().Count(),
            "plans" => plans.Count(p => p.CreatedAt >= GetRelevantStartDate(goal)),
            "sessions" => records.Count(r => r.StudyHours > 0),
            _ => 0
        };
    }

    private DateTime GetRelevantStartDate(LearningGoal goal)
    {
        return goal.Type switch
        {
            "daily" => DateTime.Today,
            "weekly" => DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek),
            "monthly" => new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1),
            _ => goal.CreatedDate
        };
    }

    private async Task CreateGoal()
    {
        isSaving = true;
        try
        {
            // 解析截止日期
            if (DateTime.TryParse(newGoalDeadlineString, out var deadline))
            {
                newGoal.Deadline = deadline;
            }
            else
            {
                newGoal.Deadline = DateTime.Today.AddDays(7);
            }

            newGoal.Id = allGoals.Any() ? allGoals.Max(g => g.Id) + 1 : 1;
            newGoal.CreatedDate = DateTime.Now;
            newGoal.Status = "active";
            newGoal.CurrentValue = 0;
            newGoal.ProgressPercentage = 0;

            allGoals.Add(newGoal);
            await StorageService.SaveAsync("learning_goals", allGoals);

            FilterGoals(selectedFilter);
            ClearForm();
            showCreateForm = false;

            ShowMessage($"目标 '{newGoal.Name}' 创建成功！", true);
            System.Diagnostics.Debug.WriteLine($"✅ 创建目标: {newGoal.Name}");
        }
        catch (Exception ex)
        {
            ShowMessage($"创建目标失败: {ex.Message}", false);
            System.Diagnostics.Debug.WriteLine($"❌ 创建目标失败: {ex}");
        }
        finally
        {
            isSaving = false;
        }
    }

    private void ClearForm()
    {
        newGoal = new LearningGoal();
        newGoalDeadlineString = DateTime.Today.AddDays(7).ToString("yyyy-MM-dd");
    }

    private void ToggleCreateForm()
    {
        showCreateForm = !showCreateForm;
        if (!showCreateForm)
        {
            ClearForm();
        }
    }

    private void FilterGoals(string filter)
    {
        selectedFilter = filter;

        filteredGoals = filter switch
        {
            "active" => allGoals.Where(g => g.Status == "active").ToList(),
            "completed" => allGoals.Where(g => g.Status == "completed").ToList(),
            "urgent" => allGoals.Where(g => g.Status == "active" && GetRemainingDays(g) <= 3).ToList(),
            _ => allGoals.ToList()
        };

        filteredGoals = filteredGoals.OrderByDescending(g => g.CreatedDate).ToList();
    }

    private async Task UpdateGoalProgress(LearningGoal goal)
    {
        // 这里可以打开一个模态框来手动更新进度
        // 暂时自动刷新进度
        await UpdateGoalsProgress();
        FilterGoals(selectedFilter);
        ShowMessage($"目标 '{goal.Name}' 进度已更新", true);
    }

    private async Task PauseGoal(LearningGoal goal)
    {
        goal.Status = "paused";
        await SaveGoals();
        FilterGoals(selectedFilter);
        ShowMessage($"目标 '{goal.Name}' 已暂停", true);
    }

    private async Task ResumeGoal(LearningGoal goal)
    {
        goal.Status = "active";
        await SaveGoals();
        FilterGoals(selectedFilter);
        ShowMessage($"目标 '{goal.Name}' 已恢复", true);
    }

    private async Task DeleteGoal(LearningGoal goal)
    {
        allGoals.Remove(goal);
        await SaveGoals();
        FilterGoals(selectedFilter);
        ShowMessage($"目标 '{goal.Name}' 已删除", true);
    }

    private async Task SaveGoals()
    {
        await StorageService.SaveAsync("learning_goals", allGoals);
    }

    // UI 辅助方法
    private double GetCompletionRate()
    {
        return totalGoals > 0 ? (double)completedGoals / totalGoals * 100 : 0;
    }

    private double GetAverageProgress()
    {
        return allGoals.Any() ? allGoals.Average(g => g.ProgressPercentage) : 0;
    }

    private string GetGoalCardClass(LearningGoal goal)
    {
        return goal.Status switch
        {
            "completed" => "border-success",
            "expired" => "border-danger",
            "paused" => "border-warning",
            _ when GetRemainingDays(goal) <= 3 => "border-warning",
            _ => "border-primary"
        };
    }

    private string GetGoalStatusBadge(LearningGoal goal)
    {
        return goal.Status switch
        {
            "completed" => "bg-success",
            "expired" => "bg-danger",
            "paused" => "bg-warning",
            _ when GetRemainingDays(goal) <= 3 => "bg-warning",
            _ => "bg-primary"
        };
    }

    private string GetGoalStatusText(LearningGoal goal)
    {
        return goal.Status switch
        {
            "completed" => "已完成",
            "expired" => "已过期",
            "paused" => "已暂停",
            _ when GetRemainingDays(goal) <= 3 => "紧急",
            _ => "进行中"
        };
    }

    private string GetProgressBarClass(LearningGoal goal)
    {
        return goal.ProgressPercentage switch
        {
            >= 100 => "bg-success",
            >= 75 => "bg-info",
            >= 50 => "bg-warning",
            _ => "bg-danger"
        };
    }

    private string GetGoalTypeDisplayName(string type)
    {
        return type switch
        {
            "daily" => "每日",
            "weekly" => "每周",
            "monthly" => "每月",
            "custom" => "自定义",
            _ => "未知"
        };
    }

    private int GetRemainingDays(LearningGoal goal)
    {
        return Math.Max(0, (goal.Deadline - DateTime.Today).Days);
    }

    private string GetRemainingDaysClass(LearningGoal goal)
    {
        var days = GetRemainingDays(goal);
        return days switch
        {
            <= 1 => "text-danger",
            <= 3 => "text-warning",
            _ => "text-success"
        };
    }

    private string GetFilterDisplayName(string filter)
    {
        return filter switch
        {
            "active" => "进行中",
            "completed" => "已完成",
            "urgent" => "紧急",
            _ => "全部"
        };
    }

    private void ShowMessage(string message, bool success)
    {
        resultMessage = message;
        isSuccess = success;

        Task.Delay(3000).ContinueWith(_ =>
        {
            resultMessage = "";
            InvokeAsync(StateHasChanged);
        });
    }

    private void ClearResult()
    {
        resultMessage = "";
    }

    // 数据模型
    public class LearningGoal
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public string Type { get; set; } = "weekly"; // daily, weekly, monthly, custom
        public double TargetValue { get; set; } = 1;
        public double CurrentValue { get; set; } = 0;
        public string Unit { get; set; } = "hours"; // hours, days, plans, sessions
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime Deadline { get; set; } = DateTime.Today.AddDays(7);
        public DateTime? CompletedDate { get; set; }
        public string Status { get; set; } = "active"; // active, paused, completed, expired
        public double ProgressPercentage { get; set; } = 0;
    }

    public class ProgressRecord
    {
        public DateTime Date { get; set; }
        public double StudyHours { get; set; }
        public string Status { get; set; } = "completed";
        public string Content { get; set; } = "";
        public int MoodScore { get; set; } = 8;
        public string Notes { get; set; } = "";
        public int PlanId { get; set; }
    }
}
