#!/usr/bin/env python3
"""
EduSynapse Backend 诊断脚本
逐步检查每个组件
"""

import sys
import os

print("🔍 开始诊断EduSynapse Backend...")

# 1. 检查基础导入
print("\n1️⃣ 检查基础导入...")
try:
    from fastapi import FastAPI
    print("✅ FastAPI导入成功")
except Exception as e:
    print(f"❌ FastAPI导入失败: {e}")
    sys.exit(1)

try:
    import uvicorn
    print("✅ Uvicorn导入成功")
except Exception as e:
    print(f"❌ Uvicorn导入失败: {e}")
    sys.exit(1)

# 2. 检查配置模块
print("\n2️⃣ 检查配置模块...")
try:
    from app.core.config import settings
    print("✅ 配置模块导入成功")
    print(f"   - 应用名称: {settings.app_name}")
    print(f"   - 主机: {settings.host}")
    print(f"   - 端口: {settings.port}")
except Exception as e:
    print(f"❌ 配置模块导入失败: {e}")

# 3. 检查数据库模块
print("\n3️⃣ 检查数据库模块...")
try:
    from app.database.database import get_db, create_tables
    print("✅ 数据库模块导入成功")
except Exception as e:
    print(f"❌ 数据库模块导入失败: {e}")

# 4. 检查AI教学模块
print("\n4️⃣ 检查AI教学模块...")
try:
    from app.api.ai_teaching import router as ai_teaching_router
    print("✅ AI教学模块导入成功")
except Exception as e:
    print(f"❌ AI教学模块导入失败: {e}")

# 5. 检查学习计划模块
print("\n5️⃣ 检查学习计划模块...")
try:
    from app.api.learning_plan import router as learning_plan_router
    print("✅ 学习计划模块导入成功")
except Exception as e:
    print(f"❌ 学习计划模块导入失败: {e}")

# 6. 检查进度模块
print("\n6️⃣ 检查进度模块...")
try:
    from app.api.progress import router as progress_router
    print("✅ 进度模块导入成功")
except Exception as e:
    print(f"❌ 进度模块导入失败: {e}")

# 7. 创建最小FastAPI应用
print("\n7️⃣ 创建最小FastAPI应用...")
try:
    app = FastAPI(title="诊断应用", version="1.0.0")
    
    @app.get("/")
    async def root():
        return {"message": "诊断成功", "status": "working"}
    
    @app.get("/health")
    async def health():
        return {"status": "healthy"}
    
    print("✅ FastAPI应用创建成功")
except Exception as e:
    print(f"❌ FastAPI应用创建失败: {e}")
    sys.exit(1)

# 8. 测试uvicorn启动
print("\n8️⃣ 测试uvicorn启动...")
try:
    print("🚀 启动uvicorn服务器...")
    print("📍 访问地址: http://localhost:8000")
    print("📚 API文档: http://localhost:8000/docs")
    print("🏥 健康检查: http://localhost:8000/health")
    print("=" * 50)
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info",
        access_log=True
    )
except KeyboardInterrupt:
    print("\n👋 服务器已停止")
except Exception as e:
    print(f"❌ uvicorn启动失败: {e}")
    sys.exit(1)

print("\n🎉 诊断完成！")
