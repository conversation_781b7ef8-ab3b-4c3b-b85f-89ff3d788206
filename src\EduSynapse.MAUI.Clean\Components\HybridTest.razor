<h1>🔬 混合事件测试</h1>

<div style="padding: 20px; border: 2px solid #007bff; border-radius: 8px; margin: 10px;">
    <h3>📊 Blazor 状态</h3>
    <p>Blazor 计数: @blazorCount</p>
    <p>初始化时间: @initTime</p>
    <p>最后更新: @lastUpdate</p>
    
    <h3>🔘 Blazor 事件测试</h3>
    <button @onclick="IncrementBlazor" style="background: #007bff; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 4px;">
        Blazor 按钮 (@blazorCount)
    </button>
    
    <h3>🌐 原生 JavaScript 测试</h3>
    <p>JavaScript 计数: <span id="jsCount">0</span></p>
    <button onclick="incrementJS()" style="background: #28a745; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 4px;">
        JavaScript 按钮
    </button>
    
    <h3>📝 测试日志</h3>
    <div id="testLog" style="background: #000; color: #0f0; padding: 10px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;">
        [初始化] 混合测试组件加载完成<br/>
    </div>
</div>

<script>
    let jsCount = 0;
    
    function incrementJS() {
        jsCount++;
        document.getElementById('jsCount').textContent = jsCount;
        
        const log = document.getElementById('testLog');
        const time = new Date().toLocaleTimeString();
        log.innerHTML += `[${time}] JavaScript 按钮点击，计数: ${jsCount}<br/>`;
        log.scrollTop = log.scrollHeight;
        
        console.log(`🟢 JavaScript 按钮点击成功: ${jsCount}`);
    }
    
    function logMessage(message) {
        const log = document.getElementById('testLog');
        const time = new Date().toLocaleTimeString();
        log.innerHTML += `[${time}] ${message}<br/>`;
        log.scrollTop = log.scrollHeight;
    }
    
    // 页面加载完成后的测试
    window.addEventListener('load', function() {
        logMessage('页面加载完成，JavaScript 正常工作');
        console.log('🟢 JavaScript 环境正常');
    });
</script>

@code {
    private int blazorCount = 0;
    private string initTime = "";
    private string lastUpdate = "未更新";

    protected override void OnInitialized()
    {
        initTime = DateTime.Now.ToString("HH:mm:ss.fff");
        lastUpdate = initTime;
        System.Diagnostics.Debug.WriteLine($"🎯 HybridTest 组件初始化: {initTime}");
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            System.Diagnostics.Debug.WriteLine("🎨 HybridTest 首次渲染完成");
        }
    }

    private void IncrementBlazor()
    {
        System.Diagnostics.Debug.WriteLine($"🔥 Blazor 按钮点击事件触发！当前计数: {blazorCount}");
        
        blazorCount++;
        lastUpdate = DateTime.Now.ToString("HH:mm:ss.fff");
        
        System.Diagnostics.Debug.WriteLine($"✅ Blazor 计数已更新: {blazorCount}, 时间: {lastUpdate}");
        
        StateHasChanged();
        System.Diagnostics.Debug.WriteLine($"🔄 StateHasChanged() 已调用");
    }
}
