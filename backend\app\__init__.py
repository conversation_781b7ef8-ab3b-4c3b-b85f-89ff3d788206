"""
EduSynapse AI Backend Package
智能教学系统后端包初始化
"""

__version__ = "1.0.0"
__author__ = "EduSynapse Team"
__description__ = "基于LangChain和AutoGen的智能教学系统后端"

# 导出核心组件
from .ai.teaching_engine import IntelligentTeachingEngine, WWHFrameworkEngine, MultiAgentTeachingSystem
from .ai.progress_tracker import FiveDimensionProgressTracker, FiveDimensionMetrics
from .models.learning_models import (
    LearningPlan, LearningPreferences, TeacherType, LearningStage,
    WWHFramework, DailyPlan, LearningSession, ProgressData
)
from .models.api_models import (
    GeneratePlanRequest, GeneratePlanResponse,
    StartSessionRequest, StartSessionResponse,
    ContinueSessionRequest, ContinueSessionResponse,
    TrackProgressRequest, TrackProgressResponse
)
from .core.config import settings
from .utils.logger import get_logger, setup_logging

__all__ = [
    # AI引擎
    "IntelligentTeachingEngine",
    "WWHFrameworkEngine", 
    "MultiAgentTeachingSystem",
    "FiveDimensionProgressTracker",
    
    # 数据模型
    "LearningPlan",
    "LearningPreferences", 
    "TeacherType",
    "LearningStage",
    "WWHFramework",
    "DailyPlan",
    "LearningSession",
    "ProgressData",
    "FiveDimensionMetrics",
    
    # API模型
    "GeneratePlanRequest",
    "GeneratePlanResponse",
    "StartSessionRequest", 
    "StartSessionResponse",
    "ContinueSessionRequest",
    "ContinueSessionResponse",
    "TrackProgressRequest",
    "TrackProgressResponse",
    
    # 配置和工具
    "settings",
    "get_logger",
    "setup_logging",
]
