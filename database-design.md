# EduSynapse 数据库设计文档

## 📋 文档信息
| 项目 | EduSynapse 智能学习系统 |
|------|------------------------|
| 版本 | 2.0 |
| 日期 | 2025-07-06 |
| 主数据库 | SQLite (本地存储) |
| 备选方案 | SQL Server (企业部署) |
| ORM | Entity Framework Core |

**相关文档:**
- [项目需求文档](docs/project-requirements.md)
- [技术架构文档](technical-architecture.md)
- [API 设计文档](api-design.md)

**版本更新:**
- v2.0 (2025-07-06): 明确 SQLite 为主，Entity Framework Core 为 ORM
- v1.0 (2025-07-05): 初始设计

---

## 🎯 设计原则

### 1. 设计理念
- **单用户优化**: 针对个人使用场景设计，无需复杂的用户管理
- **数据完整性**: 确保学习数据的准确性和一致性
- **性能优先**: 优化查询性能，支持快速数据检索
- **扩展性**: 预留扩展字段，支持未来功能增强

### 2. 命名规范
- **表名**: 小写字母 + 下划线，复数形式 (如: learning_plans)
- **字段名**: 小写字母 + 下划线 (如: created_at)
- **主键**: 统一使用 id 作为主键名
- **外键**: 使用 表名_id 格式 (如: plan_id)

---

## 📊 数据库概览

### ER 图
```mermaid
erDiagram
    LEARNING_PLANS ||--o{ LEARNING_PROGRESS : has
    LEARNING_PLANS ||--o{ AI_INTERACTIONS : involves
    LEARNING_PLANS ||--o{ LEARNING_RESOURCES : contains
    LEARNING_PROGRESS ||--o{ PRACTICE_RECORDS : includes
    AI_INTERACTIONS ||--o{ CHAT_MESSAGES : contains
    
    LEARNING_PLANS {
        int id PK
        string topic
        text description
        text wwh_structure
        int duration_days
        string difficulty_level
        datetime created_at
        datetime updated_at
    }
    
    LEARNING_PROGRESS {
        int id PK
        int plan_id FK
        int day_number
        decimal what_mastery
        decimal why_mastery
        decimal how_mastery
        int time_spent
        text notes
        datetime completed_at
    }
    
    AI_INTERACTIONS {
        int id PK
        int plan_id FK
        string teacher_type
        text context
        int session_duration
        int feedback_score
        datetime created_at
    }
```

---

## 📋 核心数据表设计

### 1. 学习计划表 (learning_plans)

#### 表结构
```sql
CREATE TABLE learning_plans (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    topic VARCHAR(200) NOT NULL COMMENT '学习主题',
    description TEXT COMMENT '计划描述',
    wwh_structure TEXT NOT NULL COMMENT 'WWH框架结构(JSON格式)',
    duration_days INTEGER DEFAULT 14 COMMENT '计划持续天数',
    difficulty_level VARCHAR(20) DEFAULT 'medium' COMMENT '难度级别: easy/medium/hard',
    target_hours_per_day DECIMAL(4,2) DEFAULT 2.0 COMMENT '每日目标学习时长',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态: active/completed/paused',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_topic (topic),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);
```

#### WWH结构示例 (JSON格式)
```json
{
    "what": {
        "core_concepts": ["概念A", "概念B", "概念C"],
        "key_features": ["特征1", "特征2"],
        "definitions": {"概念A": "定义内容..."}
    },
    "why": {
        "historical_context": "历史背景...",
        "practical_importance": "实用价值...",
        "learning_motivation": "学习动机..."
    },
    "how": {
        "practice_projects": ["项目1", "项目2"],
        "code_examples": ["示例1", "示例2"],
        "exercises": ["练习1", "练习2"]
    },
    "daily_breakdown": [
        {
            "day": 1,
            "focus": "what",
            "tasks": ["任务1", "任务2"],
            "estimated_hours": 2.5
        }
    ]
}
```

### 2. 学习进度表 (learning_progress)

#### 表结构
```sql
CREATE TABLE learning_progress (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    plan_id INTEGER NOT NULL,
    day_number INTEGER NOT NULL COMMENT '学习天数',
    what_mastery DECIMAL(5,2) DEFAULT 0.00 COMMENT 'What阶段掌握度(0-100)',
    why_mastery DECIMAL(5,2) DEFAULT 0.00 COMMENT 'Why阶段掌握度(0-100)',
    how_mastery DECIMAL(5,2) DEFAULT 0.00 COMMENT 'How阶段掌握度(0-100)',
    overall_mastery DECIMAL(5,2) GENERATED ALWAYS AS (
        (what_mastery + why_mastery + how_mastery) / 3
    ) STORED COMMENT '总体掌握度',
    time_spent INTEGER DEFAULT 0 COMMENT '实际学习时长(分钟)',
    focus_time INTEGER DEFAULT 0 COMMENT '专注时长(分钟)',
    break_count INTEGER DEFAULT 0 COMMENT '休息次数',
    notes TEXT COMMENT '学习笔记',
    mood_score INTEGER CHECK (mood_score >= 1 AND mood_score <= 5) COMMENT '学习心情评分(1-5)',
    difficulty_rating INTEGER CHECK (difficulty_rating >= 1 AND difficulty_rating <= 5) COMMENT '难度评价(1-5)',
    completed_at DATETIME COMMENT '完成时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (plan_id) REFERENCES learning_plans(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_plan_day (plan_id, day_number),
    INDEX idx_completed_at (completed_at),
    UNIQUE KEY uk_plan_day (plan_id, day_number)
);
```

### 3. AI教师交互表 (ai_interactions)

#### 表结构
```sql
CREATE TABLE ai_interactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    plan_id INTEGER,
    teacher_type VARCHAR(50) NOT NULL COMMENT '教师类型: socratic/practical/gamified',
    session_topic VARCHAR(200) COMMENT '对话主题',
    context TEXT COMMENT '对话上下文',
    session_duration INTEGER DEFAULT 0 COMMENT '对话时长(秒)',
    message_count INTEGER DEFAULT 0 COMMENT '消息数量',
    user_satisfaction INTEGER CHECK (user_satisfaction >= 1 AND user_satisfaction <= 5) COMMENT '用户满意度(1-5)',
    learning_effectiveness INTEGER CHECK (learning_effectiveness >= 1 AND learning_effectiveness <= 5) COMMENT '学习效果评价(1-5)',
    ai_model_used VARCHAR(50) COMMENT '使用的AI模型',
    tokens_consumed INTEGER DEFAULT 0 COMMENT '消耗的token数量',
    cost_usd DECIMAL(8,4) DEFAULT 0.0000 COMMENT 'API调用成本(美元)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    ended_at DATETIME COMMENT '对话结束时间',
    
    FOREIGN KEY (plan_id) REFERENCES learning_plans(id) ON DELETE SET NULL,
    
    -- 索引
    INDEX idx_plan_teacher (plan_id, teacher_type),
    INDEX idx_created_at (created_at),
    INDEX idx_teacher_type (teacher_type)
);
```

### 4. 对话消息表 (chat_messages)

#### 表结构
```sql
CREATE TABLE chat_messages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    interaction_id INTEGER NOT NULL,
    sender_type VARCHAR(10) NOT NULL CHECK (sender_type IN ('user', 'ai')) COMMENT '发送者类型',
    message_content TEXT NOT NULL COMMENT '消息内容',
    message_type VARCHAR(20) DEFAULT 'text' COMMENT '消息类型: text/code/image',
    code_language VARCHAR(20) COMMENT '代码语言(如果是代码消息)',
    is_helpful BOOLEAN COMMENT '用户标记是否有帮助',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (interaction_id) REFERENCES ai_interactions(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_interaction_time (interaction_id, created_at),
    INDEX idx_sender_type (sender_type)
);
```

### 5. 学习资源表 (learning_resources)

#### 表结构
```sql
CREATE TABLE learning_resources (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    plan_id INTEGER,
    resource_type VARCHAR(20) NOT NULL COMMENT '资源类型: pdf/video/code/article/book',
    title VARCHAR(300) NOT NULL COMMENT '资源标题',
    description TEXT COMMENT '资源描述',
    url TEXT COMMENT '资源链接',
    local_path TEXT COMMENT '本地文件路径',
    file_size INTEGER COMMENT '文件大小(字节)',
    duration_minutes INTEGER COMMENT '视频/音频时长(分钟)',
    page_count INTEGER COMMENT '文档页数',
    difficulty_level VARCHAR(20) COMMENT '难度级别',
    wwh_category VARCHAR(10) CHECK (wwh_category IN ('what', 'why', 'how')) COMMENT 'WWH分类',
    priority INTEGER DEFAULT 5 CHECK (priority >= 1 AND priority <= 10) COMMENT '优先级(1-10)',
    is_completed BOOLEAN DEFAULT FALSE COMMENT '是否已完成',
    completion_percentage DECIMAL(5,2) DEFAULT 0.00 COMMENT '完成百分比',
    user_rating INTEGER CHECK (user_rating >= 1 AND user_rating <= 5) COMMENT '用户评分(1-5)',
    notes TEXT COMMENT '用户笔记',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    accessed_at DATETIME COMMENT '最后访问时间',
    
    FOREIGN KEY (plan_id) REFERENCES learning_plans(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_plan_type (plan_id, resource_type),
    INDEX idx_wwh_category (wwh_category),
    INDEX idx_priority (priority DESC)
);
```

### 6. 实践记录表 (practice_records)

#### 表结构
```sql
CREATE TABLE practice_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    progress_id INTEGER NOT NULL,
    practice_type VARCHAR(20) NOT NULL COMMENT '实践类型: coding/quiz/project/exercise',
    title VARCHAR(200) NOT NULL COMMENT '实践标题',
    description TEXT COMMENT '实践描述',
    code_content TEXT COMMENT '代码内容',
    code_language VARCHAR(20) COMMENT '编程语言',
    expected_output TEXT COMMENT '期望输出',
    actual_output TEXT COMMENT '实际输出',
    is_successful BOOLEAN DEFAULT FALSE COMMENT '是否成功',
    error_message TEXT COMMENT '错误信息',
    execution_time_ms INTEGER COMMENT '执行时间(毫秒)',
    attempts_count INTEGER DEFAULT 1 COMMENT '尝试次数',
    hints_used INTEGER DEFAULT 0 COMMENT '使用提示次数',
    time_spent INTEGER DEFAULT 0 COMMENT '花费时间(分钟)',
    difficulty_rating INTEGER CHECK (difficulty_rating >= 1 AND difficulty_rating <= 5) COMMENT '难度评价',
    satisfaction_score INTEGER CHECK (satisfaction_score >= 1 AND satisfaction_score <= 5) COMMENT '满意度评分',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME COMMENT '完成时间',
    
    FOREIGN KEY (progress_id) REFERENCES learning_progress(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_progress_type (progress_id, practice_type),
    INDEX idx_success_time (is_successful, completed_at),
    INDEX idx_practice_type (practice_type)
);
```

---

## 🔍 查询优化

### 1. 常用查询索引
```sql
-- 学习计划查询优化
CREATE INDEX idx_plans_status_created ON learning_plans(status, created_at DESC);

-- 进度统计查询优化
CREATE INDEX idx_progress_mastery ON learning_progress(plan_id, overall_mastery DESC);

-- AI交互分析优化
CREATE INDEX idx_interactions_cost ON ai_interactions(created_at, cost_usd);

-- 实践记录分析优化
CREATE INDEX idx_practice_success_rate ON practice_records(practice_type, is_successful, created_at);
```

### 2. 常用查询示例
```sql
-- 获取当前活跃学习计划的总体进度
SELECT 
    lp.topic,
    lp.duration_days,
    COUNT(pr.id) as completed_days,
    AVG(pr.overall_mastery) as avg_mastery,
    SUM(pr.time_spent) as total_time_minutes
FROM learning_plans lp
LEFT JOIN learning_progress pr ON lp.id = pr.plan_id
WHERE lp.status = 'active'
GROUP BY lp.id;

-- 分析AI教师使用效果
SELECT 
    teacher_type,
    COUNT(*) as session_count,
    AVG(user_satisfaction) as avg_satisfaction,
    AVG(learning_effectiveness) as avg_effectiveness,
    SUM(cost_usd) as total_cost
FROM ai_interactions
WHERE created_at >= DATE('now', '-30 days')
GROUP BY teacher_type;

-- 获取学习效率统计
SELECT 
    DATE(created_at) as study_date,
    SUM(time_spent) as total_minutes,
    AVG(overall_mastery) as avg_mastery,
    COUNT(*) as sessions_count
FROM learning_progress
WHERE created_at >= DATE('now', '-7 days')
GROUP BY DATE(created_at)
ORDER BY study_date DESC;
```

---

## 🔧 数据库维护

### 1. 定期清理任务
```sql
-- 清理超过6个月的聊天消息
DELETE FROM chat_messages 
WHERE created_at < DATE('now', '-6 months');

-- 清理未完成的过期学习计划
UPDATE learning_plans 
SET status = 'expired' 
WHERE status = 'active' 
  AND created_at < DATE('now', '-90 days')
  AND id NOT IN (
      SELECT DISTINCT plan_id 
      FROM learning_progress 
      WHERE completed_at > DATE('now', '-30 days')
  );
```

### 2. 数据备份策略
```sql
-- 创建备份表
CREATE TABLE learning_plans_backup AS SELECT * FROM learning_plans;
CREATE TABLE learning_progress_backup AS SELECT * FROM learning_progress;

-- 定期备份脚本 (Python)
import sqlite3
import shutil
from datetime import datetime

def backup_database():
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_path = f'backups/edusynapse_backup_{timestamp}.db'
    shutil.copy2('edusynapse.db', backup_path)
    print(f'数据库已备份到: {backup_path}')
```

---

## 📈 性能监控

### 1. 关键性能指标
- 查询响应时间 < 100ms
- 数据库文件大小监控
- 索引使用率分析
- 慢查询日志记录

### 2. 监控查询
```sql
-- 检查表大小
SELECT 
    name as table_name,
    COUNT(*) as row_count
FROM sqlite_master 
WHERE type = 'table'
GROUP BY name;

-- 分析索引使用情况
EXPLAIN QUERY PLAN 
SELECT * FROM learning_progress 
WHERE plan_id = 1 AND day_number BETWEEN 1 AND 7;
```

这个数据库设计支持EduSynapse的所有核心功能，包括学习计划管理、进度跟踪、AI教师交互、资源管理和实践记录。设计考虑了性能优化、数据完整性和未来扩展性。
