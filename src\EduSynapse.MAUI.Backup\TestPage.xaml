<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="EduSynapse.MAUI.TestPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             Title="EduSynapse Test Page">

    <ScrollView>
        <StackLayout Padding="20" Spacing="20">
            
            <!-- 标题 -->
            <Label Text="🎓 EduSynapse - 纯 XAML 测试页面"
                   FontSize="24"
                   FontAttributes="Bold"
                   HorizontalOptions="Center"
                   TextColor="#2196F3" />
            
            <!-- 状态信息 -->
            <Frame BackgroundColor="#F5F5F5" Padding="15" CornerRadius="8">
                <StackLayout>
                    <Label Text="✅ 应用状态" FontSize="18" FontAttributes="Bold" TextColor="#333" />
                    <Label x:Name="StatusLabel" Text="应用正在运行..." FontSize="14" TextColor="#666" />
                    <Label x:Name="TimeLabel" Text="" FontSize="14" TextColor="#666" />
                </StackLayout>
            </Frame>
            
            <!-- 测试按钮 -->
            <Frame BackgroundColor="#E3F2FD" Padding="15" CornerRadius="8">
                <StackLayout>
                    <Label Text="🧪 功能测试" FontSize="18" FontAttributes="Bold" TextColor="#1976D2" />
                    
                    <Button x:Name="UpdateTimeButton"
                            Text="🔄 更新时间"
                            BackgroundColor="#2196F3"
                            TextColor="White"
                            Clicked="OnUpdateTimeClicked" />
                    
                    <Button x:Name="CounterButton"
                            Text="➕ 计数器 (0)"
                            BackgroundColor="#4CAF50"
                            TextColor="White"
                            Clicked="OnCounterClicked" />
                    
                    <Button x:Name="TestButton"
                            Text="⚠️ 测试消息"
                            BackgroundColor="#FF9800"
                            TextColor="White"
                            Clicked="OnTestClicked" />
                </StackLayout>
            </Frame>
            
            <!-- 输入测试 -->
            <Frame BackgroundColor="#FFF3E0" Padding="15" CornerRadius="8">
                <StackLayout>
                    <Label Text="📝 输入测试" FontSize="18" FontAttributes="Bold" TextColor="#F57C00" />
                    <Entry x:Name="TestEntry" 
                           Placeholder="在这里输入一些文字..."
                           TextChanged="OnEntryTextChanged" />
                    <Label x:Name="EntryLabel" Text="您输入的内容将显示在这里" FontSize="14" TextColor="#666" />
                </StackLayout>
            </Frame>
            
            <!-- 消息显示 -->
            <Frame x:Name="MessageFrame" 
                   BackgroundColor="#E8F5E8" 
                   Padding="15" 
                   CornerRadius="8"
                   IsVisible="False">
                <Label x:Name="MessageLabel" 
                       Text="" 
                       FontSize="14" 
                       TextColor="#2E7D32" />
            </Frame>
            
            <!-- 调试信息 -->
            <Frame BackgroundColor="#FAFAFA" Padding="15" CornerRadius="8" HasShadow="True">
                <StackLayout>
                    <Label Text="🔧 调试信息" FontSize="16" FontAttributes="Bold" TextColor="#666" />
                    <Label Text="如果您看到这个页面，说明 .NET MAUI 基本功能正常工作！"
                           FontSize="12" 
                           TextColor="#666" 
                           FontFamily="Courier" />
                    <Label Text="这是一个纯 XAML 页面，不依赖 Blazor 或复杂的依赖注入。"
                           FontSize="12" 
                           TextColor="#666" 
                           FontFamily="Courier" />
                </StackLayout>
            </Frame>
            
        </StackLayout>
    </ScrollView>

</ContentPage>
