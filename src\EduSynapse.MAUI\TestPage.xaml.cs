using System.Diagnostics;

namespace EduSynapse.MAUI;

public partial class TestPage : ContentPage
{
    private int _counter = 0;
    private DateTime _startTime;

    public TestPage()
    {
        try
        {
            Debug.WriteLine("TestPage: Starting initialization...");
            
            InitializeComponent();
            
            _startTime = DateTime.Now;
            UpdateTimeDisplay();
            
            StatusLabel.Text = "✅ 纯 XAML 页面初始化成功！";
            
            Debug.WriteLine("TestPage: Initialization completed successfully");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"TestPage initialization failed: {ex}");
            
            // 即使初始化失败，也尝试显示错误信息
            try
            {
                if (StatusLabel != null)
                {
                    StatusLabel.Text = $"❌ 初始化失败: {ex.Message}";
                }
            }
            catch
            {
                // 忽略二次错误
            }
        }
    }

    private void OnUpdateTimeClicked(object sender, EventArgs e)
    {
        try
        {
            UpdateTimeDisplay();
            ShowMessage("时间更新成功！");
            Debug.WriteLine("Time updated successfully");
        }
        catch (Exception ex)
        {
            ShowMessage($"更新时间失败: {ex.Message}");
            Debug.WriteLine($"Time update failed: {ex}");
        }
    }

    private void OnCounterClicked(object sender, EventArgs e)
    {
        try
        {
            _counter++;
            CounterButton.Text = $"➕ 计数器 ({_counter})";
            ShowMessage($"计数器增加到 {_counter}");
            Debug.WriteLine($"Counter incremented to {_counter}");
        }
        catch (Exception ex)
        {
            ShowMessage($"计数器错误: {ex.Message}");
            Debug.WriteLine($"Counter error: {ex}");
        }
    }

    private void OnTestClicked(object sender, EventArgs e)
    {
        try
        {
            ShowMessage("这是一个测试消息！所有功能都在正常工作。");
            Debug.WriteLine("Test message displayed");
        }
        catch (Exception ex)
        {
            ShowMessage($"测试失败: {ex.Message}");
            Debug.WriteLine($"Test failed: {ex}");
        }
    }

    private void OnEntryTextChanged(object sender, TextChangedEventArgs e)
    {
        try
        {
            if (string.IsNullOrEmpty(e.NewTextValue))
            {
                EntryLabel.Text = "您输入的内容将显示在这里";
            }
            else
            {
                EntryLabel.Text = $"您输入的内容: {e.NewTextValue}";
            }
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Entry text change error: {ex}");
        }
    }

    private void UpdateTimeDisplay()
    {
        try
        {
            var now = DateTime.Now;
            var elapsed = now - _startTime;
            
            TimeLabel.Text = $"当前时间: {now:yyyy-MM-dd HH:mm:ss}\n" +
                           $"启动时间: {_startTime:yyyy-MM-dd HH:mm:ss}\n" +
                           $"运行时长: {elapsed.TotalSeconds:F1} 秒";
        }
        catch (Exception ex)
        {
            TimeLabel.Text = $"时间显示错误: {ex.Message}";
            Debug.WriteLine($"Time display error: {ex}");
        }
    }

    private void ShowMessage(string message)
    {
        try
        {
            MessageLabel.Text = $"💬 {message}";
            MessageFrame.IsVisible = true;
            
            // 3秒后隐藏消息
            Device.StartTimer(TimeSpan.FromSeconds(3), () =>
            {
                try
                {
                    MessageFrame.IsVisible = false;
                    return false; // 停止计时器
                }
                catch
                {
                    return false;
                }
            });
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Show message error: {ex}");
        }
    }
}
