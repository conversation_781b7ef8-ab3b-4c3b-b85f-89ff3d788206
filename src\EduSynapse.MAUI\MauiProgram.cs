namespace EduSynapse.MAUI;

/// <summary>
/// 🧪 第一阶段：最纯净的 MAUI 应用程序配置
/// 目标：只包含最基本的功能，确保能正常启动
/// </summary>
public static class MauiProgram
{
    public static MauiApp CreateMauiApp()
    {
        System.Diagnostics.Debug.WriteLine("🧪 === 第一阶段：创建最纯净的 MAUI 应用 ===");

        var builder = MauiApp.CreateBuilder();
        System.Diagnostics.Debug.WriteLine("✓ MauiApp.CreateBuilder() 成功");

        // 只配置最基本的 MAUI 应用
        builder.UseMauiApp<App>();
        System.Diagnostics.Debug.WriteLine("✓ UseMauiApp<App>() 成功");

        // 只添加最基本的字体
        builder.ConfigureFonts(fonts =>
        {
            fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
        });
        System.Diagnostics.Debug.WriteLine("✓ 字体配置成功");

        // 构建应用 - 这是最关键的测试点
        System.Diagnostics.Debug.WriteLine("🔨 开始构建最纯净的应用...");
        var app = builder.Build();
        System.Diagnostics.Debug.WriteLine("🎉 第一阶段成功：最纯净的 MAUI 应用创建成功！");

        return app;
    }
}
