# EduSynapse AI Backend Docker Compose Configuration
version: '3.8'

services:
  # EduSynapse AI Backend服务
  edusynapse-backend:
    build: .
    container_name: edusynapse-ai-backend
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - HOST=0.0.0.0
      - PORT=8000
      - DEBUG=false
      - LOG_LEVEL=INFO
      
      # AI服务配置 (需要在.env文件中设置)
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_API_BASE=${OPENAI_API_BASE:-https://api.openai.com/v1}
      - OPENAI_MODEL=${OPENAI_MODEL:-gpt-4}
      
      # 数据库配置
      - DATABASE_URL=sqlite:///./data/edusynapse.db
      
      # Redis配置 (可选)
      - REDIS_URL=redis://redis:6379
      
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - edusynapse-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis缓存服务 (可选)
  redis:
    image: redis:7-alpine
    container_name: edusynapse-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - edusynapse-network
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理 (可选)
  nginx:
    image: nginx:alpine
    container_name: edusynapse-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - edusynapse-backend
    restart: unless-stopped
    networks:
      - edusynapse-network

  # Prometheus监控 (可选)
  prometheus:
    image: prom/prometheus:latest
    container_name: edusynapse-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - edusynapse-network

  # Grafana仪表板 (可选)
  grafana:
    image: grafana/grafana:latest
    container_name: edusynapse-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
    depends_on:
      - prometheus
    restart: unless-stopped
    networks:
      - edusynapse-network

# 网络配置
networks:
  edusynapse-network:
    driver: bridge

# 数据卷配置
volumes:
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
