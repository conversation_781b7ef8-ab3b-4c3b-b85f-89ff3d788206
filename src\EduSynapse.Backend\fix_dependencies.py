#!/usr/bin/env python3
"""
EduSynapse Dependencies Fix Script
修复依赖包版本冲突问题
"""

import subprocess
import sys
import os


def run_command(command, description=""):
    """运行命令并显示结果"""
    if description:
        print(f"📦 {description}")
    
    print(f"   执行: {command}")
    
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True, 
            timeout=300
        )
        
        if result.returncode == 0:
            print(f"   ✅ 成功")
            return True
        else:
            print(f"   ⚠️  警告: {result.stderr.strip()}")
            return False
    except subprocess.TimeoutExpired:
        print(f"   ⏰ 超时")
        return False
    except Exception as e:
        print(f"   ❌ 错误: {e}")
        return False


def main():
    """主修复流程"""
    print("=" * 60)
    print("🔧 EduSynapse Dependencies Fix Script")
    print("=" * 60)
    
    print(f"🐍 Python版本: {sys.version}")
    print(f"📁 工作目录: {os.getcwd()}")
    
    # 修复步骤
    steps = [
        # 1. 卸载冲突包
        {
            "command": f"{sys.executable} -m pip uninstall -y langchain langchain-openai langchain-community pyautogen",
            "description": "卸载可能冲突的AI包"
        },
        {
            "command": f"{sys.executable} -m pip uninstall -y pydantic pydantic-settings",
            "description": "卸载现有的Pydantic"
        },
        
        # 2. 安装兼容版本的Pydantic
        {
            "command": f'{sys.executable} -m pip install "pydantic>=2.0.0,<2.6.0"',
            "description": "安装兼容版本的Pydantic"
        },
        {
            "command": f'{sys.executable} -m pip install "pydantic-settings>=2.0.0,<2.6.0"',
            "description": "安装兼容版本的Pydantic Settings"
        },
        
        # 3. 安装核心Web框架
        {
            "command": f'{sys.executable} -m pip install "fastapi>=0.104.0"',
            "description": "安装FastAPI"
        },
        {
            "command": f'{sys.executable} -m pip install "uvicorn[standard]>=0.24.0"',
            "description": "安装Uvicorn服务器"
        },
        
        # 4. 安装数据库支持
        {
            "command": f'{sys.executable} -m pip install "sqlalchemy>=2.0.0"',
            "description": "安装SQLAlchemy"
        },
        {
            "command": f'{sys.executable} -m pip install "alembic>=1.13.0"',
            "description": "安装Alembic数据库迁移"
        },
        
        # 5. 安装基础工具
        {
            "command": f'{sys.executable} -m pip install "python-multipart>=0.0.6"',
            "description": "安装文件上传支持"
        },
        {
            "command": f'{sys.executable} -m pip install "python-jose[cryptography]>=3.3.0"',
            "description": "安装JWT认证"
        },
        {
            "command": f'{sys.executable} -m pip install "passlib[bcrypt]>=1.7.4"',
            "description": "安装密码加密"
        },
        {
            "command": f'{sys.executable} -m pip install "aiofiles>=23.2.0"',
            "description": "安装异步文件操作"
        },
        {
            "command": f'{sys.executable} -m pip install "python-dotenv>=1.0.0"',
            "description": "安装环境变量支持"
        },
        {
            "command": f'{sys.executable} -m pip install "httpx>=0.25.0"',
            "description": "安装HTTP客户端"
        },
        
        # 6. 安装AI服务（可选）
        {
            "command": f'{sys.executable} -m pip install "openai>=1.6.0"',
            "description": "安装OpenAI客户端（可选）"
        },
        {
            "command": f'{sys.executable} -m pip install "anthropic>=0.8.0"',
            "description": "安装Anthropic客户端（可选）"
        },
        
        # 7. 安装测试工具
        {
            "command": f'{sys.executable} -m pip install "pytest>=7.4.0"',
            "description": "安装测试框架"
        },
        {
            "command": f'{sys.executable} -m pip install "pytest-asyncio>=0.21.0"',
            "description": "安装异步测试支持"
        },
    ]
    
    # 执行修复步骤
    success_count = 0
    total_count = len(steps)
    
    for i, step in enumerate(steps, 1):
        print(f"\n[{i}/{total_count}] {step['description']}")
        success = run_command(step['command'], "")
        if success:
            success_count += 1
    
    # 显示结果
    print("\n" + "=" * 60)
    print("📊 修复结果总结")
    print("=" * 60)
    print(f"成功: {success_count}/{total_count} 个步骤")
    
    if success_count >= total_count * 0.8:
        print("✅ 修复基本成功！")
        print("\n🚀 下一步:")
        print("   1. 运行: python main.py")
        print("   2. 访问: http://localhost:8000")
        print("   3. 检查状态: http://localhost:8000/api/ai/status")
        
        print("\n💡 说明:")
        print("   - 系统将运行在备用模式")
        print("   - 基本功能完全可用")
        print("   - AI功能可稍后添加")
        
    else:
        print("⚠️  部分步骤失败，但系统可能仍可运行")
        print("请尝试手动运行: python main.py")
    
    print("\n🔍 如有问题，请检查:")
    print("   - Python版本是否为3.8+")
    print("   - 网络连接是否正常")
    print("   - pip是否为最新版本")


if __name__ == "__main__":
    main()
