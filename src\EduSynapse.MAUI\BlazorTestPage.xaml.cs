using System.Diagnostics;

namespace EduSynapse.MAUI;

public partial class BlazorTestPage : ContentPage
{
    public BlazorTestPage()
    {
        try
        {
            Debug.WriteLine("BlazorTestPage: Starting initialization...");
            
            InitializeComponent();
            
            // 添加 Blazor WebView 错误处理
            if (blazorWebView != null)
            {
                blazorWebView.BlazorWebViewInitialized += OnBlazorWebViewInitialized;
                blazorWebView.UrlLoading += OnUrlLoading;
                Debug.WriteLine("BlazorTestPage: Blazor WebView event handlers attached");
            }
            
            Debug.WriteLine("BlazorTestPage: Initialization completed successfully");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"BlazorTestPage initialization failed: {ex}");
        }
    }

    private void OnBlazorWebViewInitialized(object sender, Microsoft.AspNetCore.Components.WebView.BlazorWebViewInitializedEventArgs e)
    {
        Debug.WriteLine("BlazorTestPage: Blazor WebView initialized successfully");
    }

    private void OnUrlLoading(object sender, Microsoft.AspNetCore.Components.WebView.UrlLoadingEventArgs e)
    {
        Debug.WriteLine($"BlazorTestPage: Blazor WebView loading URL: {e.Url}");
    }
}
