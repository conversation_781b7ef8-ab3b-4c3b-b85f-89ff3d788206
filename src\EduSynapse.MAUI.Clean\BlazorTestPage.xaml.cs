using System.Diagnostics;

namespace EduSynapse.MAUI;

/// <summary>
/// EduSynapse 第二阶段：Blazor WebView 测试页面
/// </summary>
public partial class BlazorTestPage : ContentPage
{
    public BlazorTestPage()
    {
        try
        {
            Debug.WriteLine("🎓 BlazorTestPage: 开始初始化...");
            
            InitializeComponent();
            
            // 添加 Blazor WebView 事件处理
            if (blazorWebView != null)
            {
                blazorWebView.BlazorWebViewInitialized += OnBlazorWebViewInitialized;
                blazorWebView.UrlLoading += OnUrlLoading;
                Debug.WriteLine("✓ BlazorTestPage: Blazor WebView 事件处理器已附加");
            }
            
            Debug.WriteLine("🎉 BlazorTestPage: 初始化成功完成！");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"❌ BlazorTestPage 初始化失败: {ex}");
        }
    }

    private void OnBlazorWebViewInitialized(object sender, Microsoft.AspNetCore.Components.WebView.BlazorWebViewInitializedEventArgs e)
    {
        Debug.WriteLine("🎉 BlazorTestPage: Blazor WebView 初始化成功！");
    }

    private void OnUrlLoading(object sender, Microsoft.AspNetCore.Components.WebView.UrlLoadingEventArgs e)
    {
        Debug.WriteLine($"🔗 BlazorTestPage: Blazor WebView 正在加载 URL: {e.Url}");
    }
}
