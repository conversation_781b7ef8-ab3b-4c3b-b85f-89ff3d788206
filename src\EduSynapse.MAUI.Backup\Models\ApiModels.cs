using System.Text.Json.Serialization;

namespace EduSynapse.MAUI.Models;

/// <summary>
/// API统一响应格式
/// </summary>
/// <typeparam name="T">数据类型</typeparam>
public class ApiResponse<T>
{
    [JsonPropertyName("success")]
    public bool Success { get; set; }

    [JsonPropertyName("data")]
    public T? Data { get; set; }

    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    [JsonPropertyName("timestamp")]
    public DateTime Timestamp { get; set; }

    [JsonPropertyName("request_id")]
    public string? RequestId { get; set; }
}

/// <summary>
/// 学习计划生成请求
/// </summary>
public class PlanGenerationRequest
{
    [JsonPropertyName("topic")]
    public string Topic { get; set; } = string.Empty;

    [JsonPropertyName("duration_days")]
    public int DurationDays { get; set; } = 14;

    [JsonPropertyName("difficulty_level")]
    public string DifficultyLevel { get; set; } = "medium";

    [JsonPropertyName("daily_hours")]
    public double DailyHours { get; set; } = 2.0;

    [JsonPropertyName("learning_style")]
    public string LearningStyle { get; set; } = "balanced";

    [JsonPropertyName("preferences")]
    public Dictionary<string, object>? Preferences { get; set; }
}

/// <summary>
/// 学习计划摘要
/// </summary>
public class LearningPlanSummary
{
    [JsonPropertyName("id")]
    public int Id { get; set; }

    [JsonPropertyName("topic")]
    public string Topic { get; set; } = string.Empty;

    [JsonPropertyName("duration_days")]
    public int DurationDays { get; set; }

    [JsonPropertyName("difficulty_level")]
    public string DifficultyLevel { get; set; } = string.Empty;

    [JsonPropertyName("status")]
    public string Status { get; set; } = string.Empty;

    [JsonPropertyName("progress_percentage")]
    public double ProgressPercentage { get; set; }

    [JsonPropertyName("created_at")]
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// 学习计划列表响应
/// </summary>
public class LearningPlansListResponse
{
    [JsonPropertyName("plans")]
    public List<LearningPlanSummary> Plans { get; set; } = new();

    [JsonPropertyName("total")]
    public int Total { get; set; }

    [JsonPropertyName("offset")]
    public int Offset { get; set; }

    [JsonPropertyName("limit")]
    public int Limit { get; set; }
}

/// <summary>
/// 学习进度记录请求
/// </summary>
public class ProgressRecordRequest
{
    [JsonPropertyName("plan_id")]
    public int PlanId { get; set; }

    [JsonPropertyName("day_number")]
    public int DayNumber { get; set; }

    [JsonPropertyName("what_mastery")]
    public double WhatMastery { get; set; }

    [JsonPropertyName("why_mastery")]
    public double WhyMastery { get; set; }

    [JsonPropertyName("how_mastery")]
    public double HowMastery { get; set; }

    [JsonPropertyName("time_spent")]
    public int TimeSpent { get; set; }

    [JsonPropertyName("focus_time")]
    public int FocusTime { get; set; }

    [JsonPropertyName("break_count")]
    public int BreakCount { get; set; }

    [JsonPropertyName("notes")]
    public string? Notes { get; set; }

    [JsonPropertyName("mood_score")]
    public int? MoodScore { get; set; }

    [JsonPropertyName("difficulty_rating")]
    public int? DifficultyRating { get; set; }
}

/// <summary>
/// 学习进度响应
/// </summary>
public class LearningProgress
{
    [JsonPropertyName("id")]
    public int Id { get; set; }

    [JsonPropertyName("plan_id")]
    public int PlanId { get; set; }

    [JsonPropertyName("day_number")]
    public int DayNumber { get; set; }

    [JsonPropertyName("what_mastery")]
    public double WhatMastery { get; set; }

    [JsonPropertyName("why_mastery")]
    public double WhyMastery { get; set; }

    [JsonPropertyName("how_mastery")]
    public double HowMastery { get; set; }

    [JsonPropertyName("overall_mastery")]
    public double OverallMastery { get; set; }

    [JsonPropertyName("time_spent")]
    public int TimeSpent { get; set; }

    [JsonPropertyName("focus_time")]
    public int FocusTime { get; set; }

    [JsonPropertyName("break_count")]
    public int BreakCount { get; set; }

    [JsonPropertyName("notes")]
    public string? Notes { get; set; }

    [JsonPropertyName("mood_score")]
    public int? MoodScore { get; set; }

    [JsonPropertyName("difficulty_rating")]
    public int? DifficultyRating { get; set; }

    [JsonPropertyName("completed_at")]
    public DateTime? CompletedAt { get; set; }

    [JsonPropertyName("created_at")]
    public DateTime CreatedAt { get; set; }

    // 计算属性
    public string MoodDisplayName => MoodScore switch
    {
        1 => "😞 很差",
        2 => "😕 较差",
        3 => "😐 一般",
        4 => "😊 良好",
        5 => "😄 很好",
        _ => "未评分"
    };

    public string DifficultyDisplayName => DifficultyRating switch
    {
        1 => "⭐ 很简单",
        2 => "⭐⭐ 简单",
        3 => "⭐⭐⭐ 适中",
        4 => "⭐⭐⭐⭐ 困难",
        5 => "⭐⭐⭐⭐⭐ 很困难",
        _ => "未评分"
    };

    public TimeSpan TimeSpentTimeSpan => TimeSpan.FromMinutes(TimeSpent);
    public TimeSpan FocusTimeTimeSpan => TimeSpan.FromMinutes(FocusTime);
}

/// <summary>
/// 健康检查响应
/// </summary>
public class HealthCheckResponse
{
    [JsonPropertyName("status")]
    public string Status { get; set; } = string.Empty;

    [JsonPropertyName("timestamp")]
    public DateTime Timestamp { get; set; }

    [JsonPropertyName("version")]
    public string Version { get; set; } = string.Empty;

    [JsonPropertyName("database")]
    public string Database { get; set; } = string.Empty;

    [JsonPropertyName("ai_provider")]
    public string AiProvider { get; set; } = string.Empty;
}

/// <summary>
/// 错误响应
/// </summary>
public class ErrorResponse
{
    [JsonPropertyName("success")]
    public bool Success { get; set; } = false;

    [JsonPropertyName("error")]
    public ErrorDetail Error { get; set; } = new();

    [JsonPropertyName("timestamp")]
    public DateTime Timestamp { get; set; }

    [JsonPropertyName("request_id")]
    public string? RequestId { get; set; }
}

/// <summary>
/// 错误详情
/// </summary>
public class ErrorDetail
{
    [JsonPropertyName("code")]
    public string Code { get; set; } = string.Empty;

    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    [JsonPropertyName("details")]
    public List<string>? Details { get; set; }
}

/// <summary>
/// 进度日历响应
/// </summary>
public class ProgressCalendarResponse
{
    [JsonPropertyName("year")]
    public int Year { get; set; }

    [JsonPropertyName("month")]
    public int Month { get; set; }

    [JsonPropertyName("calendar_data")]
    public List<CalendarDay> CalendarData { get; set; } = new();

    [JsonPropertyName("total_days")]
    public int TotalDays { get; set; }
}

/// <summary>
/// 进度统计响应模型
/// </summary>
public class ProgressStatsResponse
{
    [JsonPropertyName("plan_id")]
    public int PlanId { get; set; }

    [JsonPropertyName("total_days")]
    public int TotalDays { get; set; }

    [JsonPropertyName("completed_days")]
    public int CompletedDays { get; set; }

    [JsonPropertyName("completion_percentage")]
    public double CompletionPercentage { get; set; }

    [JsonPropertyName("average_mastery")]
    public AverageMastery AverageMastery { get; set; } = new();

    [JsonPropertyName("total_time_spent")]
    public int TotalTimeSpent { get; set; }

    [JsonPropertyName("average_daily_time")]
    public double AverageDailyTime { get; set; }

    [JsonPropertyName("mood_trend")]
    public List<int?> MoodTrend { get; set; } = new();

    [JsonPropertyName("difficulty_trend")]
    public List<int?> DifficultyTrend { get; set; } = new();

    [JsonPropertyName("streak_days")]
    public int StreakDays { get; set; }

    [JsonPropertyName("last_study_date")]
    public DateTime? LastStudyDate { get; set; }
}

/// <summary>
/// 平均掌握度
/// </summary>
public class AverageMastery
{
    [JsonPropertyName("what")]
    public double What { get; set; }

    [JsonPropertyName("why")]
    public double Why { get; set; }

    [JsonPropertyName("how")]
    public double How { get; set; }

    [JsonPropertyName("overall")]
    public double Overall { get; set; }
}

/// <summary>
/// 日历天数据
/// </summary>
public class CalendarDay
{
    [JsonPropertyName("date")]
    public DateTime Date { get; set; }

    [JsonPropertyName("day_number")]
    public int DayNumber { get; set; }

    [JsonPropertyName("overall_mastery")]
    public double OverallMastery { get; set; }

    [JsonPropertyName("time_spent")]
    public int TimeSpent { get; set; }

    [JsonPropertyName("mood_score")]
    public int? MoodScore { get; set; }

    [JsonPropertyName("difficulty_rating")]
    public int? DifficultyRating { get; set; }

    [JsonPropertyName("has_notes")]
    public bool HasNotes { get; set; }
}

/// <summary>
/// 进度分析数据
/// </summary>
public class ProgressAnalysis
{
    [JsonPropertyName("wwh_trends")]
    public WWHTrends WwhTrends { get; set; } = new();

    [JsonPropertyName("efficiency_analysis")]
    public List<EfficiencyData> EfficiencyAnalysis { get; set; } = new();

    [JsonPropertyName("learning_patterns")]
    public LearningPatterns LearningPatterns { get; set; } = new();

    [JsonPropertyName("predictions")]
    public LearningPredictions Predictions { get; set; } = new();

    [JsonPropertyName("total_records")]
    public int TotalRecords { get; set; }
}

/// <summary>
/// WWH趋势数据
/// </summary>
public class WWHTrends
{
    [JsonPropertyName("what_trend")]
    public List<double> WhatTrend { get; set; } = new();

    [JsonPropertyName("why_trend")]
    public List<double> WhyTrend { get; set; } = new();

    [JsonPropertyName("how_trend")]
    public List<double> HowTrend { get; set; } = new();

    [JsonPropertyName("overall_trend")]
    public List<double> OverallTrend { get; set; } = new();
}

/// <summary>
/// 效率数据
/// </summary>
public class EfficiencyData
{
    [JsonPropertyName("day")]
    public int Day { get; set; }

    [JsonPropertyName("mastery_per_hour")]
    public double MasteryPerHour { get; set; }

    [JsonPropertyName("time_spent")]
    public int TimeSpent { get; set; }

    [JsonPropertyName("overall_mastery")]
    public double OverallMastery { get; set; }
}

/// <summary>
/// 学习模式
/// </summary>
public class LearningPatterns
{
    [JsonPropertyName("best_learning_hours")]
    public List<BestHour> BestLearningHours { get; set; } = new();

    [JsonPropertyName("intensity_analysis")]
    public IntensityAnalysis IntensityAnalysis { get; set; } = new();

    [JsonPropertyName("mood_performance_correlation")]
    public Dictionary<int, List<double>> MoodPerformanceCorrelation { get; set; } = new();
}

/// <summary>
/// 最佳学习时间
/// </summary>
public class BestHour
{
    [JsonPropertyName("hour")]
    public int Hour { get; set; }

    [JsonPropertyName("avg_mastery")]
    public double AvgMastery { get; set; }
}

/// <summary>
/// 强度分析
/// </summary>
public class IntensityAnalysis
{
    [JsonPropertyName("high_intensity_days")]
    public int HighIntensityDays { get; set; }

    [JsonPropertyName("medium_intensity_days")]
    public int MediumIntensityDays { get; set; }

    [JsonPropertyName("low_intensity_days")]
    public int LowIntensityDays { get; set; }
}

/// <summary>
/// 学习预测
/// </summary>
public class LearningPredictions
{
    [JsonPropertyName("estimated_completion_day")]
    public int EstimatedCompletionDay { get; set; }

    [JsonPropertyName("current_pace")]
    public string CurrentPace { get; set; } = string.Empty;

    [JsonPropertyName("suggestions")]
    public List<string> Suggestions { get; set; } = new();

    [JsonPropertyName("confidence_level")]
    public double ConfidenceLevel { get; set; }
}
