@using EduSynapse.MAUI.ViewModels
@using EduSynapse.MAUI.Models
@using MudBlazor
@inject ProgressViewModel ViewModel

<div>
    <!-- 分析操作区 -->
    <div class="d-flex justify-space-between align-center mb-4">
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.Analytics" Class="mr-2" />
            学习数据分析
        </MudText>
        
        <MudButton Variant="Variant.Filled"
                   Color="Color.Primary"
                   StartIcon="@Icons.Material.Filled.Refresh"
                   OnClick="@ViewModel.LoadAnalysisCommand.ExecuteAsync"
                   Disabled="@ViewModel.IsLoading">
            @if (ViewModel.IsLoading)
            {
                <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                <MudText Class="ms-2">分析中...</MudText>
            }
            else
            {
                <MudText>重新分析</MudText>
            }
        </MudButton>
    </div>

    @if (ViewModel.AnalysisData != null)
    {
        <!-- WWH掌握度趋势分析 -->
        <MudCard Elevation="2" Class="pa-4 mb-4">
            <MudText Typo="Typo.h6" Class="mb-4">
                <MudIcon Icon="@Icons.Material.Filled.TrendingUp" Class="mr-2" />
                WWH掌握度趋势
            </MudText>
            
            <MudGrid>
                <MudItem xs="12" md="8">
                    <!-- 趋势图 -->
                    <div class="trend-chart">
                        <div class="chart-legend">
                            <div class="legend-item">
                                <div class="legend-color what-color"></div>
                                <span>What (理论)</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color why-color"></div>
                                <span>Why (背景)</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color how-color"></div>
                                <span>How (实践)</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color overall-color"></div>
                                <span>总体</span>
                            </div>
                        </div>
                        
                        <div class="chart-container">
                            @if (ViewModel.AnalysisData.WwhTrends.WhatTrend.Any())
                            {
                                <div class="trend-chart-container">
                                    <!-- 使用 CSS 和 HTML 替代 SVG -->
                                    <div class="chart-grid">
                                        @for (int i = 0; i <= 4; i++)
                                        {
                                            var labelValue = 100 - i * 25;
                                            <div class="grid-line" style="top: @(i * 25)%">
                                                <span class="grid-label">@(labelValue)%</span>
                                            </div>
                                        }
                                    </div>

                                    <!-- 趋势线显示 -->
                                    <div class="trend-lines">
                                        <div class="trend-info">
                                            <span style="color: #2196f3;">■ What趋势</span>
                                            <span style="color: #ff9800;">■ Why趋势</span>
                                            <span style="color: #4caf50;">■ How趋势</span>
                                            <span style="color: #9c27b0;">■ 总体趋势</span>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </MudItem>
                
                <MudItem xs="12" md="4">
                    <!-- 趋势统计 -->
                    <div class="trend-stats">
                        <MudText Typo="Typo.subtitle1" Class="mb-3">趋势分析</MudText>
                        
                        @if (ViewModel.AnalysisData.WwhTrends.OverallTrend.Count >= 2)
                        {
                            var firstValue = ViewModel.AnalysisData.WwhTrends.OverallTrend.First();
                            var lastValue = ViewModel.AnalysisData.WwhTrends.OverallTrend.Last();
                            var improvement = lastValue - firstValue;
                            
                            <MudAlert Severity="@(improvement > 0 ? Severity.Success : improvement < 0 ? Severity.Warning : Severity.Info)" 
                                      Variant="Variant.Filled" Class="mb-3">
                                总体掌握度 @(improvement > 0 ? "提升" : improvement < 0 ? "下降" : "保持稳定") 
                                @Math.Abs(improvement).ToString("F1")%
                            </MudAlert>
                        }
                        
                        <!-- WWH平衡分析 -->
                        @if (ViewModel.AnalysisData.WwhTrends.WhatTrend.Any())
                        {
                            var avgWhat = ViewModel.AnalysisData.WwhTrends.WhatTrend.Average();
                            var avgWhy = ViewModel.AnalysisData.WwhTrends.WhyTrend.Average();
                            var avgHow = ViewModel.AnalysisData.WwhTrends.HowTrend.Average();
                            
                            <MudText Typo="Typo.body2" Class="mb-2">WWH平衡度:</MudText>
                            <div class="balance-bars">
                                <div class="balance-item">
                                    <span>What</span>
                                    <MudProgressLinear Color="Color.Info" Value="@avgWhat" Class="flex-1 mx-2" />
                                    <span>@avgWhat.ToString("F0")%</span>
                                </div>
                                <div class="balance-item">
                                    <span>Why</span>
                                    <MudProgressLinear Color="Color.Warning" Value="@avgWhy" Class="flex-1 mx-2" />
                                    <span>@avgWhy.ToString("F0")%</span>
                                </div>
                                <div class="balance-item">
                                    <span>How</span>
                                    <MudProgressLinear Color="Color.Success" Value="@avgHow" Class="flex-1 mx-2" />
                                    <span>@avgHow.ToString("F0")%</span>
                                </div>
                            </div>
                        }
                    </div>
                </MudItem>
            </MudGrid>
        </MudCard>

        <!-- 学习效率分析 -->
        @if (ViewModel.AnalysisData.EfficiencyAnalysis.Any())
        {
            <MudCard Elevation="2" Class="pa-4 mb-4">
                <MudText Typo="Typo.h6" Class="mb-4">
                    <MudIcon Icon="@Icons.Material.Filled.Speed" Class="mr-2" />
                    学习效率分析
                </MudText>
                
                <MudGrid>
                    @foreach (var efficiency in ViewModel.AnalysisData.EfficiencyAnalysis.Take(7))
                    {
                        <MudItem xs="12" sm="6" md="4" lg="3">
                            <MudCard Elevation="1" Class="pa-3 text-center">
                                <MudText Typo="Typo.subtitle2" Class="mb-2">第@efficiency.Day天</MudText>
                                <MudText Typo="Typo.h6" Color="Color.Primary" Class="mb-1">
                                    @efficiency.MasteryPerHour.ToString("F1")
                                </MudText>
                                <MudText Typo="Typo.caption" Color="Color.Secondary">
                                    掌握度/小时
                                </MudText>
                                <MudDivider Class="my-2" />
                                <MudText Typo="Typo.body2">
                                    @(efficiency.TimeSpent / 60.0).ToString("F1")h → @efficiency.OverallMastery.ToString("F0")%
                                </MudText>
                            </MudCard>
                        </MudItem>
                    }
                </MudGrid>
                
                <!-- 效率趋势 -->
                <div class="mt-4">
                    <MudText Typo="Typo.subtitle1" Class="mb-2">效率趋势</MudText>
                    <div class="efficiency-chart">
                        @foreach (var efficiency in ViewModel.AnalysisData.EfficiencyAnalysis)
                        {
                            var height = Math.Max(10, efficiency.MasteryPerHour / 50 * 100); // 最大50为100%
                            <div class="efficiency-bar" style="height: @height%">
                                <div class="bar-label">@efficiency.Day</div>
                                <div class="bar-value">@efficiency.MasteryPerHour.ToString("F1")</div>
                            </div>
                        }
                    </div>
                </div>
            </MudCard>
        }

        <!-- 学习模式分析 -->
        @if (ViewModel.AnalysisData.LearningPatterns != null)
        {
            <MudGrid>
                <!-- 最佳学习时间 -->
                @if (ViewModel.AnalysisData.LearningPatterns.BestLearningHours.Any())
                {
                    <MudItem xs="12" md="6">
                        <MudCard Elevation="2" Class="pa-4">
                            <MudText Typo="Typo.h6" Class="mb-4">
                                <MudIcon Icon="@Icons.Material.Filled.Schedule" Class="mr-2" />
                                最佳学习时间
                            </MudText>
                            
                            @foreach (var hour in ViewModel.AnalysisData.LearningPatterns.BestLearningHours.Take(3))
                            {
                                <div class="d-flex justify-space-between align-center mb-2">
                                    <MudText Typo="Typo.body1">@hour.Hour:00 - @(hour.Hour + 1):00</MudText>
                                    <MudChip T="string" Color="Color.Success" Size="Size.Small">
                                        @hour.AvgMastery.ToString("F1")% 平均掌握度
                                    </MudChip>
                                </div>
                            }
                            
                            <MudAlert Severity="Severity.Info" Class="mt-3">
                                建议在 @ViewModel.AnalysisData.LearningPatterns.BestLearningHours.First().Hour:00 左右安排重要学习内容
                            </MudAlert>
                        </MudCard>
                    </MudItem>
                }
                
                <!-- 学习强度分析 -->
                @if (ViewModel.AnalysisData.LearningPatterns.IntensityAnalysis != null)
                {
                    <MudItem xs="12" md="6">
                        <MudCard Elevation="2" Class="pa-4">
                            <MudText Typo="Typo.h6" Class="mb-4">
                                <MudIcon Icon="@Icons.Material.Filled.FitnessCenter" Class="mr-2" />
                                学习强度分布
                            </MudText>
                            
                            <div class="intensity-chart">
                                <div class="intensity-item">
                                    <MudIcon Icon="@Icons.Material.Filled.LocalFireDepartment" Color="Color.Error" />
                                    <span>高强度 (3h+)</span>
                                    <MudChip T="string" Color="Color.Error" Size="Size.Small">
                                        @ViewModel.AnalysisData.LearningPatterns.IntensityAnalysis.HighIntensityDays 天
                                    </MudChip>
                                </div>
                                
                                <div class="intensity-item">
                                    <MudIcon Icon="@Icons.Material.Filled.Whatshot" Color="Color.Warning" />
                                    <span>中强度 (1-3h)</span>
                                    <MudChip T="string" Color="Color.Warning" Size="Size.Small">
                                        @ViewModel.AnalysisData.LearningPatterns.IntensityAnalysis.MediumIntensityDays 天
                                    </MudChip>
                                </div>
                                
                                <div class="intensity-item">
                                    <MudIcon Icon="@Icons.Material.Filled.Nature" Color="Color.Success" />
                                    <span>轻强度 (&lt;1h)</span>
                                    <MudChip T="string" Color="Color.Success" Size="Size.Small">
                                        @ViewModel.AnalysisData.LearningPatterns.IntensityAnalysis.LowIntensityDays 天
                                    </MudChip>
                                </div>
                            </div>
                        </MudCard>
                    </MudItem>
                }
            </MudGrid>
        }

        <!-- 学习预测和建议 -->
        @if (ViewModel.AnalysisData.Predictions != null)
        {
            <MudCard Elevation="2" Class="pa-4 mt-4">
                <MudText Typo="Typo.h6" Class="mb-4">
                    <MudIcon Icon="@Icons.Material.Filled.PsychologyAlt" Class="mr-2" />
                    AI学习建议
                </MudText>
                
                <MudGrid>
                    <MudItem xs="12" md="6">
                        <MudAlert Severity="@(ViewModel.AnalysisData.Predictions.CurrentPace == "ahead" ? Severity.Success : Severity.Warning)">
                            <MudText Typo="Typo.subtitle1" Class="mb-2">学习进度预测</MudText>
                            <MudText Typo="Typo.body2">
                                预计第 @ViewModel.AnalysisData.Predictions.EstimatedCompletionDay 天完成学习计划
                            </MudText>
                            <MudText Typo="Typo.body2">
                                当前进度: @(ViewModel.AnalysisData.Predictions.CurrentPace == "ahead" ? "超前" : "落后")
                            </MudText>
                            <MudText Typo="Typo.caption">
                                置信度: @ViewModel.AnalysisData.Predictions.ConfidenceLevel.ToString("F0")%
                            </MudText>
                        </MudAlert>
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudText Typo="Typo.subtitle1" Class="mb-3">个性化建议</MudText>
                        @if (ViewModel.AnalysisData.Predictions.Suggestions.Any())
                        {
                            @foreach (var suggestion in ViewModel.AnalysisData.Predictions.Suggestions)
                            {
                                <MudChip T="string" Icon="@Icons.Material.Filled.Lightbulb"
                                         Color="Color.Info"
                                         Variant="Variant.Outlined"
                                         Class="ma-1">
                                    @suggestion
                                </MudChip>
                            }
                        }
                        else
                        {
                            <MudText Typo="Typo.body2" Color="Color.Secondary">
                                继续保持当前的学习节奏！
                            </MudText>
                        }
                    </MudItem>
                </MudGrid>
            </MudCard>
        }
    }
    else
    {
        <!-- 无分析数据 -->
        <div class="text-center pa-8">
            <MudIcon Icon="@Icons.Material.Filled.Analytics" Size="Size.Large" Color="Color.Secondary" Class="mb-4" />
            <MudText Typo="Typo.h6" Color="Color.Secondary" Class="mb-2">暂无分析数据</MudText>
            <MudText Typo="Typo.body2" Color="Color.Secondary" Class="mb-4">
                需要至少3天的学习记录才能进行深度分析
            </MudText>
            <MudButton Variant="Variant.Filled" 
                       Color="Color.Primary"
                       StartIcon="@Icons.Material.Filled.Analytics"
                       OnClick="@ViewModel.LoadAnalysisCommand.ExecuteAsync">
                开始分析
            </MudButton>
        </div>
    }
</div>

<style>
    .trend-chart {
        width: 100%;
        height: 250px;
    }
    
    .chart-legend {
        display: flex;
        justify-content: center;
        gap: 20px;
        margin-bottom: 16px;
        flex-wrap: wrap;
    }
    
    .legend-item {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
    }
    
    .legend-color {
        width: 16px;
        height: 3px;
        border-radius: 2px;
    }
    
    .what-color { background: #2196f3; }
    .why-color { background: #ff9800; }
    .how-color { background: #4caf50; }
    .overall-color { background: #9c27b0; }
    
    .chart-container {
        width: 100%;
        height: 200px;
    }
    
    .trend-svg {
        width: 100%;
        height: 100%;
    }
    
    .balance-bars {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }
    
    .balance-item {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 12px;
    }
    
    .balance-item span:first-child {
        width: 40px;
        text-align: right;
    }
    
    .balance-item span:last-child {
        width: 40px;
        text-align: left;
    }
    
    .efficiency-chart {
        display: flex;
        align-items: flex-end;
        height: 100px;
        gap: 4px;
        padding: 10px 0;
        border-bottom: 2px solid #e0e0e0;
    }
    
    .efficiency-bar {
        flex: 1;
        background: linear-gradient(to top, #1976d2, #42a5f5);
        border-radius: 4px 4px 0 0;
        position: relative;
        min-height: 20px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        color: white;
        font-size: 10px;
        padding: 2px;
    }
    
    .bar-label {
        margin-top: auto;
        font-weight: bold;
    }
    
    .bar-value {
        margin-bottom: auto;
        opacity: 0.9;
    }
    
    .intensity-chart {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }
    
    .intensity-item {
        display: flex;
        align-items: center;
        gap: 12px;
    }
    
    .intensity-item span {
        flex: 1;
    }
</style>

@code {
    // 移除了 GetTrendPoints 方法，因为我们不再使用 SVG
}
