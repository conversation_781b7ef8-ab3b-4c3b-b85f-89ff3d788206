# EduSynapse 项目技术栈快速入门手册

## 📋 目录

1. [技术栈概览](#技术栈概览)
2. [从 WinForms 到 MAUI 的转变](#从-winforms-到-maui-的转变)
3. [.NET MAUI 基础](#net-maui-基础)
4. [Blazor Hybrid 核心概念](#blazor-hybrid-核心概念)
5. [MudBlazor UI 组件库](#mudblazor-ui-组件库)
6. [项目架构和文件结构](#项目架构和文件结构)
7. [开发工作流程](#开发工作流程)
8. [常见开发任务](#常见开发任务)
9. [调试和故障排除](#调试和故障排除)
10. [学习资源和参考](#学习资源和参考)

---

## 技术栈概览

### 🎯 EduSynapse 项目技术架构

```
EduSynapse 智能学习系统
├── 🖥️ .NET MAUI (跨平台应用框架)
│   ├── 📱 支持 Windows、Android、iOS、macOS
│   └── 🔧 基于 .NET 9.0
├── 🌐 Blazor Hybrid (Web UI 技术)
│   ├── 📄 使用 HTML/CSS 构建 UI
│   ├── ⚡ C# 代码处理逻辑
│   └── 🔄 双向数据绑定
└── 🎨 MudBlazor (现代 UI 组件库)
    ├── 📦 Material Design 组件
    ├── 🎯 开箱即用的组件
    └── 📱 响应式设计
```

### 🔄 技术对比：WinForms vs MAUI Blazor

| 特性 | WinForms | MAUI Blazor |
|------|----------|-------------|
| **UI 技术** | Windows 控件 | HTML/CSS + Blazor 组件 |
| **平台支持** | 仅 Windows | Windows、Android、iOS、macOS |
| **UI 描述** | 设计器 + C# 代码 | Razor 文件 (.razor) |
| **样式** | 属性设置 | CSS 样式 |
| **数据绑定** | 手动绑定 | 自动双向绑定 |
| **事件处理** | 事件委托 | C# 方法 |

---

## 从 WinForms 到 MAUI 的转变

### 🔄 概念映射

#### WinForms → MAUI 概念对照

| WinForms 概念 | MAUI Blazor 等价概念 | 说明 |
|---------------|---------------------|------|
| `Form` | `ContentPage` | 主窗口/页面 |
| `UserControl` | `Blazor Component` | 自定义控件 |
| `Button.Click` | `@onclick="Method"` | 按钮点击事件 |
| `TextBox.Text` | `@bind="variable"` | 文本框数据绑定 |
| `DataGridView` | `MudTable` | 数据表格 |
| `MessageBox.Show()` | `ISnackbar.Add()` | 消息提示 |

#### 代码对比示例

**WinForms 代码：**
```csharp
// WinForms - 按钮点击事件
private void button1_Click(object sender, EventArgs e)
{
    textBox1.Text = "Hello World";
    MessageBox.Show("按钮被点击了！");
}
```

**MAUI Blazor 代码：**
```razor
@* Blazor - 按钮点击事件 *@
<MudButton Color="Color.Primary" @onclick="OnButtonClick">
    点击我
</MudButton>

<MudTextField @bind-Value="textValue" Label="文本输入" />

@code {
    private string textValue = "";
    
    private void OnButtonClick()
    {
        textValue = "Hello World";
        Snackbar.Add("按钮被点击了！");
    }
}
```

---

## .NET MAUI 基础

### 🏗️ MAUI 项目结构

```
EduSynapse.MAUI/
├── 📁 Platforms/          # 平台特定代码
│   ├── Android/
│   ├── iOS/
│   ├── Windows/
│   └── MacCatalyst/
├── 📁 Resources/          # 资源文件
│   ├── Images/           # 图片资源
│   ├── Fonts/            # 字体文件
│   └── Styles/           # 样式文件
├── 📁 Components/         # Blazor 组件
├── 📁 Services/          # 业务服务
├── 📁 ViewModels/        # 视图模型
├── 📁 wwwroot/           # Web 静态资源
├── 📄 App.xaml          # 应用程序配置
├── 📄 AppShell.xaml     # 应用程序外壳
└── 📄 MauiProgram.cs    # 应用程序入口
```

### 🔧 核心文件说明

#### 1. MauiProgram.cs - 应用程序配置
```csharp
public static class MauiProgram
{
    public static MauiApp CreateMauiApp()
    {
        var builder = MauiApp.CreateBuilder();
        builder
            .UseMauiApp<App>()
            .ConfigureFonts(fonts =>
            {
                fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
            });

        // 添加 Blazor WebView 支持
        builder.Services.AddMauiBlazorWebView();
        
        // 添加 MudBlazor 服务
        builder.Services.AddMudServices();

#if DEBUG
        builder.Services.AddBlazorWebViewDeveloperTools();
        builder.Logging.AddDebug();
#endif

        return builder.Build();
    }
}
```

#### 2. App.xaml.cs - 应用程序主类
```csharp
public partial class App : Application
{
    public App()
    {
        InitializeComponent();
        MainPage = new AppShell();
    }
}
```

---

## Blazor Hybrid 核心概念

### 🌐 什么是 Blazor？

**Blazor** 是微软的 Web UI 框架，允许使用 C# 而不是 JavaScript 来构建交互式 Web UI。

#### Blazor 的优势：
- ✅ **使用 C#** - 无需学习 JavaScript
- ✅ **组件化开发** - 可重用的 UI 组件
- ✅ **双向数据绑定** - 自动同步数据和 UI
- ✅ **强类型** - 编译时错误检查

### 📄 Razor 组件基础

#### 基本 Razor 组件结构：
```razor
@* 这是注释 *@
@using MudBlazor

<div class="my-component">
    <h3>@Title</h3>
    <MudButton Color="Color.Primary" @onclick="OnClick">
        @ButtonText
    </MudButton>
    <p>计数器: @counter</p>
</div>

@code {
    // 组件参数
    [Parameter] public string Title { get; set; } = "默认标题";
    [Parameter] public string ButtonText { get; set; } = "点击我";
    
    // 私有字段
    private int counter = 0;
    
    // 事件处理方法
    private void OnClick()
    {
        counter++;
        StateHasChanged(); // 通知 UI 更新
    }
    
    // 生命周期方法
    protected override void OnInitialized()
    {
        // 组件初始化时执行
    }
}
```

### 🔄 数据绑定

#### 1. 单向绑定（显示数据）
```razor
<p>用户名: @userName</p>
<p>当前时间: @DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")</p>
```

#### 2. 双向绑定（输入控件）
```razor
<MudTextField @bind-Value="userName" Label="用户名" />
<MudCheckBox @bind-Checked="isEnabled" Label="启用功能" />
<MudSelect @bind-Value="selectedOption" Label="选择选项">
    <MudSelectItem Value="@("选项1")">选项 1</MudSelectItem>
    <MudSelectItem Value="@("选项2")">选项 2</MudSelectItem>
</MudSelect>

@code {
    private string userName = "";
    private bool isEnabled = false;
    private string selectedOption = "";
}
```

#### 3. 事件绑定
```razor
<MudButton @onclick="SaveData" Color="Color.Primary">保存</MudButton>
<MudButton @onclick="() => DeleteItem(item.Id)" Color="Color.Error">删除</MudButton>

@code {
    private void SaveData()
    {
        // 保存逻辑
    }
    
    private void DeleteItem(int id)
    {
        // 删除逻辑
    }
}
```

### 🔄 组件通信

#### 1. 父组件向子组件传递数据
```razor
@* 父组件 *@
<ChildComponent Title="学生信息" StudentData="@currentStudent" />

@* 子组件 *@
@code {
    [Parameter] public string Title { get; set; }
    [Parameter] public Student StudentData { get; set; }
}
```

#### 2. 子组件向父组件传递事件
```razor
@* 子组件 *@
<MudButton @onclick="OnSave" Color="Color.Primary">保存</MudButton>

@code {
    [Parameter] public EventCallback<Student> OnStudentSaved { get; set; }
    
    private async Task OnSave()
    {
        await OnStudentSaved.InvokeAsync(currentStudent);
    }
}

@* 父组件 *@
<ChildComponent OnStudentSaved="HandleStudentSaved" />

@code {
    private void HandleStudentSaved(Student student)
    {
        // 处理保存事件
    }
}
```
