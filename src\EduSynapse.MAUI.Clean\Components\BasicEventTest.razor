@* 最基础的事件测试 - 排除所有可能的干扰因素 *@

<h1>基础事件测试</h1>

<p>计数器: @count</p>

<button @onclick="IncrementCount">点击我 +1</button>

<p>状态: @status</p>

<p>时间: @currentTime</p>

@code {
    private int count = 0;
    private string status = "等待点击";
    private string currentTime = "";

    protected override void OnInitialized()
    {
        currentTime = DateTime.Now.ToString("HH:mm:ss");
        System.Diagnostics.Debug.WriteLine("BasicEventTest 初始化完成");
    }

    private void IncrementCount()
    {
        System.Diagnostics.Debug.WriteLine("IncrementCount 被调用");
        count++;
        status = $"已点击 {count} 次";
        currentTime = DateTime.Now.ToString("HH:mm:ss");
        System.Diagnostics.Debug.WriteLine($"计数器更新为: {count}");
    }
}
