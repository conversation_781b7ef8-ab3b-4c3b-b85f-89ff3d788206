using System.Text.Json;

namespace EduSynapse.MAUI.Services;

/// <summary>
/// 本地存储服务实现
/// </summary>
public class StorageService : IStorageService
{
    private readonly string _dataDirectory;
    private readonly JsonSerializerOptions _jsonOptions;

    public StorageService()
    {
        _dataDirectory = Path.Combine(FileSystem.AppDataDirectory, "EduSynapse");
        
        // 确保数据目录存在
        if (!Directory.Exists(_dataDirectory))
        {
            Directory.CreateDirectory(_dataDirectory);
        }

        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        };
    }

    public async Task SaveAsync<T>(string key, T data)
    {
        try
        {
            var filePath = GetFilePath(key);
            var json = JsonSerializer.Serialize(data, _jsonOptions);
            await File.WriteAllTextAsync(filePath, json);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Save data failed for key '{key}': {ex.Message}");
            throw;
        }
    }

    public async Task<T?> LoadAsync<T>(string key)
    {
        try
        {
            var filePath = GetFilePath(key);
            if (!File.Exists(filePath))
            {
                return default;
            }

            var json = await File.ReadAllTextAsync(filePath);
            return JsonSerializer.Deserialize<T>(json, _jsonOptions);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Load data failed for key '{key}': {ex.Message}");
            return default;
        }
    }

    public async Task RemoveAsync(string key)
    {
        try
        {
            var filePath = GetFilePath(key);
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
            }
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Remove data failed for key '{key}': {ex.Message}");
            throw;
        }
    }

    public async Task<bool> ExistsAsync(string key)
    {
        try
        {
            var filePath = GetFilePath(key);
            return File.Exists(filePath);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Check exists failed for key '{key}': {ex.Message}");
            return false;
        }
    }

    public async Task ClearAllAsync()
    {
        try
        {
            if (Directory.Exists(_dataDirectory))
            {
                var files = Directory.GetFiles(_dataDirectory, "*.json");
                foreach (var file in files)
                {
                    File.Delete(file);
                }
            }
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Clear all data failed: {ex.Message}");
            throw;
        }
    }

    private string GetFilePath(string key)
    {
        // 清理文件名中的非法字符
        var fileName = string.Join("_", key.Split(Path.GetInvalidFileNameChars()));
        return Path.Combine(_dataDirectory, $"{fileName}.json");
    }
}
