#!/usr/bin/env python3
"""
EduSynapse Backend Server Starter
服务器启动器 - 绕过所有启动问题
"""

import subprocess
import sys
import time
import os
import signal

def kill_existing_processes():
    """杀死占用8000端口的进程"""
    try:
        # Windows
        result = subprocess.run(
            'netstat -ano | findstr :8000',
            shell=True,
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            pids = set()
            for line in lines:
                if 'LISTENING' in line:
                    parts = line.split()
                    if len(parts) > 4:
                        pid = parts[-1]
                        pids.add(pid)
            
            for pid in pids:
                try:
                    subprocess.run(f'taskkill /F /PID {pid}', shell=True, check=False)
                    print(f"✅ 已终止进程 PID: {pid}")
                except:
                    pass
    except:
        pass

def start_server():
    """启动服务器"""
    print("🚀 启动EduSynapse Backend服务器...")
    
    # 杀死现有进程
    kill_existing_processes()
    time.sleep(2)
    
    # 启动服务器
    cmd = [
        sys.executable, "-c",
        """
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from datetime import datetime
import sys
import os

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 创建应用
app = FastAPI(
    title="EduSynapse AI Backend",
    description="基于AI的智能教学系统后端API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 基础路由
@app.get("/")
async def root():
    return {
        "message": "🎓 欢迎使用 EduSynapse AI智能教学系统 API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health",
        "ai_status": "/api/ai/status",
        "timestamp": datetime.now().isoformat(),
        "status": "running"
    }

@app.get("/health")
async def health():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

# 尝试加载AI路由
ai_routes_loaded = False
try:
    from app.api.ai_teaching import router as ai_teaching_router
    app.include_router(ai_teaching_router)
    ai_routes_loaded = True
    print("✅ AI路由加载成功")
except Exception as e:
    print(f"⚠️  AI路由加载失败: {e}")

# 路由状态
@app.get("/api/routes/status")
async def routes_status():
    return {
        "ai_teaching_routes": ai_routes_loaded,
        "total_routes": len(app.routes),
        "message": "路由状态检查"
    }

print("🎉 EduSynapse Backend启动成功!")
print("📍 访问地址: http://localhost:8000")
print("📚 API文档: http://localhost:8000/docs")
print("🏥 健康检查: http://localhost:8000/health")
print("🤖 AI状态: http://localhost:8000/api/ai/status")
print("=" * 50)

# 启动服务器
uvicorn.run(
    app,
    host="0.0.0.0",
    port=8000,
    reload=False,
    log_level="info"
)
        """
    ]
    
    try:
        # 启动进程
        process = subprocess.Popen(
            cmd,
            cwd=os.path.dirname(os.path.abspath(__file__)),
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        print("📋 服务器进程已启动...")
        print("📍 访问地址: http://localhost:8000")
        print("📚 API文档: http://localhost:8000/docs")
        print("🏥 健康检查: http://localhost:8000/health")
        print("🤖 AI状态: http://localhost:8000/api/ai/status")
        print("=" * 50)
        print("💡 按 Ctrl+C 停止服务器")
        print("=" * 50)
        
        # 实时输出
        try:
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    print(output.strip())
        except KeyboardInterrupt:
            print("\n🛑 正在停止服务器...")
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            print("👋 服务器已停止")
            
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    start_server()
