@page "/ai-teacher-chat"
@using EduSynapse.MAUI.Services
@using EduSynapse.MAUI.Models
@inject AITeacherService AITeacherService
@inject StorageService StorageService

<div class="container-fluid p-4">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-3">🤖 AI教师对话</h2>
            <p class="text-muted">与不同风格的AI教师进行互动学习</p>
        </div>
    </div>

    <div class="row">
        <!-- 教师选择和设置 -->
        <div class="col-lg-3 col-md-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">🎭 选择AI教师</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">教师类型</label>
                        <select @onchange="OnTeacherTypeChanged" class="form-select" value="@selectedTeacherType.ToString()">
                            <option value="Socratic">🤔 苏格拉底式</option>
                            <option value="CaseDriven">💼 案例驱动</option>
                            <option value="Gamified">🎮 游戏化</option>
                        </select>
                        <small class="text-muted">@GetTeacherDescription(selectedTeacherType)</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">学习阶段</label>
                        <select @bind="selectedStage" class="form-select">
                            <option value="What">📖 What (是什么)</option>
                            <option value="Why">🤔 Why (为什么)</option>
                            <option value="How">💻 How (怎么做)</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">学习主题</label>
                        <input @bind="currentTopic" type="text" class="form-control" placeholder="输入学习主题..." />
                    </div>

                    <button class="btn btn-outline-danger btn-sm w-100" @onclick="ClearConversation">
                        🗑️ 清空对话
                    </button>
                </div>
            </div>

            <!-- 教师信息卡片 -->
            <div class="card mt-3">
                <div class="card-body text-center">
                    <div class="teacher-avatar mb-2">
                        @GetTeacherAvatar(selectedTeacherType)
                    </div>
                    <h6>@GetTeacherName(selectedTeacherType)</h6>
                    <small class="text-muted">@GetTeacherMotto(selectedTeacherType)</small>
                </div>
            </div>
        </div>

        <!-- 对话区域 -->
        <div class="col-lg-9 col-md-8">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">💬 对话窗口</h6>
                    <span class="badge bg-primary">@messages.Count 条消息</span>
                </div>
                <div class="card-body d-flex flex-column" style="height: 600px;">
                    <!-- 消息列表 -->
                    <div class="flex-grow-1 overflow-auto mb-3" id="messageContainer">
                        @if (!messages.Any())
                        {
                            <div class="text-center text-muted p-4">
                                <i class="fas fa-comments fa-3x mb-3"></i>
                                <h6>开始与AI教师对话</h6>
                                <p>选择教师类型和学习主题，然后发送您的第一条消息</p>
                            </div>
                        }
                        else
                        {
                            @foreach (var message in messages)
                            {
                                <div class="message-item mb-3 @(message.Role == "user" ? "user-message" : "ai-message")">
                                    <div class="d-flex @(message.Role == "user" ? "justify-content-end" : "justify-content-start")">
                                        <div class="message-bubble @(message.Role == "user" ? "bg-primary text-white" : "bg-light")">
                                            @if (message.Role == "assistant")
                                            {
                                                <div class="message-header mb-2">
                                                    <small class="text-muted">
                                                        @GetTeacherAvatar(selectedTeacherType) @GetTeacherName(selectedTeacherType)
                                                        <span class="ms-2">@message.Timestamp.ToString("HH:mm")</span>
                                                    </small>
                                                </div>
                                            }
                                            <div class="message-content">
                                                @((MarkupString)FormatMessageContent(message.Content))
                                            </div>
                                            @if (message.Role == "user")
                                            {
                                                <div class="message-time">
                                                    <small class="text-white-50">@message.Timestamp.ToString("HH:mm")</small>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                </div>
                            }
                        }

                        @if (isThinking)
                        {
                            <div class="message-item mb-3 ai-message">
                                <div class="d-flex justify-content-start">
                                    <div class="message-bubble bg-light">
                                        <div class="thinking-indicator">
                                            <span class="dot"></span>
                                            <span class="dot"></span>
                                            <span class="dot"></span>
                                        </div>
                                        <small class="text-muted">@GetTeacherName(selectedTeacherType) 正在思考...</small>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>

                    <!-- 输入区域 -->
                    <div class="input-area">
                        <form @onsubmit="SendMessage" @onsubmit:preventDefault="true">
                            <div class="input-group">
                                <input @bind="userInput" @onkeypress="OnKeyPress" type="text" class="form-control" 
                                       placeholder="输入您的问题或想法..." disabled="@isThinking" />
                                <button type="submit" class="btn btn-primary" disabled="@(isThinking || string.IsNullOrWhiteSpace(userInput))">
                                    @if (isThinking)
                                    {
                                        <span class="spinner-border spinner-border-sm"></span>
                                    }
                                    else
                                    {
                                        <span>发送</span>
                                    }
                                </button>
                            </div>
                        </form>
                        
                        <!-- 快捷回复 -->
                        @if (suggestedResponses.Any())
                        {
                            <div class="mt-2">
                                <small class="text-muted">💡 建议回复：</small>
                                <div class="d-flex flex-wrap gap-1 mt-1">
                                    @foreach (var suggestion in suggestedResponses)
                                    {
                                        <button class="btn btn-outline-secondary btn-sm" @onclick="() => UseSuggestedResponse(suggestion)">
                                            @suggestion
                                        </button>
                                    }
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作结果提示 -->
    @if (!string.IsNullOrEmpty(resultMessage))
    {
        <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1050;">
            <div class="toast show" role="alert">
                <div class="toast-header">
                    <strong class="me-auto">@(isSuccess ? "✅ 成功" : "❌ 错误")</strong>
                    <button type="button" class="btn-close" @onclick="ClearResult"></button>
                </div>
                <div class="toast-body">
                    @resultMessage
                </div>
            </div>
        </div>
    }
</div>

<style>
    .message-bubble {
        max-width: 80%;
        padding: 12px 16px;
        border-radius: 18px;
        word-wrap: break-word;
    }
    
    .user-message .message-bubble {
        border-bottom-right-radius: 4px;
    }
    
    .ai-message .message-bubble {
        border-bottom-left-radius: 4px;
    }
    
    .teacher-avatar {
        font-size: 2rem;
    }
    
    .thinking-indicator {
        display: flex;
        align-items: center;
        gap: 4px;
        margin-bottom: 8px;
    }
    
    .dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #6c757d;
        animation: thinking 1.4s infinite ease-in-out both;
    }
    
    .dot:nth-child(1) { animation-delay: -0.32s; }
    .dot:nth-child(2) { animation-delay: -0.16s; }
    
    @@keyframes thinking {
        0%, 80%, 100% { transform: scale(0); }
        40% { transform: scale(1); }
    }
    
    .message-content {
        line-height: 1.5;
    }
    
    .message-content code {
        background-color: rgba(0,0,0,0.1);
        padding: 2px 4px;
        border-radius: 4px;
        font-family: 'Courier New', monospace;
    }
    
    .input-area {
        border-top: 1px solid #dee2e6;
        padding-top: 1rem;
    }
    
    #messageContainer {
        scroll-behavior: smooth;
    }
</style>

@code {
    private bool isThinking = false;
    private bool isSuccess = false;
    private string resultMessage = "";
    private string userInput = "";
    private string currentTopic = "机器学习";

    private TeacherType selectedTeacherType = TeacherType.CaseDriven;
    private LearningStage selectedStage = LearningStage.What;

    private List<ChatMessage> messages = new();
    private List<string> suggestedResponses = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadConversationHistory();
        UpdateSuggestedResponses();
    }

    private async Task SendMessage()
    {
        if (string.IsNullOrWhiteSpace(userInput)) return;

        var userMessage = new ChatMessage
        {
            Role = "user",
            Content = userInput.Trim(),
            Timestamp = DateTime.Now
        };

        messages.Add(userMessage);
        var currentInput = userInput;
        userInput = "";
        isThinking = true;

        StateHasChanged();
        await ScrollToBottom();

        try
        {
            // 调用AI教师服务
            var response = await AITeacherService.ChatWithTeacherAsync(
                selectedTeacherType,
                selectedStage,
                currentTopic,
                currentInput,
                messages.TakeLast(10).ToList()
            );

            var aiMessage = new ChatMessage
            {
                Role = "assistant",
                Content = response.Content,
                Timestamp = response.Timestamp
            };

            messages.Add(aiMessage);

            // 更新建议回复
            UpdateSuggestedResponsesBasedOnAI(response);

            // 保存对话历史
            await SaveConversationHistory();

            System.Diagnostics.Debug.WriteLine($"✅ AI教师回复: {response.Content.Substring(0, Math.Min(50, response.Content.Length))}...");
        }
        catch (Exception ex)
        {
            var errorMessage = new ChatMessage
            {
                Role = "assistant",
                Content = $"抱歉，我现在无法回复。请稍后再试。({ex.Message})",
                Timestamp = DateTime.Now
            };
            messages.Add(errorMessage);

            ShowMessage($"AI教师暂时无法回复: {ex.Message}", false);
            System.Diagnostics.Debug.WriteLine($"❌ AI教师对话失败: {ex}");
        }
        finally
        {
            isThinking = false;
            StateHasChanged();
            await ScrollToBottom();
        }
    }

    private async Task OnKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter" && !e.ShiftKey)
        {
            await SendMessage();
        }
    }

    private void OnTeacherTypeChanged(ChangeEventArgs e)
    {
        if (Enum.TryParse<TeacherType>(e.Value?.ToString(), out var teacherType))
        {
            selectedTeacherType = teacherType;
            UpdateSuggestedResponses();
        }
    }

    private void UseSuggestedResponse(string suggestion)
    {
        userInput = suggestion;
    }

    private async Task ClearConversation()
    {
        messages.Clear();
        suggestedResponses.Clear();
        await SaveConversationHistory();
        UpdateSuggestedResponses();
        ShowMessage("对话已清空", true);
    }

    private void UpdateSuggestedResponses()
    {
        suggestedResponses = selectedTeacherType switch
        {
            TeacherType.Socratic => new List<string>
            {
                "我想了解更多",
                "为什么会这样？",
                "能举个例子吗？",
                "这和什么相关？"
            },
            TeacherType.CaseDriven => new List<string>
            {
                "有实际案例吗？",
                "在项目中如何应用？",
                "能看看代码示例吗？",
                "有什么最佳实践？"
            },
            TeacherType.Gamified => new List<string>
            {
                "我准备好挑战了！",
                "下一个任务是什么？",
                "我能获得什么奖励？",
                "让我们开始练习吧！"
            },
            _ => new List<string> { "继续", "解释一下", "举个例子" }
        };
    }

    private void UpdateSuggestedResponsesBasedOnAI(TeacherResponse response)
    {
        // 根据AI回复动态更新建议
        if (response.SuggestedNextAction.Contains("思考"))
        {
            suggestedResponses = new List<string> { "让我想想...", "我认为是...", "可能的原因是..." };
        }
        else if (response.SuggestedNextAction.Contains("实践"))
        {
            suggestedResponses = new List<string> { "我想试试", "给我一个练习", "如何开始？" };
        }
        else if (response.SuggestedNextAction.Contains("挑战"))
        {
            suggestedResponses = new List<string> { "接受挑战！", "这看起来有趣", "我准备好了" };
        }
    }

    private async Task LoadConversationHistory()
    {
        try
        {
            var history = await StorageService.LoadAsync<List<ChatMessage>>("ai_chat_history") ?? new List<ChatMessage>();
            messages = history.TakeLast(50).ToList(); // 只保留最近50条消息
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ 加载对话历史失败: {ex}");
        }
    }

    private async Task SaveConversationHistory()
    {
        try
        {
            await StorageService.SaveAsync("ai_chat_history", messages);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ 保存对话历史失败: {ex}");
        }
    }

    private async Task ScrollToBottom()
    {
        await Task.Delay(100); // 等待DOM更新
        // 这里可以添加JavaScript调用来滚动到底部
    }

    private string FormatMessageContent(string content)
    {
        // 简单的格式化：将代码块用<code>标签包围
        content = System.Text.RegularExpressions.Regex.Replace(
            content,
            @"`([^`]+)`",
            "<code>$1</code>"
        );

        // 将换行符转换为<br>
        content = content.Replace("\n", "<br>");

        return content;
    }

    private string GetTeacherDescription(TeacherType teacherType)
    {
        return teacherType switch
        {
            TeacherType.Socratic => "通过提问引导思考",
            TeacherType.CaseDriven => "基于实际案例教学",
            TeacherType.Gamified => "游戏化趣味学习",
            _ => "通用教学方式"
        };
    }

    private string GetTeacherAvatar(TeacherType teacherType)
    {
        return teacherType switch
        {
            TeacherType.Socratic => "🤔",
            TeacherType.CaseDriven => "💼",
            TeacherType.Gamified => "🎮",
            _ => "🤖"
        };
    }

    private string GetTeacherName(TeacherType teacherType)
    {
        return teacherType switch
        {
            TeacherType.Socratic => "苏格拉底老师",
            TeacherType.CaseDriven => "实战导师",
            TeacherType.Gamified => "游戏教练",
            _ => "AI助教"
        };
    }

    private string GetTeacherMotto(TeacherType teacherType)
    {
        return teacherType switch
        {
            TeacherType.Socratic => "\"我知道我一无所知\"",
            TeacherType.CaseDriven => "\"实践出真知\"",
            TeacherType.Gamified => "\"学习就是游戏\"",
            _ => "\"知识改变命运\""
        };
    }

    private void ShowMessage(string message, bool success)
    {
        resultMessage = message;
        isSuccess = success;

        Task.Delay(3000).ContinueWith(_ =>
        {
            resultMessage = "";
            InvokeAsync(StateHasChanged);
        });
    }

    private void ClearResult()
    {
        resultMessage = "";
    }
}
