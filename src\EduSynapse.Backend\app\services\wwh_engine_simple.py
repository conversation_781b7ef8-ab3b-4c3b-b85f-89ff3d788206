"""
EduSynapse WWH Learning Plan Generation Engine (Simplified)
WWH框架学习计划生成引擎 (简化版)
"""

import json
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
import openai
from app.core.config import settings


class WWHEngine:
    """
    WWH (What-Why-How) 学习计划生成引擎 (简化版)

    基于WWH框架生成结构化的个性化学习计划:
    - What: 学习什么 (核心概念、定义、特征)
    - Why: 为什么学 (历史背景、实用价值、学习动机)
    - How: 怎么学 (实践项目、代码示例、练习)
    """

    def __init__(self):
        self.client = None
        self._initialize_client()

    def _initialize_client(self):
        """初始化 OpenAI 客户端"""
        try:
            if settings.has_openai_key:
                # 设置自定义 API 基地址
                if settings.openai_api_base:
                    print(f"🔗 使用自定义 OpenAI API 基地址: {settings.openai_api_base}")
                
                self.client = openai.OpenAI(
                    api_key=settings.openai_api_key,
                    base_url=settings.openai_api_base
                )
                print(f"✅ OpenAI客户端初始化成功: {settings.openai_model}")
            else:
                print("❌ 未配置OpenAI API密钥")
                raise ValueError("需要配置AI API密钥")
        except Exception as e:
            print(f"❌ OpenAI客户端初始化失败: {e}")
            raise

    async def generate_learning_plan(
        self,
        topic: str,
        duration_days: int = 14,
        difficulty_level: str = "medium",
        target_hours_per_day: float = 2.0,
        learning_goals: str = "全面掌握相关知识和技能"
    ) -> Dict[str, Any]:
        """
        生成完整的WWH学习计划
        
        Args:
            topic: 学习主题
            duration_days: 计划持续天数
            difficulty_level: 难度级别 (easy/medium/hard)
            target_hours_per_day: 每日目标学习时长
            learning_goals: 学习目标
            
        Returns:
            完整的学习计划字典
        """
        try:
            print(f"🎯 开始生成学习计划: {topic}")
            
            # 构建提示词
            system_prompt = """你是一位专业的学习规划师，擅长使用WWH框架(What-Why-How)设计个性化学习计划。

WWH框架说明:
- What: 学习什么 (核心概念、定义、特征)
- Why: 为什么学 (历史背景、实用价值、学习动机)  
- How: 怎么学 (实践项目、代码示例、练习)

请严格按照JSON格式返回，确保结构完整且实用。"""

            user_prompt = f"""请为主题"{topic}"生成一个{duration_days}天的学习计划。

要求:
- 难度级别: {difficulty_level}
- 每日目标学习时长: {target_hours_per_day}小时
- 学习目标: {learning_goals}

请返回JSON格式，包含以下结构:
{{
    "title": "学习计划标题",
    "description": "计划描述",
    "wwh_structure": {{
        "what": {{
            "focus": "What阶段重点",
            "content": ["核心概念1", "核心概念2"],
            "learning_objectives": ["目标1", "目标2"]
        }},
        "why": {{
            "focus": "Why阶段重点", 
            "content": ["背景知识1", "应用价值2"],
            "learning_objectives": ["目标1", "目标2"]
        }},
        "how": {{
            "focus": "How阶段重点",
            "content": ["实践项目1", "练习2"],
            "learning_objectives": ["目标1", "目标2"]
        }}
    }},
    "daily_plans": [
        {{
            "day": 1,
            "phase": "what",
            "title": "第1天标题",
            "objectives": ["目标1", "目标2"],
            "content": "详细学习内容",
            "activities": ["活动1", "活动2"],
            "estimated_hours": 2.0,
            "resources": ["资源1", "资源2"],
            "assessment": "评估方式"
        }}
    ],
    "resources": {{
        "books": ["推荐书籍"],
        "websites": ["网站资源"],
        "tools": ["工具软件"],
        "communities": ["学习社区"]
    }},
    "milestones": [
        {{
            "day": 7,
            "title": "里程碑标题",
            "description": "里程碑描述",
            "deliverables": ["交付物1", "交付物2"]
        }}
    ]
}}"""

            # 调用 OpenAI API
            response = self.client.chat.completions.create(
                model=settings.openai_model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=settings.openai_temperature,
                max_tokens=settings.openai_max_tokens
            )
            
            # 解析响应
            content = response.choices[0].message.content
            
            try:
                plan_data = json.loads(content)
                print(f"✅ 学习计划生成成功: {plan_data.get('title', topic)}")
                return plan_data
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                # 返回一个基础的计划结构
                return self._create_fallback_plan(topic, duration_days, target_hours_per_day)
                
        except Exception as e:
            print(f"❌ 学习计划生成失败: {e}")
            # 返回一个基础的计划结构
            return self._create_fallback_plan(topic, duration_days, target_hours_per_day)

    def _create_fallback_plan(self, topic: str, duration_days: int, target_hours_per_day: float) -> Dict[str, Any]:
        """创建备用的基础学习计划"""
        return {
            "title": f"{topic} 学习计划",
            "description": f"为期{duration_days}天的{topic}学习计划",
            "wwh_structure": {
                "what": {
                    "focus": f"{topic}的核心概念和基础知识",
                    "content": [f"{topic}基础概念", f"{topic}核心原理"],
                    "learning_objectives": ["理解基本概念", "掌握核心原理"]
                },
                "why": {
                    "focus": f"学习{topic}的重要性和价值",
                    "content": [f"{topic}的应用价值", f"{topic}的发展前景"],
                    "learning_objectives": ["了解应用场景", "认识学习价值"]
                },
                "how": {
                    "focus": f"{topic}的实践应用和技能训练",
                    "content": [f"{topic}实践项目", f"{topic}技能练习"],
                    "learning_objectives": ["完成实践项目", "提升实际技能"]
                }
            },
            "daily_plans": self._generate_daily_plans(topic, duration_days, target_hours_per_day),
            "resources": {
                "books": [f"{topic}相关书籍"],
                "websites": [f"{topic}学习网站"],
                "tools": [f"{topic}相关工具"],
                "communities": [f"{topic}学习社区"]
            },
            "milestones": [
                {
                    "day": duration_days // 2,
                    "title": "中期检查点",
                    "description": "评估学习进度和效果",
                    "deliverables": ["阶段性总结", "知识点梳理"]
                },
                {
                    "day": duration_days,
                    "title": "学习完成",
                    "description": "完成全部学习内容",
                    "deliverables": ["最终项目", "学习总结"]
                }
            ]
        }

    def _generate_daily_plans(self, topic: str, duration_days: int, target_hours_per_day: float) -> List[Dict[str, Any]]:
        """生成每日学习计划"""
        daily_plans = []
        
        # WWH阶段分配 (前1/3 What, 中1/3 Why, 后1/3 How)
        what_days = max(1, duration_days // 3)
        why_days = max(1, duration_days // 3)
        how_days = duration_days - what_days - why_days
        
        for day in range(1, duration_days + 1):
            if day <= what_days:
                phase = "what"
                phase_name = "理论学习"
            elif day <= what_days + why_days:
                phase = "why"
                phase_name = "背景理解"
            else:
                phase = "how"
                phase_name = "实践应用"
            
            daily_plan = {
                "day": day,
                "phase": phase,
                "title": f"第{day}天: {topic} {phase_name}",
                "objectives": [f"完成{phase_name}相关内容", f"达到第{day}天学习目标"],
                "content": f"第{day}天的{topic}学习内容，重点关注{phase_name}",
                "activities": [f"{phase_name}学习", "练习和复习"],
                "estimated_hours": target_hours_per_day,
                "resources": [f"第{day}天学习资料"],
                "assessment": f"通过练习和测试评估第{day}天的学习效果"
            }
            daily_plans.append(daily_plan)
        
        return daily_plans

    async def test_connection(self) -> bool:
        """测试API连接"""
        try:
            response = self.client.chat.completions.create(
                model=settings.openai_model,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=10
            )
            return True
        except Exception as e:
            print(f"❌ API连接测试失败: {e}")
            return False


# 创建全局实例
wwh_engine = WWHEngine()
