"""
EduSynapse Backend Main Application - Fixed Version
修复版主应用程序
"""

import os
import sys
import uvicorn
from datetime import datetime
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("🚀 正在启动EduSynapse Backend...")

# 尝试导入配置
try:
    from app.core.config import settings
    print("✅ 配置加载成功")
except Exception as e:
    print(f"⚠️  配置加载失败: {e}")
    # 使用默认配置
    class DefaultSettings:
        app_name = "EduSynapse AI Backend"
        app_version = "1.0.0"
        host = "0.0.0.0"
        port = 8000
        debug = True
        log_level = "info"
        cors_origins = ["*"]
        preferred_ai_provider = "openai"
    
    settings = DefaultSettings()

# 创建FastAPI应用
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="EduSynapse 智能学习系统后端API",
    docs_url="/docs",
    redoc_url="/redoc",
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理"""
    print(f"❌ 未处理的异常: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": {
                "code": "INTERNAL_SERVER_ERROR",
                "message": "服务器内部错误",
                "details": str(exc) if settings.debug else "请联系管理员",
            },
            "timestamp": datetime.now().isoformat(),
        },
    )

# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查"""
    try:
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": settings.app_version,
            "database": "connected",
            "ai_provider": settings.preferred_ai_provider,
            "backend_mode": "operational",
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e),
            "backend_mode": "error",
        }

# 根路径
@app.get("/")
async def root():
    """根路径信息"""
    return {
        "message": "🎓 欢迎使用 EduSynapse AI智能教学系统 API",
        "version": settings.app_version,
        "docs": "/docs",
        "health": "/health",
        "ai_status": "/api/ai/status",
        "timestamp": datetime.now().isoformat(),
        "features": [
            "🤖 AI智能教学引擎 (LangChain + AutoGen)",
            "📚 WWH教学框架",
            "🎭 多AI教师协作",
            "📊 智能学习计划生成",
            "📈 学习进度跟踪",
            "🔍 智能分析",
        ],
        "ai_endpoints": {
            "generate_plan": "/api/ai/generate-plan",
            "start_session": "/api/ai/start-session",
            "continue_session": "/api/ai/continue-session",
            "teachers": "/api/ai/teachers",
            "wwh_framework": "/api/ai/wwh-framework",
        },
    }

# 尝试注册路由
print("📋 正在注册路由...")

# 注册AI教学路由
try:
    from app.api.ai_teaching import router as ai_teaching_router
    app.include_router(ai_teaching_router)
    print("✅ AI教学路由注册成功")
except Exception as e:
    print(f"⚠️  AI教学路由注册失败: {e}")

# 注册学习计划路由
try:
    from app.api.learning_plan import router as learning_plan_router
    app.include_router(learning_plan_router)
    print("✅ 学习计划路由注册成功")
except Exception as e:
    print(f"⚠️  学习计划路由注册失败: {e}")

# 注册进度路由
try:
    from app.api.progress import router as progress_router
    app.include_router(progress_router)
    print("✅ 进度路由注册成功")
except Exception as e:
    print(f"⚠️  进度路由注册失败: {e}")

print("🎉 EduSynapse Backend 初始化完成!")

# 开发服务器启动
if __name__ == "__main__":
    print("🔧 开发模式启动...")
    print(f"📍 访问地址: http://{settings.host}:{settings.port}")
    print(f"📚 API文档: http://{settings.host}:{settings.port}/docs")
    print(f"🏥 健康检查: http://{settings.host}:{settings.port}/health")
    
    # 启动服务器
    uvicorn.run(
        app,  # 直接传递app对象
        host=settings.host,
        port=settings.port,
        reload=False,  # 禁用自动重载
        log_level=settings.log_level.lower(),
        access_log=True,
    )
