#!/usr/bin/env python3
"""
数据库连接测试脚本
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_database():
    """测试数据库连接和初始化"""
    print("🔍 开始数据库测试...")
    
    try:
        # 导入数据库模块
        from app.database.database import check_database_connection, create_tables, DATABASE_URL
        
        print(f"📍 数据库URL: {DATABASE_URL}")
        
        # 测试连接
        print("\n1. 测试数据库连接...")
        if check_database_connection():
            print("✅ 数据库连接成功")
        else:
            print("❌ 数据库连接失败")
            return False
        
        # 测试表创建
        print("\n2. 测试数据库表创建...")
        try:
            create_tables()
            print("✅ 数据库表创建成功")
        except Exception as e:
            print(f"❌ 数据库表创建失败: {e}")
            return False
        
        # 测试模型导入
        print("\n3. 测试数据模型导入...")
        try:
            from app.models.learning_plan import LearningPlan, LearningProgress
            print("✅ 数据模型导入成功")
        except Exception as e:
            print(f"❌ 数据模型导入失败: {e}")
            return False
        
        # 测试会话创建
        print("\n4. 测试数据库会话...")
        try:
            from app.database.database import get_db
            db_gen = get_db()
            db = next(db_gen)
            db.close()
            print("✅ 数据库会话创建成功")
        except Exception as e:
            print(f"❌ 数据库会话创建失败: {e}")
            return False
        
        print("\n🎉 所有数据库测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sqlalchemy_version():
    """测试SQLAlchemy版本兼容性"""
    print("\n🔍 检查SQLAlchemy版本...")
    
    try:
        import sqlalchemy
        print(f"SQLAlchemy版本: {sqlalchemy.__version__}")
        
        # 测试text函数
        from sqlalchemy import text
        print("✅ text函数导入成功")
        
        # 测试基本类型
        from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime
        print("✅ 基本数据类型导入成功")
        
        return True
    except Exception as e:
        print(f"❌ SQLAlchemy版本检查失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("EduSynapse 数据库测试")
    print("=" * 50)
    
    # 测试SQLAlchemy版本
    if not test_sqlalchemy_version():
        sys.exit(1)
    
    # 测试数据库
    if not test_database():
        sys.exit(1)
    
    print("\n✅ 所有测试通过，数据库配置正常!")
