@page "/learning-plan-detail/{PlanId:int}"
@using EduSynapse.MAUI.Services
@using EduSynapse.MAUI.Models
@using Microsoft.AspNetCore.Components
@inject StorageService StorageService
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation

<div class="container-fluid p-4">
    @if (currentPlan == null)
    {
        <div class="text-center p-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载学习计划详情...</p>
        </div>
    }
    else
    {
        <!-- 学习计划基本信息 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-gradient-primary text-white">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h2 class="mb-2">📚 @currentPlan.Topic</h2>
                                <p class="mb-2">@currentPlan.Description</p>
                                <div class="d-flex flex-wrap gap-2">
                                    <span class="badge bg-light text-dark">
                                        📅 @currentPlan.DurationDays 天计划
                                    </span>
                                    <span class="badge bg-light text-dark">
                                        ⏰ 每日 @currentPlan.TargetHoursPerDay 小时
                                    </span>
                                    <span class="badge @GetDifficultyBadgeClass(currentPlan.DifficultyLevel)">
                                        🎯 @currentPlan.DifficultyDisplayName
                                    </span>
                                    <span class="badge @GetStatusBadgeClass(currentPlan.Status)">
                                        📊 @currentPlan.StatusDisplayName
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-4 text-md-end mt-3 mt-md-0">
                                <div class="row">
                                    <div class="col-6 col-md-12 mb-2">
                                        <small>创建时间</small>
                                        <div class="h6 mb-0">@currentPlan.CreatedAt.ToString("yyyy-MM-dd")</div>
                                    </div>
                                    <div class="col-6 col-md-12">
                                        <small>计划ID</small>
                                        <div class="h6 mb-0">#@currentPlan.Id</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 学习统计卡片 -->
        <div class="row mb-4">
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-primary">@GetTotalStudyDays()</h3>
                        <p class="mb-0">已学习天数</p>
                        <small class="text-muted">目标: @currentPlan.DurationDays 天</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-success">@GetTotalStudyHours().ToString("F1")</h3>
                        <p class="mb-0">累计学习时间</p>
                        <small class="text-muted">目标: @(currentPlan.DurationDays * currentPlan.TargetHoursPerDay).ToString("F1") 小时</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-info">@GetAverageStudyHours().ToString("F1")</h3>
                        <p class="mb-0">平均每日时间</p>
                        <small class="text-muted">目标: @currentPlan.TargetHoursPerDay 小时</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-warning">@GetCompletionRate().ToString("F0")%</h3>
                        <p class="mb-0">完成率</p>
                        <small class="text-muted">基于时间进度</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex flex-wrap gap-2">
                            <button class="btn btn-outline-secondary" @onclick="GoBack">
                                ← 返回列表
                            </button>
                            <button class="btn btn-primary" @onclick="RecordProgress">
                                📝 记录今日进度
                            </button>
                            <button class="btn btn-outline-info" @onclick="RefreshData">
                                🔄 刷新数据
                            </button>
                            <button class="btn btn-outline-success" @onclick="ExportData">
                                📤 导出数据
                            </button>
                            @if (currentPlan.Status == "active")
                            {
                                <button class="btn btn-outline-warning" @onclick="PausePlan">
                                    ⏸️ 暂停计划
                                </button>
                            }
                            else if (currentPlan.Status == "paused")
                            {
                                <button class="btn btn-outline-success" @onclick="ResumePlan">
                                    ▶️ 恢复计划
                                </button>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 学习记录列表 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">📊 学习记录历史 (@(progressRecords?.Count ?? 0) 条记录)</h5>
                        <div class="d-flex gap-2">
                            <select @bind="selectedSortOrder" @bind:after="SortRecords" class="form-select form-select-sm" style="width: auto;">
                                <option value="desc">最新在前</option>
                                <option value="asc">最早在前</option>
                            </select>
                            <select @bind="selectedStatusFilter" @bind:after="FilterRecords" class="form-select form-select-sm" style="width: auto;">
                                <option value="">全部状态</option>
                                <option value="completed">已完成</option>
                                <option value="partial">部分完成</option>
                                <option value="skipped">未学习</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-body">
                        @if (filteredRecords == null || !filteredRecords.Any())
                        {
                            <div class="text-center p-4">
                                <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">@(progressRecords?.Any() == true ? "没有符合条件的记录" : "还没有学习记录")</h6>
                                <p class="text-muted">@(progressRecords?.Any() == true ? "尝试调整筛选条件" : "点击上方按钮开始记录学习进度")</p>
                                @if (progressRecords?.Any() != true)
                                {
                                    <button class="btn btn-primary mt-2" @onclick="RecordProgress">
                                        📝 记录今日进度
                                    </button>
                                }
                            </div>
                        }
                        else
                        {
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>日期</th>
                                            <th>学习时间</th>
                                            <th>状态</th>
                                            <th>学习内容</th>
                                            <th>心情评分</th>
                                            <th>学习笔记</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var record in filteredRecords)
                                        {
                                            <tr class="@(record.Date.Date == DateTime.Today ? "table-warning" : "")">
                                                <td>
                                                    <strong>@record.Date.ToString("MM-dd")</strong>
                                                    <br/>
                                                    <small class="text-muted">@record.Date.ToString("dddd")</small>
                                                </td>
                                                <td>
                                                    <span class="badge @GetHoursBadgeClass(record.StudyHours)">
                                                        @record.StudyHours.ToString("F1")h
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge @GetRecordStatusBadgeClass(record.Status)">
                                                        @GetRecordStatusDisplayName(record.Status)
                                                    </span>
                                                </td>
                                                <td class="text-truncate" style="max-width: 200px;" title="@record.Content">
                                                    @record.Content
                                                </td>
                                                <td>
                                                    @if (record.MoodScore > 0)
                                                    {
                                                        <span class="badge @GetMoodBadgeClass(record.MoodScore)" title="心情评分: @record.MoodScore/10">
                                                            😊 @record.MoodScore
                                                        </span>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-muted">-</span>
                                                    }
                                                </td>
                                                <td class="text-truncate" style="max-width: 250px;" title="@record.Notes">
                                                    @record.Notes
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        @if (record.Date.Date == DateTime.Today)
                                                        {
                                                            <button class="btn btn-outline-primary btn-sm" @onclick="() => EditRecord(record)" title="编辑今日记录">
                                                                ✏️
                                                            </button>
                                                        }
                                                        <button class="btn btn-outline-info btn-sm" @onclick="() => ViewRecordDetail(record)" title="查看详情">
                                                            👁️
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>

                            <!-- 分页 (如果记录很多) -->
                            @if (filteredRecords.Count > 10)
                            {
                                <nav class="mt-3">
                                    <p class="text-muted text-center">显示最近 10 条记录，共 @filteredRecords.Count 条</p>
                                </nav>
                            }
                        }
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- 操作结果提示 -->
    @if (!string.IsNullOrEmpty(resultMessage))
    {
        <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1050;">
            <div class="toast show" role="alert">
                <div class="toast-header">
                    <strong class="me-auto">@(isSuccess ? "✅ 成功" : "❌ 错误")</strong>
                    <button type="button" class="btn-close" @onclick="ClearResult"></button>
                </div>
                <div class="toast-body">
                    @resultMessage
                </div>
            </div>
        </div>
    }
</div>

@code {
    [Parameter] public int PlanId { get; set; }

    private LearningPlan? currentPlan = null;
    private List<ProgressRecord>? progressRecords = null;
    private List<ProgressRecord>? filteredRecords = null;
    private bool isSuccess = false;
    private string resultMessage = "";

    // 筛选和排序
    private string selectedSortOrder = "desc";
    private string selectedStatusFilter = "";

    protected override async Task OnInitializedAsync()
    {
        await LoadPlanAndProgress();
    }

    private async Task LoadPlanAndProgress()
    {
        try
        {
            // 加载学习计划
            var allPlans = await StorageService.LoadAsync<List<LearningPlan>>("learning_plans") ?? new List<LearningPlan>();
            currentPlan = allPlans.FirstOrDefault(p => p.Id == PlanId);

            if (currentPlan == null)
            {
                ShowMessage("未找到指定的学习计划", false);
                return;
            }

            // 加载进度记录
            await LoadProgressRecords();

            System.Diagnostics.Debug.WriteLine($"✅ 加载学习计划详情: {currentPlan.Topic}, {progressRecords?.Count ?? 0} 条记录");
        }
        catch (Exception ex)
        {
            ShowMessage($"加载数据失败: {ex.Message}", false);
            System.Diagnostics.Debug.WriteLine($"❌ 加载数据失败: {ex}");
        }
    }

    private async Task LoadProgressRecords()
    {
        try
        {
            var storageKey = $"progress_records_{PlanId}";
            progressRecords = await StorageService.LoadAsync<List<ProgressRecord>>(storageKey) ?? new List<ProgressRecord>();
            FilterRecords();
        }
        catch (Exception ex)
        {
            progressRecords = new List<ProgressRecord>();
            filteredRecords = new List<ProgressRecord>();
            System.Diagnostics.Debug.WriteLine($"❌ 加载进度记录失败: {ex}");
        }
    }

    private void FilterRecords()
    {
        if (progressRecords == null)
        {
            filteredRecords = new List<ProgressRecord>();
            return;
        }

        var filtered = progressRecords.AsEnumerable();

        // 状态筛选
        if (!string.IsNullOrEmpty(selectedStatusFilter))
        {
            filtered = filtered.Where(r => r.Status == selectedStatusFilter);
        }

        // 排序
        if (selectedSortOrder == "desc")
        {
            filtered = filtered.OrderByDescending(r => r.Date);
        }
        else
        {
            filtered = filtered.OrderBy(r => r.Date);
        }

        filteredRecords = filtered.ToList();
    }

    private void SortRecords()
    {
        FilterRecords();
    }

    private async Task RefreshData()
    {
        await LoadPlanAndProgress();
        ShowMessage("数据已刷新", true);
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/learning-plan-list");
    }

    private void RecordProgress()
    {
        // TODO: 打开进度记录模态框或导航到记录页面
        ShowMessage("进度记录功能开发中...", true);
    }

    private void EditRecord(ProgressRecord record)
    {
        ShowMessage($"编辑 {record.Date:MM-dd} 的学习记录功能开发中...", true);
    }

    private void ViewRecordDetail(ProgressRecord record)
    {
        var detail = $"日期: {record.Date:yyyy-MM-dd}\n学习时间: {record.StudyHours}小时\n状态: {GetRecordStatusDisplayName(record.Status)}\n内容: {record.Content}\n心情: {record.MoodScore}/10\n笔记: {record.Notes}";
        ShowMessage($"📋 学习记录详情:\n{detail}", true);
    }

    private async Task PausePlan()
    {
        if (currentPlan == null) return;

        try
        {
            currentPlan.Status = "paused";
            currentPlan.UpdatedAt = DateTime.Now;

            var allPlans = await StorageService.LoadAsync<List<LearningPlan>>("learning_plans") ?? new List<LearningPlan>();
            var planIndex = allPlans.FindIndex(p => p.Id == PlanId);
            if (planIndex >= 0)
            {
                allPlans[planIndex] = currentPlan;
                await StorageService.SaveAsync("learning_plans", allPlans);
                ShowMessage("学习计划已暂停", true);
            }
        }
        catch (Exception ex)
        {
            ShowMessage($"暂停计划失败: {ex.Message}", false);
        }
    }

    private async Task ResumePlan()
    {
        if (currentPlan == null) return;

        try
        {
            currentPlan.Status = "active";
            currentPlan.UpdatedAt = DateTime.Now;

            var allPlans = await StorageService.LoadAsync<List<LearningPlan>>("learning_plans") ?? new List<LearningPlan>();
            var planIndex = allPlans.FindIndex(p => p.Id == PlanId);
            if (planIndex >= 0)
            {
                allPlans[planIndex] = currentPlan;
                await StorageService.SaveAsync("learning_plans", allPlans);
                ShowMessage("学习计划已恢复", true);
            }
        }
        catch (Exception ex)
        {
            ShowMessage($"恢复计划失败: {ex.Message}", false);
        }
    }

    private async Task ExportData()
    {
        try
        {
            var exportData = new
            {
                Plan = currentPlan,
                Records = progressRecords,
                Statistics = new
                {
                    TotalDays = GetTotalStudyDays(),
                    TotalHours = GetTotalStudyHours(),
                    AverageHours = GetAverageStudyHours(),
                    CompletionRate = GetCompletionRate()
                },
                ExportTime = DateTime.Now
            };

            var json = System.Text.Json.JsonSerializer.Serialize(exportData, new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
            });

            await JSRuntime.InvokeVoidAsync("console.log", "学习计划数据导出:", json);
            ShowMessage("数据已导出到浏览器控制台", true);
        }
        catch (Exception ex)
        {
            ShowMessage($"导出数据失败: {ex.Message}", false);
        }
    }

    private void ShowMessage(string message, bool success)
    {
        resultMessage = message;
        isSuccess = success;

        // 3秒后自动清除消息
        Task.Delay(3000).ContinueWith(_ =>
        {
            resultMessage = "";
            InvokeAsync(StateHasChanged);
        });
    }

    private void ClearResult()
    {
        resultMessage = "";
    }

    // 统计计算方法
    private int GetTotalStudyDays()
    {
        return progressRecords?.Count(r => r.StudyHours > 0) ?? 0;
    }

    private double GetTotalStudyHours()
    {
        return progressRecords?.Sum(r => r.StudyHours) ?? 0;
    }

    private double GetAverageStudyHours()
    {
        var totalDays = GetTotalStudyDays();
        return totalDays > 0 ? GetTotalStudyHours() / totalDays : 0;
    }

    private double GetCompletionRate()
    {
        if (currentPlan == null) return 0;

        var daysSinceStart = (DateTime.Today - currentPlan.CreatedAt.Date).Days + 1;
        var targetDays = Math.Min(daysSinceStart, currentPlan.DurationDays);
        var studyDays = GetTotalStudyDays();

        return targetDays > 0 ? (double)studyDays / targetDays * 100 : 0;
    }

    // UI 辅助方法
    private string GetDifficultyBadgeClass(string difficulty)
    {
        return difficulty switch
        {
            "easy" => "bg-success",
            "medium" => "bg-warning",
            "hard" => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private string GetStatusBadgeClass(string status)
    {
        return status switch
        {
            "active" => "bg-primary",
            "paused" => "bg-warning",
            "completed" => "bg-success",
            "cancelled" => "bg-secondary",
            _ => "bg-secondary"
        };
    }

    private string GetRecordStatusBadgeClass(string status)
    {
        return status switch
        {
            "completed" => "bg-success",
            "partial" => "bg-warning",
            "skipped" => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private string GetRecordStatusDisplayName(string status)
    {
        return status switch
        {
            "completed" => "已完成",
            "partial" => "部分完成",
            "skipped" => "未学习",
            _ => "未知"
        };
    }

    private string GetMoodBadgeClass(int moodScore)
    {
        return moodScore switch
        {
            >= 8 => "bg-success",
            >= 6 => "bg-info",
            >= 4 => "bg-warning",
            _ => "bg-danger"
        };
    }

    private string GetHoursBadgeClass(double hours)
    {
        if (currentPlan == null) return "bg-secondary";

        var target = currentPlan.TargetHoursPerDay;
        return hours switch
        {
            var h when h >= target => "bg-success",
            var h when h >= target * 0.7 => "bg-warning",
            _ => "bg-danger"
        };
    }

    // 进度记录数据模型
    public class ProgressRecord
    {
        public DateTime Date { get; set; }
        public double StudyHours { get; set; }
        public string Status { get; set; } = "completed";
        public string Content { get; set; } = "";
        public int MoodScore { get; set; } = 8;
        public string Notes { get; set; } = "";
        public int PlanId { get; set; }
    }
}
