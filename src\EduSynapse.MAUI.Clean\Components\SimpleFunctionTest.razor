@page "/simple-function-test"
@using EduSynapse.MAUI.Services
@inject IStateService StateService
@inject StorageService StorageService

<div class="container-fluid p-4">
    <div class="row">
        <div class="col-12">
            <h1 class="text-primary mb-4">🧪 简化功能测试</h1>
            <p class="text-muted">基础功能验证</p>
        </div>
    </div>

    <!-- 基础 Blazor 事件测试 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>⚡ Blazor 事件测试</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <button class="btn btn-primary me-2" @onclick="IncrementCounter">
                            点击计数 (@clickCount)
                        </button>
                        
                        <button class="btn btn-outline-warning me-2" @onclick="ResetCounter">
                            重置
                        </button>
                        
                        <span class="badge @(clickCount > 0 ? "bg-success" : "bg-secondary")">
                            事件状态: @(clickCount > 0 ? "✅ 正常" : "⏳ 待测试")
                        </span>
                    </div>
                    
                    <div class="mb-3">
                        <input @bind="testInput" 
                               class="form-control" 
                               placeholder="测试双向绑定..." />
                    </div>
                    
                    @if (!string.IsNullOrEmpty(testInput))
                    {
                        <div class="alert alert-info">
                            双向绑定正常: @testInput
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- 服务注入测试 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>🔧 服务注入测试</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <button class="btn btn-info" @onclick="TestServices" disabled="@isTestingServices">
                            @if (isTestingServices)
                            {
                                <span>测试中...</span>
                            }
                            else
                            {
                                <span>测试服务注入</span>
                            }
                        </button>
                    </div>
                    
                    @if (!string.IsNullOrEmpty(serviceTestResult))
                    {
                        <div class="alert @(serviceTestSuccess ? "alert-success" : "alert-danger")">
                            @serviceTestResult
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- 测试结果 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>📊 测试结果</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <span class="badge @(clickCount > 0 ? "bg-success" : "bg-secondary") me-2">
                            Blazor 事件: @(clickCount > 0 ? "正常" : "待测试")
                        </span>
                        <span class="badge @(GetServiceTestBadgeClass()) me-2">
                            服务注入: @GetServiceTestStatus()
                        </span>
                    </div>
                    
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" style="width: @(GetOverallProgress())%" 
                             aria-valuenow="@GetOverallProgress()" aria-valuemin="0" aria-valuemax="100">
                            @GetOverallProgress().ToString("F0")%
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    // 基础测试状态
    private int clickCount = 0;
    private string testInput = "";
    
    // 服务测试状态
    private bool isTestingServices = false;
    private bool serviceTestSuccess = false;
    private string serviceTestResult = "";

    private void IncrementCounter()
    {
        try
        {
            clickCount++;
            System.Diagnostics.Debug.WriteLine($"✅ 计数增加到: {clickCount}");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ 增加计数失败: {ex}");
        }
    }

    private void ResetCounter()
    {
        try
        {
            clickCount = 0;
            System.Diagnostics.Debug.WriteLine("✅ 计数重置成功");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ 重置计数失败: {ex}");
        }
    }

    private async Task TestServices()
    {
        isTestingServices = true;
        serviceTestResult = "";

        try
        {
            // 测试存储服务
            var testKey = "test_key";
            var testValue = $"test_value_{DateTime.Now:HHmmss}";

            await StorageService.SaveAsync(testKey, testValue);
            var retrievedValue = await StorageService.LoadAsync<string>(testKey);

            if (retrievedValue == testValue)
            {
                serviceTestSuccess = true;
                serviceTestResult = $"✅ 存储服务正常 - 保存和读取数据成功: {testValue}";
                System.Diagnostics.Debug.WriteLine("✅ 存储服务测试成功");
            }
            else
            {
                serviceTestSuccess = false;
                serviceTestResult = "❌ 存储服务异常 - 数据读写不一致";
                System.Diagnostics.Debug.WriteLine("❌ 存储服务数据不一致");
            }
        }
        catch (Exception ex)
        {
            serviceTestSuccess = false;
            serviceTestResult = $"❌ 服务测试失败: {ex.Message}";
            System.Diagnostics.Debug.WriteLine($"❌ 服务测试失败: {ex}");
        }
        finally
        {
            isTestingServices = false;
        }
    }

    private string GetServiceTestBadgeClass()
    {
        if (string.IsNullOrEmpty(serviceTestResult))
            return "bg-secondary";
        return serviceTestSuccess ? "bg-success" : "bg-danger";
    }

    private string GetServiceTestStatus()
    {
        if (string.IsNullOrEmpty(serviceTestResult))
            return "待测试";
        return serviceTestSuccess ? "正常" : "失败";
    }

    private double GetOverallProgress()
    {
        int totalTests = 2;
        int passedTests = 0;
        
        if (clickCount > 0) passedTests++;
        if (!string.IsNullOrEmpty(serviceTestResult) && serviceTestSuccess) passedTests++;
        
        return (double)passedTests / totalTests * 100;
    }
}
