﻿using Microsoft.Extensions.Logging;
using MudBlazor.Services;
using EduSynapse.MAUI.Services;

namespace EduSynapse.MAUI;

public static class MauiProgram
{
    public static MauiApp CreateMauiApp()
    {
        var builder = MauiApp.CreateBuilder();
        builder
            .UseMauiApp<App>()
            .ConfigureFonts(fonts =>
            {
                fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
            });

        builder.Services.AddMauiBlazorWebView();

        // 添加 MudBlazor 服务
        builder.Services.AddMudServices();

        // 注册 HttpClient 和 HttpClientFactory
        builder.Services.AddHttpClient();
        builder.Services.AddHttpClient("EduSynapseAPI", client =>
        {
            // 配置 API 基础地址
            client.BaseAddress = new Uri("http://localhost:8000/");
            client.DefaultRequestHeaders.Add("Accept", "application/json");
        });

        // 逐步重新启用业务服务
        // builder.Services.AddScoped<IApiService, ApiService>();
        builder.Services.AddSingleton<IStateService, StateService>();
        builder.Services.AddSingleton<StorageService>();

        // 注册AI教师服务
        builder.Services.AddHttpClient<AITeacherService>();

#if DEBUG
        builder.Services.AddBlazorWebViewDeveloperTools();
        builder.Logging.AddDebug();
#endif

        return builder.Build();
    }
}
