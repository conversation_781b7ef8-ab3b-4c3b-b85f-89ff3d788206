<?xml version="1.0" encoding="UTF-8" ?>
<?xaml-stylesheet href="../Platform/Tizen/Styles.css"?>
<ResourceDictionary 
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml">

    <!-- Primary Colors -->
    <Color x:Key="Primary">#1976D2</Color>
    <Color x:Key="PrimaryDark">#1565C0</Color>
    <Color x:Key="PrimaryLight">#BBDEFB</Color>

    <!-- Secondary Colors -->
    <Color x:Key="Secondary">#DC004E</Color>
    <Color x:Key="SecondaryDark">#C51162</Color>
    <Color x:Key="SecondaryLight">#F8BBD0</Color>

    <!-- Accent Colors -->
    <Color x:Key="Tertiary">#2B0B98</Color>

    <!-- Background Colors -->
    <Color x:Key="White">White</Color>
    <Color x:Key="Black">Black</Color>
    <Color x:Key="Gray100">#E1E1E1</Color>
    <Color x:Key="Gray200">#C8C8C8</Color>
    <Color x:Key="Gray300">#ACACAC</Color>
    <Color x:Key="Gray400">#919191</Color>
    <Color x:Key="Gray500">#6E6E6E</Color>
    <Color x:Key="Gray600">#404040</Color>
    <Color x:Key="Gray900">#212121</Color>
    <Color x:Key="Gray950">#141414</Color>

    <!-- Status Colors -->
    <Color x:Key="Success">#4CAF50</Color>
    <Color x:Key="Warning">#FF9800</Color>
    <Color x:Key="Error">#F44336</Color>
    <Color x:Key="Info">#2196F3</Color>

    <!-- Light Theme -->
    <Color x:Key="LightBackground">#FAFAFA</Color>
    <Color x:Key="LightSurface">#FFFFFF</Color>
    <Color x:Key="LightOnSurface">#212121</Color>

    <!-- Dark Theme -->
    <Color x:Key="DarkBackground">#121212</Color>
    <Color x:Key="DarkSurface">#1E1E1E</Color>
    <Color x:Key="DarkOnSurface">#FFFFFF</Color>

</ResourceDictionary>
