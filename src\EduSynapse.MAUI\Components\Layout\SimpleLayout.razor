@using MudBlazor
@using Microsoft.AspNetCore.Components
@inherits LayoutComponentBase

<MudLayout>
    <MudAppBar Elevation="1">
        <MudIconButton Icon="@Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start" OnClick="@ToggleDrawer" />
        <MudSpacer />
        <MudText Typo="Typo.h6">EduSynapse (Simple Mode)</MudText>
        <MudSpacer />
        
        <!-- 简化的状态指示器 -->
        <MudIcon Icon="@Icons.Material.Filled.CloudDone" Color="Color.Primary" />
    </MudAppBar>

    <MudDrawer Open="_drawerOpen"
               OpenChanged="@((bool val) => _drawerOpen = val)"
               Elevation="1"
               Variant="@DrawerVariant.Responsive"
               ClipMode="DrawerClipMode.Always">
        <MudDrawerHeader>
            <MudText Typo="Typo.h6">导航菜单</MudText>
        </MudDrawerHeader>
        <MudNavMenu>
            <MudNavLink Href="/" Match="NavLinkMatch.All" Icon="@Icons.Material.Filled.Home">首页</MudNavLink>
            <MudNavLink Href="/learning-plan" Icon="@Icons.Material.Filled.School">学习计划</MudNavLink>
            <MudNavLink Href="/progress" Icon="@Icons.Material.Filled.Analytics">学习进度</MudNavLink>
        </MudNavMenu>
    </MudDrawer>

    <MudMainContent>
        <MudContainer MaxWidth="MaxWidth.Large" Class="my-4">
            <!-- 页面内容 -->
            <div class="fade-in">
                @ChildContent
            </div>
        </MudContainer>
    </MudMainContent>
</MudLayout>



@code {
    [Parameter] public RenderFragment? ChildContent { get; set; }
    
    private bool _drawerOpen = true;
    
    protected override async Task OnInitializedAsync()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("SimpleLayout: OnInitializedAsync started");
            await Task.Delay(1); // 模拟异步操作
            System.Diagnostics.Debug.WriteLine("SimpleLayout: OnInitializedAsync completed");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"SimpleLayout OnInitializedAsync error: {ex}");
        }
    }

    private void ToggleDrawer()
    {
        try
        {
            _drawerOpen = !_drawerOpen;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"SimpleLayout ToggleDrawer error: {ex}");
        }
    }
}
