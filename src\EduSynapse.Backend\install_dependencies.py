#!/usr/bin/env python3
"""
EduSynapse Backend Dependencies Installer
智能依赖包安装脚本 - 处理版本兼容性问题
"""

import subprocess
import sys
import os
from typing import List, Tuple, Dict


def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"🐍 Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        return False
    
    if version.major == 3 and version.minor >= 12:
        print("⚠️  注意: Python 3.12+，某些包可能需要特殊处理")
        return True, "3.12+"
    
    return True, "standard"


def install_package(package: str, description: str = "") -> bool:
    """安装单个包"""
    print(f"📦 安装 {package}...")
    if description:
        print(f"   {description}")
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ {package} 安装成功")
            return True
        else:
            print(f"❌ {package} 安装失败:")
            print(f"   {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ {package} 安装超时")
        return False
    except Exception as e:
        print(f"❌ {package} 安装异常: {e}")
        return False


def install_dependencies_by_category():
    """按类别安装依赖包"""
    
    # 基础Web框架 (必需)
    basic_packages = [
        ("fastapi>=0.104.0", "FastAPI Web框架"),
        ("uvicorn[standard]>=0.24.0", "ASGI服务器"),
        ("pydantic>=2.5.0", "数据验证"),
        ("pydantic-settings>=2.1.0", "配置管理"),
    ]
    
    # 数据库相关 (必需)
    database_packages = [
        ("sqlalchemy>=2.0.0", "数据库ORM"),
        ("alembic>=1.13.0", "数据库迁移"),
    ]
    
    # 基础工具 (必需)
    utility_packages = [
        ("python-multipart>=0.0.6", "文件上传支持"),
        ("python-jose[cryptography]>=3.3.0", "JWT认证"),
        ("passlib[bcrypt]>=1.7.4", "密码加密"),
        ("aiofiles>=23.2.0", "异步文件操作"),
        ("python-dotenv>=1.0.0", "环境变量"),
        ("httpx>=0.25.0", "HTTP客户端"),
    ]
    
    # AI相关包 (可选，但推荐)
    ai_packages = [
        ("openai>=1.6.0", "OpenAI API客户端"),
        ("anthropic>=0.8.0", "Anthropic API客户端"),
        ("tiktoken>=0.5.0", "Token计算"),
    ]
    
    # LangChain相关 (可选)
    langchain_packages = [
        ("langchain>=0.1.0", "LangChain核心"),
        ("langchain-openai>=0.0.2", "LangChain OpenAI集成"),
        ("langchain-community>=0.0.10", "LangChain社区包"),
    ]
    
    # AutoGen (可选，版本敏感)
    autogen_packages = [
        ("pyautogen>=0.2.7", "多代理对话框架"),
    ]
    
    # 数据处理 (可选)
    data_packages = [
        ("pandas>=2.0.0", "数据处理"),
        ("numpy>=1.24.0", "数值计算"),
        ("scikit-learn>=1.3.0", "机器学习"),
    ]
    
    # 文档处理 (可选)
    document_packages = [
        ("pypdf2>=3.0.0", "PDF处理"),
        ("python-docx>=1.0.0", "Word文档处理"),
    ]
    
    # 监控和日志 (可选)
    monitoring_packages = [
        ("structlog>=23.0.0", "结构化日志"),
    ]
    
    # 开发工具 (可选)
    dev_packages = [
        ("pytest>=7.4.0", "测试框架"),
        ("pytest-asyncio>=0.21.0", "异步测试"),
        ("black>=23.11.0", "代码格式化"),
        ("pylint>=3.0.0", "代码检查"),
    ]
    
    # 安装顺序和结果跟踪
    install_categories = [
        ("基础Web框架", basic_packages, True),  # 必需
        ("数据库支持", database_packages, True),  # 必需
        ("基础工具", utility_packages, True),  # 必需
        ("AI服务", ai_packages, False),  # 可选
        ("LangChain", langchain_packages, False),  # 可选
        ("AutoGen", autogen_packages, False),  # 可选
        ("数据处理", data_packages, False),  # 可选
        ("文档处理", document_packages, False),  # 可选
        ("监控日志", monitoring_packages, False),  # 可选
        ("开发工具", dev_packages, False),  # 可选
    ]
    
    results = {}
    
    for category_name, packages, required in install_categories:
        print(f"\n{'='*50}")
        print(f"📂 安装类别: {category_name}")
        print(f"{'='*50}")
        
        category_results = []
        for package, description in packages:
            success = install_package(package, description)
            category_results.append((package, success))
            
            if not success and required:
                print(f"❌ 必需包 {package} 安装失败，可能影响系统运行")
        
        results[category_name] = category_results
        
        # 统计本类别结果
        successful = sum(1 for _, success in category_results if success)
        total = len(category_results)
        print(f"📊 {category_name}: {successful}/{total} 个包安装成功")
    
    return results


def print_installation_summary(results: Dict):
    """打印安装总结"""
    print(f"\n{'='*60}")
    print("📋 安装总结")
    print(f"{'='*60}")
    
    total_packages = 0
    successful_packages = 0
    
    for category, packages in results.items():
        successful = sum(1 for _, success in packages if success)
        total = len(packages)
        total_packages += total
        successful_packages += successful
        
        status = "✅" if successful == total else "⚠️" if successful > 0 else "❌"
        print(f"{status} {category}: {successful}/{total}")
        
        # 显示失败的包
        failed = [pkg for pkg, success in packages if not success]
        if failed:
            print(f"   失败: {', '.join(failed)}")
    
    print(f"\n📊 总计: {successful_packages}/{total_packages} 个包安装成功 ({successful_packages/total_packages*100:.1f}%)")
    
    # 给出建议
    if successful_packages == total_packages:
        print("🎉 所有包安装成功！EduSynapse后端已准备就绪！")
    elif successful_packages >= total_packages * 0.8:
        print("✅ 大部分包安装成功，系统可以正常运行")
        print("💡 部分AI功能可能不可用，但系统会自动切换到备用模式")
    else:
        print("⚠️  多个包安装失败，请检查网络连接和Python环境")
    
    print(f"\n🚀 下一步:")
    print("   1. 配置环境变量 (.env文件)")
    print("   2. 运行: python main.py")
    print("   3. 访问: http://localhost:8000")


def main():
    """主函数"""
    print("🎓 EduSynapse Backend Dependencies Installer")
    print("=" * 60)
    
    # 检查Python版本
    version_check = check_python_version()
    if isinstance(version_check, tuple):
        version_ok, version_type = version_check
    else:
        version_ok = version_check
        version_type = "unknown"
    
    if not version_ok:
        sys.exit(1)
    
    # 升级pip
    print("\n📦 升级pip...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        print("✅ pip升级成功")
    except subprocess.CalledProcessError:
        print("⚠️  pip升级失败，继续安装...")
    
    # 安装依赖
    print(f"\n🚀 开始安装依赖包...")
    results = install_dependencies_by_category()
    
    # 打印总结
    print_installation_summary(results)


if __name__ == "__main__":
    main()
