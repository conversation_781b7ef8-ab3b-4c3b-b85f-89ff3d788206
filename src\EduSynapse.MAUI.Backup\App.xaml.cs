using System.Diagnostics;

namespace EduSynapse.MAUI;

public partial class App : Application
{
    public App()
    {
        try
        {
            InitializeComponent();

            // 添加全局异常处理
            AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
            TaskScheduler.UnobservedTaskException += OnUnobservedTaskException;

            // 🧪 第一阶段：使用最纯净的页面
            MainPage = new PurePage();

            Debug.WriteLine("App initialized successfully");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"App initialization failed: {ex}");

            // 显示错误信息给用户
            MainPage = new ContentPage
            {
                Content = new Label
                {
                    Text = $"应用启动失败: {ex.Message}",
                    HorizontalOptions = LayoutOptions.Center,
                    VerticalOptions = LayoutOptions.Center
                }
            };
        }
    }

    private void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        var exception = e.ExceptionObject as Exception;
        var detailedMessage = $"CRITICAL UNHANDLED EXCEPTION:\n" +
                             $"Type: {exception?.GetType().FullName}\n" +
                             $"Message: {exception?.Message}\n" +
                             $"Stack Trace: {exception?.StackTrace}\n" +
                             $"Inner Exception: {exception?.InnerException}\n" +
                             $"Is Terminating: {e.IsTerminating}";

        Debug.WriteLine(detailedMessage);
        Console.WriteLine(detailedMessage);

        // 记录到文件
        LogException(exception, "UnhandledException");

        // 尝试显示错误对话框
        try
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                Application.Current.MainPage?.DisplayAlert("Critical Error",
                    $"Application encountered a critical error:\n{exception?.Message}", "OK");
            });
        }
        catch
        {
            // 忽略显示错误
        }
    }

    private void OnUnobservedTaskException(object sender, UnobservedTaskExceptionEventArgs e)
    {
        Debug.WriteLine($"Unobserved task exception: {e.Exception}");

        // 记录异常
        LogException(e.Exception, "UnobservedTaskException");

        // 标记为已处理，防止应用崩溃
        e.SetObserved();
    }

    private void LogException(Exception exception, string source)
    {
        try
        {
            var logMessage = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {source}: {exception}";
            Debug.WriteLine(logMessage);

            // 可以添加文件日志记录
            var logPath = Path.Combine(FileSystem.AppDataDirectory, "error.log");
            File.AppendAllText(logPath, logMessage + Environment.NewLine);
        }
        catch
        {
            // 忽略日志记录错误
        }
    }
}
