@page "/function-test"
@using EduSynapse.MAUI.Services
@using EduSynapse.MAUI.ViewModels
@inject IApiService ApiService
@inject IStateService StateService
@inject StorageService StorageService

<div class="container-fluid p-4">
    <div class="row">
        <div class="col-12">
            <h1 class="text-primary mb-4">🧪 EduSynapse 功能测试中心</h1>
            <p class="text-muted">测试所有核心功能是否正常工作</p>
        </div>
    </div>

    <!-- API 连接测试 -->
    <div class="row mb-4">
        <div class="col-12">
            <MudCard>
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">🌐 API 连接测试</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <div class="d-flex align-items-center mb-3">
                        <MudButton Variant="Variant.Filled" 
                                   Color="Color.Primary" 
                                   OnClick="TestApiConnection"
                                   Disabled="@isTestingApi">
                            @if (isTestingApi)
                            {
                                <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
                                <span>测试中...</span>
                            }
                            else
                            {
                                <span>测试 API 连接</span>
                            }
                        </MudButton>
                        
                        @if (!string.IsNullOrEmpty(apiTestResult))
                        {
                            <MudChip T="string" Color="@(apiConnected ? Color.Success : Color.Error)" 
                                     Variant="Variant.Filled" Class="ml-3">
                                @apiTestResult
                            </MudChip>
                        }
                    </div>
                    
                    @if (!string.IsNullOrEmpty(apiDetails))
                    {
                        <MudAlert Severity="@(apiConnected ? Severity.Success : Severity.Error)">
                            @apiDetails
                        </MudAlert>
                    }
                </MudCardContent>
            </MudCard>
        </div>
    </div>

    <!-- Blazor 事件测试 -->
    <div class="row mb-4">
        <div class="col-12">
            <MudCard>
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">⚡ Blazor 事件测试</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <div class="d-flex align-items-center mb-3">
                        <MudButton Variant="Variant.Filled" 
                                   Color="Color.Secondary" 
                                   OnClick="IncrementCounter">
                            点击计数 (@clickCount)
                        </MudButton>
                        
                        <MudButton Variant="Variant.Outlined" 
                                   Color="Color.Warning" 
                                   OnClick="ResetCounter"
                                   Class="ml-2">
                            重置
                        </MudButton>
                        
                        <MudChip T="string" Color="@(clickCount > 0 ? Color.Success : Color.Default)" 
                                 Class="ml-3">
                            事件状态: @(clickCount > 0 ? "✅ 正常" : "⏳ 待测试")
                        </MudChip>
                    </div>
                    
                    <MudTextField @bind-Value="testInput" 
                                  Label="输入测试" 
                                  Placeholder="测试双向绑定..."
                                  Variant="Variant.Outlined"
                                  Class="mb-2" />
                    
                    @if (!string.IsNullOrEmpty(testInput))
                    {
                        <MudAlert Severity="Severity.Info">
                            双向绑定正常: @testInput
                        </MudAlert>
                    }
                </MudCardContent>
            </MudCard>
        </div>
    </div>

    <!-- 存储服务测试 -->
    <div class="row mb-4">
        <div class="col-12">
            <MudCard>
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">💾 存储服务测试</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <div class="d-flex align-items-center mb-3">
                        <MudButton Variant="Variant.Filled" 
                                   Color="Color.Info" 
                                   OnClick="TestStorage"
                                   Disabled="@isTestingStorage">
                            @if (isTestingStorage)
                            {
                                <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
                                <span>测试中...</span>
                            }
                            else
                            {
                                <span>测试本地存储</span>
                            }
                        </MudButton>
                        
                        @if (!string.IsNullOrEmpty(storageTestResult))
                        {
                            <MudChip T="string" Color="@(storageWorking ? Color.Success : Color.Error)" 
                                     Variant="Variant.Filled" Class="ml-3">
                                @storageTestResult
                            </MudChip>
                        }
                    </div>
                    
                    @if (!string.IsNullOrEmpty(storageDetails))
                    {
                        <MudAlert Severity="@(storageWorking ? Severity.Success : Severity.Error)">
                            @storageDetails
                        </MudAlert>
                    }
                </MudCardContent>
            </MudCard>
        </div>
    </div>

    <!-- MudBlazor 组件测试 -->
    <div class="row mb-4">
        <div class="col-12">
            <MudCard>
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">🎨 MudBlazor 组件测试</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <div class="row">
                        <div class="col-md-6">
                            <MudSelect T="string" @bind-Value="selectedOption" Label="下拉选择测试" Variant="Variant.Outlined">
                                <MudSelectItem Value="@("option1")">选项 1</MudSelectItem>
                                <MudSelectItem Value="@("option2")">选项 2</MudSelectItem>
                                <MudSelectItem Value="@("option3")">选项 3</MudSelectItem>
                            </MudSelect>
                        </div>
                        <div class="col-md-6">
                            <MudSlider @bind-Value="sliderValue" Min="0" Max="100" Step="1" />
                            <MudText Typo="Typo.body2">滑块值: @sliderValue</MudText>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <MudSwitch T="bool" @bind-Checked="switchValue" Label="开关测试" Color="Color.Primary" />
                        <MudCheckBox T="bool" @bind-Checked="checkboxValue" Label="复选框测试" Color="Color.Secondary" />
                    </div>
                    
                    @if (!string.IsNullOrEmpty(selectedOption) || sliderValue > 0 || switchValue || checkboxValue)
                    {
                        <MudAlert Severity="Severity.Success" Class="mt-3">
                            MudBlazor 组件工作正常！
                            选择: @selectedOption, 滑块: @sliderValue, 开关: @switchValue, 复选框: @checkboxValue
                        </MudAlert>
                    }
                </MudCardContent>
            </MudCard>
        </div>
    </div>

    <!-- 测试结果总览 -->
    <div class="row">
        <div class="col-12">
            <MudCard>
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">📊 测试结果总览</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <div class="d-flex flex-wrap gap-2">
                        <MudChip T="string" Color="@GetTestStatusColor("blazor")" Variant="Variant.Filled">
                            Blazor 事件: @GetTestStatus("blazor")
                        </MudChip>
                        <MudChip T="string" Color="@GetTestStatusColor("api")" Variant="Variant.Filled">
                            API 连接: @GetTestStatus("api")
                        </MudChip>
                        <MudChip T="string" Color="@GetTestStatusColor("storage")" Variant="Variant.Filled">
                            本地存储: @GetTestStatus("storage")
                        </MudChip>
                        <MudChip T="string" Color="@GetTestStatusColor("mudblazor")" Variant="Variant.Filled">
                            MudBlazor: @GetTestStatus("mudblazor")
                        </MudChip>
                    </div>
                    
                    <div class="mt-3">
                        <MudProgressLinear Value="@GetOverallProgress()" Color="Color.Primary" />
                        <MudText Typo="Typo.body2" Class="mt-1">
                            整体进度: @GetOverallProgress().ToString("F0")%
                        </MudText>
                    </div>
                </MudCardContent>
            </MudCard>
        </div>
    </div>
</div>

@code {
    // 测试状态
    private bool isTestingApi = false;
    private bool isTestingStorage = false;
    
    // API 测试
    private bool apiConnected = false;
    private string apiTestResult = "";
    private string apiDetails = "";
    
    // Blazor 事件测试
    private int clickCount = 0;
    private string testInput = "";
    
    // 存储测试
    private bool storageWorking = false;
    private string storageTestResult = "";
    private string storageDetails = "";
    
    // MudBlazor 组件测试
    private string selectedOption = "";
    private int sliderValue = 0;
    private bool switchValue = false;
    private bool checkboxValue = false;

    private async Task TestApiConnection()
    {
        isTestingApi = true;
        apiTestResult = "";
        apiDetails = "";

        try
        {
            var result = await ApiService.CheckHealthAsync();
            apiConnected = result != null && result.Status == "healthy";
            apiTestResult = apiConnected ? "✅ 连接成功" : "❌ 连接失败";
            apiDetails = apiConnected ? $"API 服务响应正常 - 版本: {result?.Version}, 数据库: {result?.Database}" : "无法连接到后端服务 (http://localhost:8000)";
        }
        catch (Exception ex)
        {
            apiConnected = false;
            apiTestResult = "❌ 连接异常";
            apiDetails = $"错误: {ex.Message}";
        }
        finally
        {
            isTestingApi = false;
        }
    }

    private void IncrementCounter()
    {
        clickCount++;
    }

    private void ResetCounter()
    {
        clickCount = 0;
    }

    private async Task TestStorage()
    {
        isTestingStorage = true;
        storageTestResult = "";
        storageDetails = "";
        
        try
        {
            var testKey = "test_key";
            var testValue = $"test_value_{DateTime.Now:HHmmss}";
            
            // 测试写入
            await StorageService.SaveAsync(testKey, testValue);
            
            // 测试读取
            var retrievedValue = await StorageService.LoadAsync<string>(testKey);
            
            if (retrievedValue == testValue)
            {
                storageWorking = true;
                storageTestResult = "✅ 存储正常";
                storageDetails = $"成功保存和读取数据: {testValue}";
            }
            else
            {
                storageWorking = false;
                storageTestResult = "❌ 存储异常";
                storageDetails = "数据读写不一致";
            }
        }
        catch (Exception ex)
        {
            storageWorking = false;
            storageTestResult = "❌ 存储失败";
            storageDetails = $"错误: {ex.Message}";
        }
        finally
        {
            isTestingStorage = false;
        }
    }

    private Color GetTestStatusColor(string testType)
    {
        return testType switch
        {
            "blazor" => clickCount > 0 ? Color.Success : Color.Default,
            "api" => !string.IsNullOrEmpty(apiTestResult) ? (apiConnected ? Color.Success : Color.Error) : Color.Default,
            "storage" => !string.IsNullOrEmpty(storageTestResult) ? (storageWorking ? Color.Success : Color.Error) : Color.Default,
            "mudblazor" => (selectedOption != "" || sliderValue > 0 || switchValue || checkboxValue) ? Color.Success : Color.Default,
            _ => Color.Default
        };
    }

    private string GetTestStatus(string testType)
    {
        return testType switch
        {
            "blazor" => clickCount > 0 ? "正常" : "待测试",
            "api" => !string.IsNullOrEmpty(apiTestResult) ? (apiConnected ? "正常" : "失败") : "待测试",
            "storage" => !string.IsNullOrEmpty(storageTestResult) ? (storageWorking ? "正常" : "失败") : "待测试",
            "mudblazor" => (selectedOption != "" || sliderValue > 0 || switchValue || checkboxValue) ? "正常" : "待测试",
            _ => "未知"
        };
    }

    private double GetOverallProgress()
    {
        int totalTests = 4;
        int passedTests = 0;
        
        if (clickCount > 0) passedTests++;
        if (!string.IsNullOrEmpty(apiTestResult) && apiConnected) passedTests++;
        if (!string.IsNullOrEmpty(storageTestResult) && storageWorking) passedTests++;
        if (selectedOption != "" || sliderValue > 0 || switchValue || checkboxValue) passedTests++;
        
        return (double)passedTests / totalTests * 100;
    }
}
