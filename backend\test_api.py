#!/usr/bin/env python3
"""
EduSynapse AI Backend API测试脚本
快速验证API功能的测试工具
"""

import asyncio
import json
import time
from typing import Dict, Any
import httpx
from datetime import datetime, timedelta


class EduSynapseAPITester:
    """EduSynapse API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=30.0)
        self.test_results = []
    
    async def test_health_check(self) -> bool:
        """测试健康检查"""
        print("🔍 测试健康检查...")
        try:
            response = await self.client.get(f"{self.base_url}/health")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 健康检查通过: {data}")
                return True
            else:
                print(f"❌ 健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 健康检查异常: {e}")
            return False
    
    async def test_generate_plan(self) -> Dict[str, Any]:
        """测试学习计划生成"""
        print("\n🚀 测试学习计划生成...")
        
        request_data = {
            "topic": "Python编程基础",
            "difficulty_level": "beginner",
            "daily_hours": 2.0,
            "duration_days": 7,
            "learning_style": "balanced",
            "focus_areas": ["语法基础", "数据结构", "函数编程"],
            "preferred_teacher_type": "case_driven"
        }
        
        try:
            start_time = time.time()
            response = await self.client.post(
                f"{self.base_url}/api/v1/teaching/generate-plan",
                json=request_data
            )
            processing_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 计划生成成功 (耗时: {processing_time:.2f}s)")
                print(f"   计划ID: {data.get('plan_id')}")
                print(f"   主题: {data.get('plan', {}).get('topic')}")
                print(f"   天数: {data.get('plan', {}).get('duration_days')}")
                return data
            else:
                print(f"❌ 计划生成失败: {response.status_code}")
                print(f"   错误: {response.text}")
                return {}
                
        except Exception as e:
            print(f"❌ 计划生成异常: {e}")
            return {}
    
    async def test_start_session(self) -> Dict[str, Any]:
        """测试开始教学会话"""
        print("\n🎭 测试开始教学会话...")
        
        request_data = {
            "student_id": "test_student_001",
            "topic": "Python变量和数据类型",
            "difficulty_level": "beginner",
            "daily_hours": 2.0,
            "learning_style": "balanced",
            "preferred_teacher_type": "socratic"
        }
        
        try:
            response = await self.client.post(
                f"{self.base_url}/api/v1/teaching/start-session",
                json=request_data
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 会话启动成功")
                print(f"   会话ID: {data.get('session_id')}")
                print(f"   当前教师: {data.get('current_teacher')}")
                print(f"   当前阶段: {data.get('current_stage')}")
                print(f"   欢迎消息: {data.get('welcome_message', '')[:100]}...")
                return data
            else:
                print(f"❌ 会话启动失败: {response.status_code}")
                print(f"   错误: {response.text}")
                return {}
                
        except Exception as e:
            print(f"❌ 会话启动异常: {e}")
            return {}
    
    async def test_continue_session(self, student_id: str = "test_student_001") -> Dict[str, Any]:
        """测试继续教学会话"""
        print("\n💬 测试继续教学会话...")
        
        test_questions = [
            "什么是变量？",
            "Python有哪些基本数据类型？",
            "如何定义一个字符串变量？",
            "整数和浮点数有什么区别？"
        ]
        
        results = []
        for question in test_questions:
            request_data = {
                "student_id": student_id,
                "user_input": question
            }
            
            try:
                start_time = time.time()
                response = await self.client.post(
                    f"{self.base_url}/api/v1/teaching/continue-session",
                    json=request_data
                )
                processing_time = time.time() - start_time
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ 问题: {question}")
                    print(f"   AI回复: {data.get('ai_response', '')[:150]}...")
                    print(f"   教师类型: {data.get('current_teacher')}")
                    print(f"   处理时间: {processing_time:.2f}s")
                    results.append(data)
                else:
                    print(f"❌ 对话失败: {response.status_code}")
                    print(f"   问题: {question}")
                    print(f"   错误: {response.text}")
                
                # 避免请求过快
                await asyncio.sleep(1)
                
            except Exception as e:
                print(f"❌ 对话异常: {e}")
                print(f"   问题: {question}")
        
        return results
    
    async def test_track_progress(self, student_id: str = "test_student_001") -> Dict[str, Any]:
        """测试进度跟踪"""
        print("\n📊 测试进度跟踪...")
        
        # 模拟学习数据
        learning_data = {
            "wwh_scores": {
                "what": 0.8,
                "why": 0.7,
                "how": 0.6
            },
            "concept_assessments": [
                {
                    "concept": "变量定义",
                    "score": 0.85,
                    "timestamp": datetime.now().isoformat(),
                    "answer_quality": 0.8,
                    "explanation_depth": 0.7,
                    "connection_ability": 0.9
                },
                {
                    "concept": "数据类型",
                    "score": 0.75,
                    "timestamp": (datetime.now() - timedelta(hours=1)).isoformat(),
                    "answer_quality": 0.7,
                    "explanation_depth": 0.8,
                    "connection_ability": 0.7
                }
            ],
            "code_submissions": [
                {
                    "code": "name = 'Alice'\nage = 25\nprint(f'Hello, {name}! You are {age} years old.')",
                    "timestamp": datetime.now().isoformat(),
                    "status": "success"
                },
                {
                    "code": "numbers = [1, 2, 3, 4, 5]\nfor num in numbers:\n    print(num * 2)",
                    "timestamp": (datetime.now() - timedelta(minutes=30)).isoformat(),
                    "status": "success"
                }
            ],
            "practice_tasks": [
                {"id": "task_1", "status": "completed", "score": 0.9},
                {"id": "task_2", "status": "completed", "score": 0.8},
                {"id": "task_3", "status": "in_progress", "score": 0.0}
            ],
            "error_history": [
                {
                    "message": "NameError: name 'x' is not defined",
                    "code": "print(x)",
                    "timestamp": (datetime.now() - timedelta(hours=2)).isoformat(),
                    "type": "runtime_error"
                }
            ],
            "learning_sessions": [],
            "assessment_results": []
        }
        
        request_data = {
            "student_id": student_id,
            "learning_data": learning_data
        }
        
        try:
            start_time = time.time()
            response = await self.client.post(
                f"{self.base_url}/api/v1/progress/track",
                json=request_data
            )
            processing_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                metrics = data.get('metrics', {})
                insights = data.get('insights', {})
                
                print(f"✅ 进度跟踪成功 (耗时: {processing_time:.2f}s)")
                print(f"   综合评分: {metrics.get('overall_score', 0):.3f}")
                print(f"   知识掌握度: {metrics.get('knowledge_mastery', 0):.3f}")
                print(f"   实践完成度: {metrics.get('practice_completion', 0):.3f}")
                print(f"   时间投入效率: {metrics.get('time_investment', 0):.3f}")
                print(f"   错误改善度: {metrics.get('error_pattern_score', 0):.3f}")
                print(f"   能力发展指数: {metrics.get('ability_development', 0):.3f}")
                
                recommendations = data.get('recommendations', [])
                if recommendations:
                    print(f"   改进建议: {recommendations[0]}")
                
                return data
            else:
                print(f"❌ 进度跟踪失败: {response.status_code}")
                print(f"   错误: {response.text}")
                return {}
                
        except Exception as e:
            print(f"❌ 进度跟踪异常: {e}")
            return {}
    
    async def test_get_teachers(self) -> bool:
        """测试获取教师列表"""
        print("\n👥 测试获取教师列表...")
        
        try:
            response = await self.client.get(f"{self.base_url}/api/v1/teaching/teachers")
            
            if response.status_code == 200:
                data = response.json()
                teachers = data.get('teachers', [])
                print(f"✅ 获取教师列表成功，共 {len(teachers)} 个教师:")
                for teacher in teachers:
                    print(f"   - {teacher.get('name')}: {teacher.get('description')}")
                return True
            else:
                print(f"❌ 获取教师列表失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 获取教师列表异常: {e}")
            return False
    
    async def test_get_dimensions(self) -> bool:
        """测试获取进度维度"""
        print("\n📏 测试获取进度维度...")
        
        try:
            response = await self.client.get(f"{self.base_url}/api/v1/progress/dimensions")
            
            if response.status_code == 200:
                data = response.json()
                dimensions = data.get('dimensions', [])
                print(f"✅ 获取进度维度成功，共 {len(dimensions)} 个维度:")
                for dim in dimensions:
                    print(f"   - {dim.get('display_name')}: {dim.get('description')} (权重: {dim.get('weight')})")
                return True
            else:
                print(f"❌ 获取进度维度失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 获取进度维度异常: {e}")
            return False
    
    async def run_all_tests(self) -> Dict[str, bool]:
        """运行所有测试"""
        print("🧪 开始EduSynapse AI Backend API测试")
        print("=" * 60)
        
        results = {}
        
        # 1. 健康检查
        results['health_check'] = await self.test_health_check()
        
        # 2. 获取基础信息
        results['get_teachers'] = await self.test_get_teachers()
        results['get_dimensions'] = await self.test_get_dimensions()
        
        # 3. 核心功能测试
        plan_result = await self.test_generate_plan()
        results['generate_plan'] = bool(plan_result)
        
        session_result = await self.test_start_session()
        results['start_session'] = bool(session_result)
        
        if session_result:
            chat_results = await self.test_continue_session()
            results['continue_session'] = bool(chat_results)
        else:
            results['continue_session'] = False
        
        progress_result = await self.test_track_progress()
        results['track_progress'] = bool(progress_result)
        
        # 输出测试总结
        print("\n" + "=" * 60)
        print("🎯 测试结果总结:")
        print("=" * 60)
        
        passed = sum(results.values())
        total = len(results)
        
        for test_name, passed_test in results.items():
            status = "✅ 通过" if passed_test else "❌ 失败"
            print(f"{test_name:20} : {status}")
        
        print("-" * 60)
        print(f"总计: {passed}/{total} 个测试通过 ({passed/total*100:.1f}%)")
        
        if passed == total:
            print("🎉 所有测试通过！EduSynapse AI Backend运行正常！")
        else:
            print("⚠️  部分测试失败，请检查服务器状态和配置")
        
        await self.client.aclose()
        return results


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="EduSynapse AI Backend API测试工具")
    parser.add_argument("--url", default="http://localhost:8000", help="API服务器地址")
    parser.add_argument("--test", choices=["all", "health", "plan", "session", "progress"], 
                       default="all", help="要运行的测试类型")
    
    args = parser.parse_args()
    
    tester = EduSynapseAPITester(args.url)
    
    if args.test == "all":
        await tester.run_all_tests()
    elif args.test == "health":
        await tester.test_health_check()
    elif args.test == "plan":
        await tester.test_generate_plan()
    elif args.test == "session":
        await tester.test_start_session()
        await tester.test_continue_session()
    elif args.test == "progress":
        await tester.test_track_progress()


if __name__ == "__main__":
    asyncio.run(main())
