# 🎓 EduSynapse AI智能教学系统后端

> **基于LangChain和AutoGen的智能教学系统后端服务**

EduSynapse AI智能教学系统后端，集成了WWH教学框架、多AI教师协作和智能学习计划生成功能。

## 🚀 快速开始

## 🌟 核心AI功能

### 🤖 AI智能教学引擎
- **多AI教师协作** - 苏格拉底式、案例驱动、游戏化三种教学风格
- **WWH教学框架** - What(是什么)、Why(为什么)、How(怎么做)结构化教学
- **动态教学策略** - 根据学习进度自动调整教学方法
- **智能对话系统** - 基于LangChain的自然语言交互

### 📊 智能学习分析
- **个性化学习计划** - AI生成的结构化学习路径
- **学习进度跟踪** - 多维度学习数据分析
- **错误模式识别** - 智能识别和改善学习问题

### 1. 环境要求

- Python 3.11+
- OpenAI API 密钥 (推荐，用于完整AI功能)
- 备用模式：无API密钥时自动切换到基础功能

### 2. 安装和启动

#### Windows 用户
```bash
# 双击运行启动脚本
start.bat
```

#### 手动安装
```bash
# 1. 创建虚拟环境
python -m venv venv

# 2. 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate

# 3. 安装依赖
pip install -r requirements.txt

# 4. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，添加您的 API 密钥

# 5. 初始化数据库
python scripts/init_database.py

# 6. 启动服务
python main.py
```

### 3. 配置 API 密钥

编辑 `.env` 文件：

```env
# OpenAI API 配置
OPENAI_API_BASE=https://api.openai.com/v1  # 可自定义 API 端点
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_MODEL=gpt-4

# 或者使用 Anthropic Claude
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# 第三方 API 示例 (如 DeepBricks)
# OPENAI_API_BASE=https://api.deepbricks.ai/v1/
# OPENAI_API_KEY=sk-your-deepbricks-key
```

## 📡 API 文档

启动服务后，访问以下地址：

- **API 文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health
- **根路径**: http://localhost:8000/

## 🧪 测试 API

```bash
# 运行 API 测试脚本
python scripts/test_api.py
```

## 📊 核心功能

### 1. WWH 学习计划生成

```bash
POST /api/learning-plans/generate
```

**请求示例:**
```json
{
    "topic": "Python面向对象编程",
    "duration_days": 14,
    "difficulty_level": "medium",
    "daily_hours": 2.5,
    "learning_style": "practical"
}
```

### 2. 学习进度跟踪

```bash
POST /api/learning-plans/{plan_id}/progress
```

**请求示例:**
```json
{
    "day_number": 1,
    "what_mastery": 85.0,
    "why_mastery": 70.0,
    "how_mastery": 60.0,
    "time_spent": 150,
    "notes": "今天学习了基础概念..."
}
```

### 3. 计划管理

- `GET /api/learning-plans/` - 获取计划列表
- `GET /api/learning-plans/{id}` - 获取计划详情
- `PUT /api/learning-plans/{id}` - 更新计划
- `DELETE /api/learning-plans/{id}` - 删除计划

## 🏗️ 项目结构

```
src/EduSynapse.Backend/
├── app/
│   ├── api/                 # API 路由
│   ├── core/                # 核心配置
│   ├── database/            # 数据库配置
│   ├── models/              # 数据模型
│   ├── schemas/             # 数据传输对象
│   └── services/            # 业务服务
├── scripts/                 # 工具脚本
├── requirements.txt         # Python 依赖
├── .env.example            # 环境变量模板
├── main.py                 # 主应用文件
└── start.bat               # Windows 启动脚本
```

## 🔧 配置选项

### 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `DEBUG` | 调试模式 | `False` |
| `HOST` | 服务器地址 | `0.0.0.0` |
| `PORT` | 服务器端口 | `8000` |
| `DATABASE_URL` | 数据库连接 | `sqlite:///./edusynapse.db` |
| `OPENAI_API_KEY` | OpenAI API 密钥 | - |
| `OPENAI_MODEL` | OpenAI 模型 | `gpt-4` |
| `OPENAI_TEMPERATURE` | 生成温度 | `0.7` |

### WWH 引擎配置

- `WWH_MAX_CONCEPTS_PER_SECTION`: 每个部分最大概念数 (默认: 5)
- `WWH_MAX_DAILY_TASKS`: 每日最大任务数 (默认: 3)
- `WWH_DEFAULT_DURATION_DAYS`: 默认学习天数 (默认: 14)

## 🗄️ 数据库

### 核心表结构

1. **learning_plans** - 学习计划
2. **learning_progress** - 学习进度

### 数据库操作

```bash
# 重置数据库
python scripts/init_database.py

# 创建示例数据
# 在初始化脚本中选择 "Y"
```

## 🔍 故障排除

### 常见问题

1. **API 密钥错误**
   ```
   ⚠️ 警告: 未配置AI API密钥
   ```
   - 检查 `.env` 文件中的 API 密钥配置

2. **数据库连接失败**
   ```
   ❌ 数据库连接失败
   ```
   - 检查数据库文件权限
   - 确保目录可写

3. **依赖安装失败**
   ```
   ❌ 依赖安装失败
   ```
   - 升级 pip: `python -m pip install --upgrade pip`
   - 检查 Python 版本是否为 3.11+

### 日志查看

- 应用日志: `logs/edusynapse.log`
- 控制台输出: 实时显示在终端

## 🤝 开发指南

### 添加新的 API 端点

1. 在 `app/api/` 中创建新的路由文件
2. 在 `app/schemas/` 中定义数据传输对象
3. 在 `main.py` 中注册路由

### 扩展 WWH 引擎

1. 修改 `app/services/wwh_engine.py`
2. 更新提示词模板
3. 添加新的生成策略

## 📄 许可证

本项目为个人学习项目，仅供学习和研究使用。
