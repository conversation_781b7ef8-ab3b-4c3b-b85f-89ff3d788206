# 批量修复 Color.TextSecondary 和图标问题的脚本

Write-Host "🔧 修复 MudBlazor 颜色和图标引用..." -ForegroundColor Cyan

$projectRoot = $PSScriptRoot

# 获取所有 .razor 文件
$razorFiles = Get-ChildItem -Path $projectRoot -Filter "*.razor" -Recurse

$totalFiles = 0
$modifiedFiles = 0

foreach ($file in $razorFiles) {
    $totalFiles++
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    
    # 替换 Color.TextSecondary
    $content = $content -replace 'Color\.TextSecondary', 'Color.Secondary'
    
    # 替换过时的图标
    $content = $content -replace 'Icons\.Material\.Filled\.SchoolOutlined', 'Icons.Material.Filled.School'
    $content = $content -replace 'Icons\.Material\.Filled\.Eco', 'Icons.Material.Filled.Nature'
    
    # 如果内容有变化，保存文件
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "✅ 修复: $($file.Name)" -ForegroundColor Green
        $modifiedFiles++
    }
}

Write-Host "`n📊 修复完成:" -ForegroundColor Cyan
Write-Host "  总文件数: $totalFiles" -ForegroundColor White
Write-Host "  修改文件数: $modifiedFiles" -ForegroundColor Green

if ($modifiedFiles -gt 0) {
    Write-Host "`n🎉 所有颜色和图标引用已修复!" -ForegroundColor Green
} else {
    Write-Host "`n✅ 没有发现需要修复的文件" -ForegroundColor Green
}
