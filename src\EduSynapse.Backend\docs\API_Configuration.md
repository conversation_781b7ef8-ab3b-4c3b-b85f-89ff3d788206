# EduSynapse API 配置指南

## OpenAI API 配置

EduSynapse 支持使用官方 OpenAI API 或兼容的第三方 API 端点。

### 配置项说明

| 配置项 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `OPENAI_API_BASE` | OpenAI API 基地址 | `https://api.openai.com/v1` | `https://api.deepbricks.ai/v1/` |
| `OPENAI_API_KEY` | OpenAI API 密钥 | - | `sk-xxx...` |
| `OPENAI_MODEL` | 使用的模型名称 | `gpt-4` | `gpt-4`, `gpt-3.5-turbo` |
| `OPENAI_MAX_TOKENS` | 最大生成令牌数 | `2000` | `1000-4000` |
| `OPENAI_TEMPERATURE` | 生成温度 | `0.7` | `0.0-2.0` |

### 支持的第三方 API 提供商

#### 1. DeepBricks AI
```env
OPENAI_API_BASE=https://api.deepbricks.ai/v1/
OPENAI_API_KEY=sk-your-deepbricks-key
OPENAI_MODEL=gpt-4
```

#### 2. Azure OpenAI
```env
OPENAI_API_BASE=https://your-resource.openai.azure.com/
OPENAI_API_KEY=your-azure-key
OPENAI_MODEL=gpt-4
```

#### 3. 其他兼容 OpenAI API 的服务
```env
OPENAI_API_BASE=https://your-custom-endpoint/v1/
OPENAI_API_KEY=your-api-key
OPENAI_MODEL=gpt-4
```

### 配置验证

启动时，系统会自动验证配置：

- ✅ **API 基地址检查**: 显示当前使用的 API 端点
- ✅ **自定义端点提示**: 如果使用非官方端点会有提示
- ✅ **密钥验证**: 检查 API 密钥是否配置
- ✅ **模型兼容性**: 确保模型名称正确

### 故障排除

#### 1. API 连接失败
```
❌ LLM初始化失败: Connection error
```
**解决方案**:
- 检查 `OPENAI_API_BASE` 地址是否正确
- 确认网络连接正常
- 验证 API 端点是否可访问

#### 2. 认证失败
```
❌ LLM初始化失败: Authentication failed
```
**解决方案**:
- 检查 `OPENAI_API_KEY` 是否正确
- 确认 API 密钥是否有效
- 验证密钥权限是否足够

#### 3. 模型不支持
```
❌ LLM初始化失败: Model not found
```
**解决方案**:
- 检查 `OPENAI_MODEL` 名称是否正确
- 确认 API 提供商是否支持该模型
- 尝试使用其他兼容模型

### 性能优化建议

#### 1. 模型选择
- **gpt-4**: 最高质量，适合复杂学习计划生成
- **gpt-3.5-turbo**: 平衡性能和成本
- **自定义模型**: 根据提供商文档选择

#### 2. 参数调优
- **Temperature**: 
  - `0.3-0.5`: 更一致的输出
  - `0.7-0.9`: 更有创意的内容
- **Max Tokens**: 
  - `1000-2000`: 适合大多数学习计划
  - `2000-4000`: 复杂或详细的计划

#### 3. 请求优化
- 使用连接池减少延迟
- 实现请求重试机制
- 监控 API 使用量和成本

### 安全注意事项

1. **API 密钥保护**:
   - 不要在代码中硬编码密钥
   - 使用环境变量存储敏感信息
   - 定期轮换 API 密钥

2. **网络安全**:
   - 使用 HTTPS 连接
   - 验证 SSL 证书
   - 考虑使用 VPN 或专线

3. **数据隐私**:
   - 了解 API 提供商的数据处理政策
   - 避免发送敏感个人信息
   - 考虑本地部署方案

### 监控和日志

系统会记录以下信息：
- API 连接状态
- 请求响应时间
- 错误和异常
- 使用统计

查看日志：
```bash
tail -f logs/edusynapse.log
```

### 成本控制

1. **设置使用限制**:
   ```env
   OPENAI_MAX_TOKENS=1500  # 减少单次请求成本
   RATE_LIMIT_REQUESTS=50  # 限制请求频率
   ```

2. **监控使用量**:
   - 定期检查 API 使用统计
   - 设置预算警报
   - 优化提示词长度

3. **缓存策略**:
   - 缓存常见学习计划
   - 避免重复生成相同内容
   - 使用本地存储减少 API 调用
