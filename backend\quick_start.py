#!/usr/bin/env python3
"""
EduSynapse AI Backend Quick Start Script
快速启动和验证脚本
"""

import os
import sys
import subprocess
import time
import asyncio
from pathlib import Path
import shutil


def print_banner():
    """打印启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║               🎓 EduSynapse AI Backend                       ║
    ║                                                              ║
    ║           基于LangChain和AutoGen的智能教学系统                ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"   当前版本: {sys.version}")
        return False
    
    print(f"✅ Python版本: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True


def check_dependencies():
    """检查依赖包"""
    print("\n📦 检查依赖包...")
    
    required_packages = [
        "fastapi", "uvicorn", "pydantic", "langchain", 
        "openai", "pandas", "numpy", "scikit-learn", "httpx"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("正在安装依赖包...")
        
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ])
            print("✅ 依赖包安装完成")
            return True
        except subprocess.CalledProcessError:
            print("❌ 依赖包安装失败")
            return False
    
    print("✅ 所有依赖包已安装")
    return True


def setup_environment():
    """设置环境"""
    print("\n🔧 设置环境...")
    
    # 检查.env文件
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists():
        if env_example.exists():
            print("📋 复制环境配置文件...")
            shutil.copy(env_example, env_file)
            print("✅ 已创建.env文件")
        else:
            print("⚠️  .env.example文件不存在，创建基础配置...")
            create_basic_env_file()
    
    # 检查OpenAI API Key
    openai_key = os.getenv("OPENAI_API_KEY")
    if not openai_key:
        print("\n⚠️  未设置OPENAI_API_KEY环境变量")
        print("请在.env文件中设置您的OpenAI API Key:")
        print("OPENAI_API_KEY=your_api_key_here")
        
        # 询问是否继续
        response = input("\n是否继续启动? (某些功能可能无法正常工作) [y/N]: ")
        if response.lower() != 'y':
            return False
    else:
        print("✅ OpenAI API Key已配置")
    
    # 创建必要目录
    directories = ["logs", "data", "uploads"]
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ 目录: {directory}")
    
    return True


def create_basic_env_file():
    """创建基础环境配置文件"""
    basic_env = """# EduSynapse AI Backend Environment Configuration

# 基础配置
DEBUG=true
HOST=0.0.0.0
PORT=8000
ENVIRONMENT=development

# AI服务配置 (必需)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_API_BASE=https://api.openai.com/v1
OPENAI_MODEL=gpt-4

# 数据库配置
DATABASE_URL=sqlite:///./edusynapse.db

# 日志配置
LOG_LEVEL=INFO
"""
    
    with open(".env", "w", encoding="utf-8") as f:
        f.write(basic_env)


def start_server():
    """启动服务器"""
    print("\n🚀 启动EduSynapse AI Backend服务器...")
    
    try:
        # 使用start_server.py启动
        if Path("start_server.py").exists():
            print("使用start_server.py启动...")
            process = subprocess.Popen([
                sys.executable, "start_server.py", 
                "--host", "0.0.0.0", 
                "--port", "8000",
                "--reload"
            ])
        else:
            print("使用uvicorn直接启动...")
            process = subprocess.Popen([
                sys.executable, "-m", "uvicorn", 
                "app.main:app", 
                "--host", "0.0.0.0", 
                "--port", "8000", 
                "--reload"
            ])
        
        print("✅ 服务器启动中...")
        print("📍 访问地址: http://localhost:8000")
        print("📚 API文档: http://localhost:8000/docs")
        print("📖 ReDoc文档: http://localhost:8000/redoc")
        
        # 等待服务器启动
        print("\n⏳ 等待服务器启动...")
        time.sleep(5)
        
        return process
        
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        return None


async def test_server():
    """测试服务器"""
    print("\n🧪 测试服务器功能...")
    
    try:
        # 导入测试模块
        from test_api import EduSynapseAPITester
        
        tester = EduSynapseAPITester("http://localhost:8000")
        
        # 运行基础测试
        print("进行健康检查...")
        health_ok = await tester.test_health_check()
        
        if health_ok:
            print("\n进行功能测试...")
            await tester.test_get_teachers()
            await tester.test_get_dimensions()
            
            print("\n✅ 基础功能测试通过！")
            print("🎉 EduSynapse AI Backend启动成功！")
            return True
        else:
            print("❌ 健康检查失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def print_usage_info():
    """打印使用信息"""
    print("\n" + "="*60)
    print("🎯 EduSynapse AI Backend 使用指南")
    print("="*60)
    print("📍 服务地址:")
    print("   - 主页: http://localhost:8000")
    print("   - 健康检查: http://localhost:8000/health")
    print("   - API文档: http://localhost:8000/docs")
    print("   - ReDoc文档: http://localhost:8000/redoc")
    
    print("\n🔧 主要API端点:")
    print("   - 生成学习计划: POST /api/v1/teaching/generate-plan")
    print("   - 开始教学会话: POST /api/v1/teaching/start-session")
    print("   - 继续对话: POST /api/v1/teaching/continue-session")
    print("   - 进度跟踪: POST /api/v1/progress/track")
    
    print("\n🧪 测试命令:")
    print("   python test_api.py --test all")
    print("   python test_api.py --test health")
    
    print("\n📚 更多信息:")
    print("   - 查看README.md了解详细使用说明")
    print("   - 查看functional-requirements.md了解功能需求")
    
    print("\n⚠️  注意事项:")
    print("   - 确保在.env文件中设置了有效的OPENAI_API_KEY")
    print("   - 首次使用可能需要下载AI模型，请耐心等待")
    print("   - 按Ctrl+C停止服务器")
    print("="*60)


async def main():
    """主函数"""
    print_banner()
    
    # 检查环境
    if not check_python_version():
        return
    
    if not check_dependencies():
        return
    
    if not setup_environment():
        return
    
    # 启动服务器
    server_process = start_server()
    if not server_process:
        return
    
    try:
        # 测试服务器
        test_success = await test_server()
        
        if test_success:
            print_usage_info()
            
            # 保持服务器运行
            print("\n🔄 服务器正在运行中...")
            print("按Ctrl+C停止服务器")
            
            server_process.wait()
        else:
            print("\n❌ 服务器测试失败，正在停止...")
            server_process.terminate()
            
    except KeyboardInterrupt:
        print("\n\n👋 正在停止服务器...")
        server_process.terminate()
        server_process.wait()
        print("✅ 服务器已停止")
    
    except Exception as e:
        print(f"\n❌ 运行时错误: {e}")
        if server_process:
            server_process.terminate()


if __name__ == "__main__":
    # 在Windows上设置事件循环策略
    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    asyncio.run(main())
