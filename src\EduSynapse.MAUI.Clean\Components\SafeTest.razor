<h1>🛡️ 安全测试组件</h1>

<div style="padding: 20px; border: 2px solid #007bff; border-radius: 8px; margin: 10px;">
    <h3>📊 组件状态</h3>
    <p><strong>计数器:</strong> @count</p>
    <p><strong>初始化时间:</strong> @initTime</p>
    <p><strong>最后点击时间:</strong> @lastClickTime</p>
    <p><strong>总点击次数:</strong> @totalClicks</p>
    <p><strong>当前时间:</strong> @DateTime.Now.ToString("HH:mm:ss.fff")</p>
    
    <h3>🔘 事件测试</h3>
    <div style="margin: 10px 0;">
        <button @onclick="IncrementCount" 
                style="background: #007bff; color: white; border: none; padding: 12px 24px; margin: 5px; border-radius: 4px; cursor: pointer; font-size: 14px;">
            🚀 点击增加 (@count)
        </button>
        
        <button @onclick="ResetCount" 
                style="background: #dc3545; color: white; border: none; padding: 12px 24px; margin: 5px; border-radius: 4px; cursor: pointer; font-size: 14px;">
            🔄 重置计数
        </button>
        
        <button @onclick="TestMultipleOperations" 
                style="background: #28a745; color: white; border: none; padding: 12px 24px; margin: 5px; border-radius: 4px; cursor: pointer; font-size: 14px;">
            🧪 多重操作测试
        </button>
    </div>
    
    <h3>📝 事件日志</h3>
    <div style="background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; border-radius: 4px; max-height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px;">
        @if (eventLogs.Any())
        {
            @foreach (var log in eventLogs)
            {
                <div style="margin: 2px 0; color: #495057;">@log</div>
            }
        }
        else
        {
            <div style="color: #6c757d;">暂无事件日志</div>
        }
    </div>
    
    <h3>🔍 调试信息</h3>
    <div style="background: #e9ecef; padding: 10px; border-radius: 4px; font-size: 12px;">
        <p><strong>组件状态:</strong> @componentStatus</p>
        <p><strong>渲染次数:</strong> @renderCount</p>
        <p><strong>错误信息:</strong> @(errorMessage ?? "无错误")</p>
    </div>
</div>

@code {
    private int count = 0;
    private int totalClicks = 0;
    private int renderCount = 0;
    private string initTime = "";
    private string lastClickTime = "未点击";
    private string componentStatus = "正常";
    private string? errorMessage = null;
    private List<string> eventLogs = new List<string>();

    protected override void OnInitialized()
    {
        try
        {
            initTime = DateTime.Now.ToString("HH:mm:ss.fff");
            componentStatus = "初始化成功";
            AddLog("🎯 SafeTest 组件初始化成功");
            System.Diagnostics.Debug.WriteLine($"🎯 SafeTest 组件初始化: {initTime}");
        }
        catch (Exception ex)
        {
            errorMessage = ex.Message;
            componentStatus = "初始化失败";
            System.Diagnostics.Debug.WriteLine($"❌ SafeTest 初始化失败: {ex}");
        }
    }

    protected override void OnAfterRender(bool firstRender)
    {
        renderCount++;
        if (firstRender)
        {
            System.Diagnostics.Debug.WriteLine("🎨 SafeTest 首次渲染完成");
        }
    }

    private void IncrementCount()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine($"🔥 IncrementCount 事件触发！当前计数: {count}");
            
            count++;
            totalClicks++;
            lastClickTime = DateTime.Now.ToString("HH:mm:ss.fff");
            componentStatus = "计数增加成功";
            
            AddLog($"➕ 计数增加到: {count}");
            System.Diagnostics.Debug.WriteLine($"✅ 计数已更新: {count}, 总点击: {totalClicks}");
            
            StateHasChanged();
            System.Diagnostics.Debug.WriteLine("🔄 StateHasChanged() 已调用");
        }
        catch (Exception ex)
        {
            errorMessage = ex.Message;
            componentStatus = "计数增加失败";
            AddLog($"❌ 错误: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"❌ IncrementCount 失败: {ex}");
        }
    }

    private void ResetCount()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("🔄 ResetCount 事件触发！");
            
            count = 0;
            totalClicks++;
            lastClickTime = DateTime.Now.ToString("HH:mm:ss.fff");
            componentStatus = "计数重置成功";
            
            AddLog("🔄 计数已重置");
            System.Diagnostics.Debug.WriteLine("✅ 计数已重置");
            
            StateHasChanged();
        }
        catch (Exception ex)
        {
            errorMessage = ex.Message;
            componentStatus = "计数重置失败";
            AddLog($"❌ 重置错误: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"❌ ResetCount 失败: {ex}");
        }
    }

    private void TestMultipleOperations()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("🧪 TestMultipleOperations 事件触发！");
            
            count += 5;
            totalClicks++;
            lastClickTime = DateTime.Now.ToString("HH:mm:ss.fff");
            componentStatus = "多重操作成功";
            
            AddLog($"🧪 多重操作: +5, 新计数: {count}");
            System.Diagnostics.Debug.WriteLine($"✅ 多重操作完成: {count}");
            
            StateHasChanged();
        }
        catch (Exception ex)
        {
            errorMessage = ex.Message;
            componentStatus = "多重操作失败";
            AddLog($"❌ 多重操作错误: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"❌ TestMultipleOperations 失败: {ex}");
        }
    }

    private void AddLog(string message)
    {
        try
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
            eventLogs.Add($"[{timestamp}] {message}");
            
            // 保持日志数量在合理范围内
            if (eventLogs.Count > 10)
            {
                eventLogs.RemoveAt(0);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ AddLog 失败: {ex}");
        }
    }
}
