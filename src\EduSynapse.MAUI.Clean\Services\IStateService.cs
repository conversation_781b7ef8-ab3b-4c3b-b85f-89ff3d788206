using EduSynapse.MAUI.Models;

namespace EduSynapse.MAUI.Services;

/// <summary>
/// 状态管理服务接口
/// </summary>
public interface IStateService
{
    /// <summary>
    /// 当前选中的学习计划
    /// </summary>
    LearningPlan? CurrentPlan { get; set; }

    /// <summary>
    /// 学习计划列表
    /// </summary>
    List<LearningPlanSummary> LearningPlans { get; set; }

    /// <summary>
    /// 当前学习进度列表
    /// </summary>
    List<LearningProgress> CurrentProgress { get; set; }

    /// <summary>
    /// API连接状态
    /// </summary>
    bool IsApiConnected { get; set; }

    /// <summary>
    /// 应用是否正在加载
    /// </summary>
    bool IsLoading { get; set; }

    /// <summary>
    /// 当前错误消息
    /// </summary>
    string? ErrorMessage { get; set; }

    /// <summary>
    /// 状态变化事件
    /// </summary>
    event EventHandler? StateChanged;

    /// <summary>
    /// 通知状态变化
    /// </summary>
    void NotifyStateChanged();

    /// <summary>
    /// 清除错误消息
    /// </summary>
    void ClearError();

    /// <summary>
    /// 设置错误消息
    /// </summary>
    void SetError(string message);

    /// <summary>
    /// 重置状态
    /// </summary>
    void Reset();
}

/// <summary>
/// 本地存储服务接口
/// </summary>
public interface IStorageService
{
    /// <summary>
    /// 保存数据
    /// </summary>
    Task SaveAsync<T>(string key, T data);

    /// <summary>
    /// 读取数据
    /// </summary>
    Task<T?> LoadAsync<T>(string key);

    /// <summary>
    /// 删除数据
    /// </summary>
    Task RemoveAsync(string key);

    /// <summary>
    /// 检查数据是否存在
    /// </summary>
    Task<bool> ExistsAsync(string key);

    /// <summary>
    /// 清除所有数据
    /// </summary>
    Task ClearAllAsync();
}
