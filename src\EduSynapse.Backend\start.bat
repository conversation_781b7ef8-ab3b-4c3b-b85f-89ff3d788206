@echo off
echo 🚀 EduSynapse Backend 启动脚本
echo ================================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.11+
    pause
    exit /b 1
)

REM 检查虚拟环境
if not exist "venv" (
    echo 📦 创建Python虚拟环境...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ 虚拟环境创建失败
        pause
        exit /b 1
    )
)

REM 激活虚拟环境
echo 🔧 激活虚拟环境...
call venv\Scripts\activate.bat

REM 安装依赖
echo 📥 安装依赖包...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

REM 检查环境变量文件
if not exist ".env" (
    echo ⚙️ 创建环境配置文件...
    copy .env.example .env
    echo.
    echo ⚠️ 请编辑 .env 文件，配置您的API密钥:
    echo    - OPENAI_API_KEY=your-openai-api-key-here
    echo.
    echo 按任意键继续...
    pause >nul
)

REM 初始化数据库
echo 🗄️ 初始化数据库...
python scripts\init_database.py

REM 启动服务器
echo 🚀 启动EduSynapse后端服务...
echo.
echo 📊 API文档地址: http://localhost:8000/docs
echo 🔍 健康检查: http://localhost:8000/health
echo.
python main.py

pause
