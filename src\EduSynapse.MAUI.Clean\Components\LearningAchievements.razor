@page "/learning-achievements"
@using EduSynapse.MAUI.Services
@using EduSynapse.MAUI.Models
@inject StorageService StorageService
@inject IJSRuntime JSRuntime

<div class="container-fluid p-4">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-3">🏆 学习成就</h2>
            <p class="text-muted">查看您的学习成就和里程碑</p>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center p-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载成就数据...</p>
        </div>
    }
    else
    {
        <!-- 总体成就统计 -->
        <div class="row mb-4">
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-gradient-primary text-white">
                    <div class="card-body text-center">
                        <h3>@totalAchievements</h3>
                        <p class="mb-0">总成就数</p>
                        <small>已解锁 @unlockedAchievements 个</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-gradient-success text-white">
                    <div class="card-body text-center">
                        <h3>@totalPoints</h3>
                        <p class="mb-0">成就积分</p>
                        <small>学习等级 @GetLearningLevel()</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-gradient-info text-white">
                    <div class="card-body text-center">
                        <h3>@longestStreak</h3>
                        <p class="mb-0">最长连续学习</p>
                        <small>天数记录</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-gradient-warning text-white">
                    <div class="card-body text-center">
                        <h3>@totalStudyHours.ToString("F0")</h3>
                        <p class="mb-0">累计学习时间</p>
                        <small>小时</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最新解锁的成就 -->
        @if (recentAchievements.Any())
        {
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">🎉 最新解锁成就</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                @foreach (var achievement in recentAchievements.Take(3))
                                {
                                    <div class="col-md-4 mb-3">
                                        <div class="card border-success">
                                            <div class="card-body text-center">
                                                <div class="achievement-icon mb-3">
                                                    @achievement.Icon
                                                </div>
                                                <h6 class="card-title">@achievement.Name</h6>
                                                <p class="card-text text-muted">@achievement.Description</p>
                                                <small class="text-success">
                                                    <i class="fas fa-calendar"></i>
                                                    @achievement.UnlockedDate?.ToString("MM-dd")
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }

        <!-- 成就分类 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">🏅 成就列表</h5>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn @(selectedCategory == "all" ? "btn-primary" : "btn-outline-primary") btn-sm" 
                                        @onclick="() => FilterAchievements(\"all\")">
                                    全部
                                </button>
                                <button type="button" class="btn @(selectedCategory == "study" ? "btn-primary" : "btn-outline-primary") btn-sm" 
                                        @onclick="() => FilterAchievements(\"study\")">
                                    学习类
                                </button>
                                <button type="button" class="btn @(selectedCategory == "time" ? "btn-primary" : "btn-outline-primary") btn-sm" 
                                        @onclick="() => FilterAchievements(\"time\")">
                                    时间类
                                </button>
                                <button type="button" class="btn @(selectedCategory == "streak" ? "btn-primary" : "btn-outline-primary") btn-sm" 
                                        @onclick="() => FilterAchievements(\"streak\")">
                                    连续类
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @foreach (var achievement in filteredAchievements)
                            {
                                <div class="col-lg-4 col-md-6 mb-4">
                                    <div class="card h-100 @(achievement.IsUnlocked ? "border-success" : "border-secondary")">
                                        <div class="card-body text-center">
                                            <div class="achievement-icon mb-3 @(achievement.IsUnlocked ? "" : "grayscale")">
                                                @achievement.Icon
                                            </div>
                                            <h6 class="card-title @(achievement.IsUnlocked ? "text-success" : "text-muted")">
                                                @achievement.Name
                                            </h6>
                                            <p class="card-text">@achievement.Description</p>
                                            <div class="achievement-progress mb-3">
                                                <div class="progress">
                                                    <div class="progress-bar @(achievement.IsUnlocked ? "bg-success" : "bg-info")" 
                                                         style="width: @achievement.ProgressPercentage%">
                                                    </div>
                                                </div>
                                                <small class="text-muted">
                                                    @achievement.CurrentProgress / @achievement.RequiredProgress
                                                </small>
                                            </div>
                                            <div class="achievement-reward">
                                                <span class="badge @(achievement.IsUnlocked ? "bg-success" : "bg-secondary")">
                                                    +@achievement.Points 积分
                                                </span>
                                                @if (achievement.IsUnlocked && achievement.UnlockedDate.HasValue)
                                                {
                                                    <br/>
                                                    <small class="text-success">
                                                        解锁于 @achievement.UnlockedDate.Value.ToString("yyyy-MM-dd")
                                                    </small>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 学习等级进度 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">📈 学习等级进度</h5>
                    </div>
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h6>当前等级：@GetLearningLevel() 级学习者</h6>
                                <div class="progress mb-2" style="height: 20px;">
                                    <div class="progress-bar bg-gradient-primary" 
                                         style="width: @GetLevelProgress()%">
                                        @GetLevelProgress().ToString("F0")%
                                    </div>
                                </div>
                                <small class="text-muted">
                                    距离下一级还需 @GetPointsToNextLevel() 积分
                                </small>
                            </div>
                            <div class="col-md-4 text-center">
                                <div class="level-badge">
                                    <div class="badge bg-primary p-3" style="font-size: 1.5rem;">
                                        @GetLevelIcon() @GetLearningLevel()
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- 操作结果提示 -->
    @if (!string.IsNullOrEmpty(resultMessage))
    {
        <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1050;">
            <div class="toast show" role="alert">
                <div class="toast-header">
                    <strong class="me-auto">@(isSuccess ? "✅ 成功" : "❌ 错误")</strong>
                    <button type="button" class="btn-close" @onclick="ClearResult"></button>
                </div>
                <div class="toast-body">
                    @resultMessage
                </div>
            </div>
        </div>
    }
</div>

<style>
    .achievement-icon {
        font-size: 3rem;
        transition: all 0.3s ease;
    }
    
    .grayscale {
        filter: grayscale(100%);
        opacity: 0.5;
    }
    
    .bg-gradient-primary {
        background: linear-gradient(45deg, #007bff, #0056b3);
    }
    
    .bg-gradient-success {
        background: linear-gradient(45deg, #28a745, #1e7e34);
    }
    
    .bg-gradient-info {
        background: linear-gradient(45deg, #17a2b8, #117a8b);
    }
    
    .bg-gradient-warning {
        background: linear-gradient(45deg, #ffc107, #e0a800);
    }
    
    .card:hover {
        transform: translateY(-2px);
        transition: all 0.3s ease;
    }
    
    .level-badge {
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
</style>

@code {
    private bool isLoading = false;
    private bool isSuccess = false;
    private string resultMessage = "";
    private string selectedCategory = "all";

    private List<Achievement> allAchievements = new();
    private List<Achievement> filteredAchievements = new();
    private List<Achievement> recentAchievements = new();

    private int totalAchievements => allAchievements.Count;
    private int unlockedAchievements => allAchievements.Count(a => a.IsUnlocked);
    private int totalPoints => allAchievements.Where(a => a.IsUnlocked).Sum(a => a.Points);
    private int longestStreak = 0;
    private double totalStudyHours = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadAchievementData();
    }

    private async Task LoadAchievementData()
    {
        isLoading = true;
        try
        {
            // 初始化成就列表
            InitializeAchievements();

            // 加载学习数据来计算成就进度
            await CalculateAchievementProgress();

            // 筛选最近解锁的成就
            recentAchievements = allAchievements
                .Where(a => a.IsUnlocked && a.UnlockedDate.HasValue)
                .OrderByDescending(a => a.UnlockedDate)
                .Take(5)
                .ToList();

            FilterAchievements(selectedCategory);

            System.Diagnostics.Debug.WriteLine($"✅ 加载成就数据: {totalAchievements} 个成就，{unlockedAchievements} 个已解锁");
        }
        catch (Exception ex)
        {
            ShowMessage($"加载成就数据失败: {ex.Message}", false);
            System.Diagnostics.Debug.WriteLine($"❌ 加载成就数据失败: {ex}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private void InitializeAchievements()
    {
        allAchievements = new List<Achievement>
        {
            // 学习类成就
            new Achievement
            {
                Id = 1,
                Name = "学习新手",
                Description = "创建第一个学习计划",
                Icon = "🎯",
                Category = "study",
                Points = 10,
                RequiredProgress = 1
            },
            new Achievement
            {
                Id = 2,
                Name = "学习达人",
                Description = "创建 5 个学习计划",
                Icon = "📚",
                Category = "study",
                Points = 50,
                RequiredProgress = 5
            },
            new Achievement
            {
                Id = 3,
                Name = "学习专家",
                Description = "创建 10 个学习计划",
                Icon = "🎓",
                Category = "study",
                Points = 100,
                RequiredProgress = 10
            },
            new Achievement
            {
                Id = 4,
                Name = "学习大师",
                Description = "完成 3 个学习计划",
                Icon = "👑",
                Category = "study",
                Points = 150,
                RequiredProgress = 3
            },

            // 时间类成就
            new Achievement
            {
                Id = 5,
                Name = "时间管理者",
                Description = "累计学习 10 小时",
                Icon = "⏰",
                Category = "time",
                Points = 30,
                RequiredProgress = 10
            },
            new Achievement
            {
                Id = 6,
                Name = "勤奋学习者",
                Description = "累计学习 50 小时",
                Icon = "💪",
                Category = "time",
                Points = 100,
                RequiredProgress = 50
            },
            new Achievement
            {
                Id = 7,
                Name = "学习马拉松",
                Description = "累计学习 100 小时",
                Icon = "🏃‍♂️",
                Category = "time",
                Points = 200,
                RequiredProgress = 100
            },
            new Achievement
            {
                Id = 8,
                Name = "学习传奇",
                Description = "累计学习 500 小时",
                Icon = "🌟",
                Category = "time",
                Points = 500,
                RequiredProgress = 500
            },

            // 连续类成就
            new Achievement
            {
                Id = 9,
                Name = "坚持不懈",
                Description = "连续学习 3 天",
                Icon = "🔥",
                Category = "streak",
                Points = 25,
                RequiredProgress = 3
            },
            new Achievement
            {
                Id = 10,
                Name = "习惯养成",
                Description = "连续学习 7 天",
                Icon = "📅",
                Category = "streak",
                Points = 75,
                RequiredProgress = 7
            },
            new Achievement
            {
                Id = 11,
                Name = "月度坚持",
                Description = "连续学习 30 天",
                Icon = "🗓️",
                Category = "streak",
                Points = 300,
                RequiredProgress = 30
            },
            new Achievement
            {
                Id = 12,
                Name = "年度坚持",
                Description = "连续学习 365 天",
                Icon = "🏆",
                Category = "streak",
                Points = 1000,
                RequiredProgress = 365
            }
        };
    }

    private async Task CalculateAchievementProgress()
    {
        try
        {
            // 加载学习计划数据
            var allPlans = await StorageService.LoadAsync<List<LearningPlan>>("learning_plans") ?? new List<LearningPlan>();
            var completedPlans = allPlans.Count(p => p.Status == "completed");

            // 加载所有进度记录
            var allRecords = new List<ProgressRecord>();
            foreach (var plan in allPlans)
            {
                var storageKey = $"progress_records_{plan.Id}";
                var records = await StorageService.LoadAsync<List<ProgressRecord>>(storageKey) ?? new List<ProgressRecord>();
                allRecords.AddRange(records);
            }

            // 计算总学习时间
            totalStudyHours = allRecords.Sum(r => r.StudyHours);

            // 计算最长连续学习天数
            longestStreak = CalculateLongestStreak(allRecords);

            // 更新成就进度
            foreach (var achievement in allAchievements)
            {
                switch (achievement.Category)
                {
                    case "study":
                        if (achievement.Name.Contains("创建"))
                        {
                            achievement.CurrentProgress = allPlans.Count;
                        }
                        else if (achievement.Name.Contains("完成"))
                        {
                            achievement.CurrentProgress = completedPlans;
                        }
                        break;

                    case "time":
                        achievement.CurrentProgress = (int)totalStudyHours;
                        break;

                    case "streak":
                        achievement.CurrentProgress = longestStreak;
                        break;
                }

                // 检查是否解锁
                if (achievement.CurrentProgress >= achievement.RequiredProgress && !achievement.IsUnlocked)
                {
                    achievement.IsUnlocked = true;
                    achievement.UnlockedDate = DateTime.Now;
                }

                // 计算进度百分比
                achievement.ProgressPercentage = Math.Min(100, (double)achievement.CurrentProgress / achievement.RequiredProgress * 100);
            }

            // 保存成就数据
            await StorageService.SaveAsync("achievements", allAchievements);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ 计算成就进度失败: {ex}");
        }
    }

    private int CalculateLongestStreak(List<ProgressRecord> records)
    {
        if (!records.Any()) return 0;

        var studyDates = records
            .Where(r => r.StudyHours > 0)
            .Select(r => r.Date.Date)
            .Distinct()
            .OrderBy(d => d)
            .ToList();

        if (!studyDates.Any()) return 0;

        int maxStreak = 1;
        int currentStreak = 1;

        for (int i = 1; i < studyDates.Count; i++)
        {
            if (studyDates[i] == studyDates[i - 1].AddDays(1))
            {
                currentStreak++;
                maxStreak = Math.Max(maxStreak, currentStreak);
            }
            else
            {
                currentStreak = 1;
            }
        }

        return maxStreak;
    }

    private void FilterAchievements(string category)
    {
        selectedCategory = category;

        if (category == "all")
        {
            filteredAchievements = allAchievements.ToList();
        }
        else
        {
            filteredAchievements = allAchievements.Where(a => a.Category == category).ToList();
        }
    }

    private int GetLearningLevel()
    {
        return (totalPoints / 100) + 1;
    }

    private string GetLevelIcon()
    {
        var level = GetLearningLevel();
        return level switch
        {
            >= 10 => "👑",
            >= 7 => "🌟",
            >= 5 => "🎓",
            >= 3 => "📚",
            _ => "🎯"
        };
    }

    private double GetLevelProgress()
    {
        var currentLevelPoints = totalPoints % 100;
        return currentLevelPoints;
    }

    private int GetPointsToNextLevel()
    {
        var currentLevelPoints = totalPoints % 100;
        return 100 - currentLevelPoints;
    }

    private void ShowMessage(string message, bool success)
    {
        resultMessage = message;
        isSuccess = success;

        Task.Delay(3000).ContinueWith(_ =>
        {
            resultMessage = "";
            InvokeAsync(StateHasChanged);
        });
    }

    private void ClearResult()
    {
        resultMessage = "";
    }

    // 数据模型
    public class Achievement
    {
        public int Id { get; set; }
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public string Icon { get; set; } = "";
        public string Category { get; set; } = "";
        public int Points { get; set; }
        public int RequiredProgress { get; set; }
        public int CurrentProgress { get; set; }
        public double ProgressPercentage { get; set; }
        public bool IsUnlocked { get; set; }
        public DateTime? UnlockedDate { get; set; }
    }

    public class ProgressRecord
    {
        public DateTime Date { get; set; }
        public double StudyHours { get; set; }
        public string Status { get; set; } = "completed";
        public string Content { get; set; } = "";
        public int MoodScore { get; set; } = 8;
        public string Notes { get; set; } = "";
        public int PlanId { get; set; }
    }
}
