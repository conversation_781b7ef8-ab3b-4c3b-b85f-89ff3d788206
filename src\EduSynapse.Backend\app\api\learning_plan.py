"""
EduSynapse Learning Plan API Endpoints
学习计划相关API端点
"""

import json
import uuid
from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import desc

from app.database.database import get_db
from app.models.learning_plan import LearningPlan, LearningProgress
from app.schemas.learning_plan import (
    PlanGenerationRequest,
    LearningPlanResponse,
    LearningPlanSummary,
    LearningPlanUpdate,
    ProgressRecordRequest,
    ProgressResponse,
    ProgressStatsResponse,
    ApiResponse,
    ErrorResponse,
    WWHStructure,
)
from app.services.wwh_engine_simple import wwh_engine

# 创建路由器
router = APIRouter(prefix="/api/learning-plans", tags=["学习计划"])

# WWH引擎已在模块中初始化


@router.post("/generate", response_model=ApiResponse)
async def generate_learning_plan(
    request: PlanGenerationRequest, db: Session = Depends(get_db)
):
    """
    生成新的学习计划

    基于用户输入的主题和偏好，使用WWH框架生成个性化学习计划
    """
    request_id = str(uuid.uuid4())

    try:
        print(f"📝 开始生成学习计划: {request.topic}")

        # 使用WWH引擎生成计划
        wwh_plan = await wwh_engine.generate_learning_plan(
            topic=request.topic,
            duration_days=request.duration_days,
            difficulty_level=request.difficulty_level,
            daily_hours=request.daily_hours,
            learning_style=request.learning_style,
        )

        # 创建数据库记录
        db_plan = LearningPlan(
            topic=request.topic,
            description=f"基于WWH框架的{request.topic}学习计划",
            wwh_structure=json.dumps(wwh_plan, ensure_ascii=False),
            duration_days=request.duration_days,
            difficulty_level=request.difficulty_level,
            target_hours_per_day=request.daily_hours,
            status="active",
            started_at=datetime.now(),
        )

        db.add(db_plan)
        db.commit()
        db.refresh(db_plan)

        # 构造响应
        response_data = {
            "plan_id": db_plan.id,
            "topic": db_plan.topic,
            "duration_days": db_plan.duration_days,
            "difficulty_level": db_plan.difficulty_level,
            "wwh_structure": wwh_plan,
            "status": db_plan.status,
            "created_at": db_plan.created_at.isoformat(),
        }

        return ApiResponse(
            success=True,
            data=response_data,
            message=f"学习计划 '{request.topic}' 生成成功",
            request_id=request_id,
        )

    except Exception as e:
        print(f"❌ 学习计划生成失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"学习计划生成失败: {str(e)}",
        )


@router.get("/", response_model=ApiResponse)
async def get_learning_plans(
    status_filter: Optional[str] = None,
    limit: int = 10,
    offset: int = 0,
    db: Session = Depends(get_db),
):
    """
    获取学习计划列表

    支持按状态筛选和分页
    """
    try:
        query = db.query(LearningPlan)

        # 状态筛选
        if status_filter:
            query = query.filter(LearningPlan.status == status_filter)

        # 按创建时间倒序排列
        query = query.order_by(desc(LearningPlan.created_at))

        # 分页
        plans = query.offset(offset).limit(limit).all()

        # 转换为摘要格式
        plan_summaries = []
        for plan in plans:
            summary = LearningPlanSummary(
                id=plan.id,
                topic=plan.topic,
                duration_days=plan.duration_days,
                difficulty_level=plan.difficulty_level,
                status=plan.status,
                created_at=plan.created_at,
            )
            plan_summaries.append(summary.dict())

        return ApiResponse(
            success=True,
            data={
                "plans": plan_summaries,
                "total": len(plan_summaries),
                "offset": offset,
                "limit": limit,
            },
            message="学习计划列表获取成功",
        )

    except Exception as e:
        print(f"❌ 获取学习计划列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取学习计划列表失败: {str(e)}",
        )


@router.get("/{plan_id}", response_model=ApiResponse)
async def get_learning_plan(plan_id: int, db: Session = Depends(get_db)):
    """
    获取特定学习计划的详细信息
    """
    try:
        plan = db.query(LearningPlan).filter(LearningPlan.id == plan_id).first()

        if not plan:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"学习计划 {plan_id} 不存在",
            )

        # 解析WWH结构
        wwh_structure = json.loads(plan.wwh_structure)

        response_data = {
            "id": plan.id,
            "topic": plan.topic,
            "description": plan.description,
            "wwh_structure": wwh_structure,
            "duration_days": plan.duration_days,
            "difficulty_level": plan.difficulty_level,
            "target_hours_per_day": float(plan.target_hours_per_day),
            "status": plan.status,
            "created_at": plan.created_at.isoformat(),
            "updated_at": plan.updated_at.isoformat(),
            "started_at": plan.started_at.isoformat() if plan.started_at else None,
            "completed_at": (
                plan.completed_at.isoformat() if plan.completed_at else None
            ),
        }

        return ApiResponse(
            success=True, data=response_data, message="学习计划详情获取成功"
        )

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 获取学习计划详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取学习计划详情失败: {str(e)}",
        )


@router.put("/{plan_id}", response_model=ApiResponse)
async def update_learning_plan(
    plan_id: int, update_data: LearningPlanUpdate, db: Session = Depends(get_db)
):
    """
    更新学习计划
    """
    try:
        plan = db.query(LearningPlan).filter(LearningPlan.id == plan_id).first()

        if not plan:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"学习计划 {plan_id} 不存在",
            )

        # 更新字段
        update_dict = update_data.dict(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(plan, field, value)

        plan.updated_at = datetime.now()

        # 如果状态改为completed，设置完成时间
        if update_data.status == "completed":
            plan.completed_at = datetime.now()

        db.commit()
        db.refresh(plan)

        return ApiResponse(
            success=True, data=plan.to_dict(), message="学习计划更新成功"
        )

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 更新学习计划失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新学习计划失败: {str(e)}",
        )


@router.delete("/{plan_id}", response_model=ApiResponse)
async def delete_learning_plan(plan_id: int, db: Session = Depends(get_db)):
    """
    删除学习计划
    """
    try:
        plan = db.query(LearningPlan).filter(LearningPlan.id == plan_id).first()

        if not plan:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"学习计划 {plan_id} 不存在",
            )

        # 删除相关的学习进度记录
        db.query(LearningProgress).filter(LearningProgress.plan_id == plan_id).delete()

        # 删除学习计划
        db.delete(plan)
        db.commit()

        return ApiResponse(
            success=True, data={"deleted_plan_id": plan_id}, message="学习计划删除成功"
        )

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 删除学习计划失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除学习计划失败: {str(e)}",
        )


@router.post("/{plan_id}/progress", response_model=ApiResponse)
async def record_learning_progress(
    plan_id: int, progress_data: ProgressRecordRequest, db: Session = Depends(get_db)
):
    """
    记录学习进度
    """
    try:
        # 验证学习计划是否存在
        plan = db.query(LearningPlan).filter(LearningPlan.id == plan_id).first()
        if not plan:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"学习计划 {plan_id} 不存在",
            )

        # 检查是否已有当天的进度记录
        existing_progress = (
            db.query(LearningProgress)
            .filter(
                LearningProgress.plan_id == plan_id,
                LearningProgress.day_number == progress_data.day_number,
            )
            .first()
        )

        if existing_progress:
            # 更新现有记录
            for field, value in progress_data.dict(exclude={"plan_id"}).items():
                setattr(existing_progress, field, value)
            existing_progress.completed_at = datetime.now()
            db.commit()
            db.refresh(existing_progress)
            progress_record = existing_progress
        else:
            # 创建新记录
            progress_record = LearningProgress(
                plan_id=plan_id,
                day_number=progress_data.day_number,
                what_mastery=progress_data.what_mastery,
                why_mastery=progress_data.why_mastery,
                how_mastery=progress_data.how_mastery,
                time_spent=progress_data.time_spent,
                focus_time=progress_data.focus_time,
                break_count=progress_data.break_count,
                notes=progress_data.notes,
                mood_score=progress_data.mood_score,
                difficulty_rating=progress_data.difficulty_rating,
                completed_at=datetime.now(),
            )
            db.add(progress_record)
            db.commit()
            db.refresh(progress_record)

        return ApiResponse(
            success=True, data=progress_record.to_dict(), message="学习进度记录成功"
        )

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 记录学习进度失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"记录学习进度失败: {str(e)}",
        )
