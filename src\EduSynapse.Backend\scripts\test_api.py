"""
EduSynapse API Testing Script
API测试脚本
"""

import asyncio
import json
import requests
from datetime import datetime


class EduSynapseAPITester:
    """EduSynapse API测试器"""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            "Content-Type": "application/json",
            "Accept": "application/json"
        })
    
    def test_health_check(self):
        """测试健康检查"""
        print("🔍 测试健康检查...")
        try:
            response = self.session.get(f"{self.base_url}/health")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 健康检查通过: {data['status']}")
                print(f"   - 数据库状态: {data['database']}")
                print(f"   - AI提供商: {data['ai_provider']}")
                return True
            else:
                print(f"❌ 健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 健康检查异常: {e}")
            return False
    
    def test_generate_plan(self):
        """测试学习计划生成"""
        print("\n📝 测试学习计划生成...")
        
        # 测试数据
        test_request = {
            "topic": "JavaScript异步编程",
            "duration_days": 10,
            "difficulty_level": "medium",
            "daily_hours": 2.0,
            "learning_style": "practical",
            "preferences": {
                "include_projects": True,
                "focus_areas": ["Promise", "async/await", "事件循环"]
            }
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/learning-plans/generate",
                json=test_request
            )
            
            if response.status_code == 200:
                data = response.json()
                if data["success"]:
                    plan_data = data["data"]
                    print(f"✅ 学习计划生成成功!")
                    print(f"   - 计划ID: {plan_data['plan_id']}")
                    print(f"   - 主题: {plan_data['topic']}")
                    print(f"   - 天数: {plan_data['duration_days']}")
                    print(f"   - 难度: {plan_data['difficulty_level']}")
                    
                    # 检查WWH结构
                    wwh = plan_data["wwh_structure"]
                    print(f"   - What概念数: {len(wwh.get('what', {}).get('core_concepts', []))}")
                    print(f"   - How项目数: {len(wwh.get('how', {}).get('practice_projects', []))}")
                    print(f"   - 每日计划数: {len(wwh.get('daily_breakdown', []))}")
                    
                    return plan_data["plan_id"]
                else:
                    print(f"❌ 计划生成失败: {data.get('message', '未知错误')}")
                    return None
            else:
                print(f"❌ API调用失败: {response.status_code}")
                print(f"   响应: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 计划生成异常: {e}")
            return None
    
    def test_get_plan(self, plan_id):
        """测试获取学习计划详情"""
        if not plan_id:
            print("⚠️ 跳过计划详情测试 (无有效计划ID)")
            return
        
        print(f"\n📖 测试获取计划详情 (ID: {plan_id})...")
        
        try:
            response = self.session.get(f"{self.base_url}/api/learning-plans/{plan_id}")
            
            if response.status_code == 200:
                data = response.json()
                if data["success"]:
                    plan = data["data"]
                    print(f"✅ 计划详情获取成功!")
                    print(f"   - 主题: {plan['topic']}")
                    print(f"   - 描述: {plan['description'][:50]}...")
                    print(f"   - 状态: {plan['status']}")
                    print(f"   - 创建时间: {plan['created_at']}")
                    return True
                else:
                    print(f"❌ 获取详情失败: {data.get('message', '未知错误')}")
                    return False
            else:
                print(f"❌ API调用失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 获取详情异常: {e}")
            return False
    
    def test_record_progress(self, plan_id):
        """测试记录学习进度"""
        if not plan_id:
            print("⚠️ 跳过进度记录测试 (无有效计划ID)")
            return
        
        print(f"\n📊 测试记录学习进度 (计划ID: {plan_id})...")
        
        # 测试进度数据
        progress_data = {
            "plan_id": plan_id,
            "day_number": 1,
            "what_mastery": 80.0,
            "why_mastery": 70.0,
            "how_mastery": 60.0,
            "time_spent": 120,  # 2小时
            "focus_time": 100,
            "break_count": 2,
            "notes": "今天学习了JavaScript异步编程的基础概念，理解了回调函数的工作原理。",
            "mood_score": 4,
            "difficulty_rating": 3
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/learning-plans/{plan_id}/progress",
                json=progress_data
            )
            
            if response.status_code == 200:
                data = response.json()
                if data["success"]:
                    progress = data["data"]
                    print(f"✅ 学习进度记录成功!")
                    print(f"   - 进度ID: {progress['id']}")
                    print(f"   - 第{progress['day_number']}天")
                    print(f"   - 总体掌握度: {progress['overall_mastery']:.1f}%")
                    print(f"   - 学习时长: {progress['time_spent']}分钟")
                    return True
                else:
                    print(f"❌ 进度记录失败: {data.get('message', '未知错误')}")
                    return False
            else:
                print(f"❌ API调用失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 进度记录异常: {e}")
            return False
    
    def test_get_plans_list(self):
        """测试获取学习计划列表"""
        print("\n📋 测试获取学习计划列表...")
        
        try:
            response = self.session.get(f"{self.base_url}/api/learning-plans/")
            
            if response.status_code == 200:
                data = response.json()
                if data["success"]:
                    plans_data = data["data"]
                    plans = plans_data["plans"]
                    print(f"✅ 计划列表获取成功!")
                    print(f"   - 计划总数: {plans_data['total']}")
                    
                    for i, plan in enumerate(plans[:3], 1):  # 显示前3个
                        print(f"   {i}. {plan['topic']} ({plan['status']})")
                    
                    return True
                else:
                    print(f"❌ 获取列表失败: {data.get('message', '未知错误')}")
                    return False
            else:
                print(f"❌ API调用失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 获取列表异常: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 EduSynapse API 测试开始")
        print("=" * 50)
        
        # 测试结果统计
        results = []
        
        # 1. 健康检查
        results.append(("健康检查", self.test_health_check()))
        
        # 2. 生成学习计划
        plan_id = self.test_generate_plan()
        results.append(("生成学习计划", plan_id is not None))
        
        # 3. 获取计划详情
        results.append(("获取计划详情", self.test_get_plan(plan_id)))
        
        # 4. 记录学习进度
        results.append(("记录学习进度", self.test_record_progress(plan_id)))
        
        # 5. 获取计划列表
        results.append(("获取计划列表", self.test_get_plans_list()))
        
        # 输出测试结果
        print("\n" + "=" * 50)
        print("📊 测试结果汇总:")
        
        passed = 0
        total = len(results)
        
        for test_name, success in results:
            status = "✅ 通过" if success else "❌ 失败"
            print(f"   {test_name}: {status}")
            if success:
                passed += 1
        
        print(f"\n🎯 测试通过率: {passed}/{total} ({passed/total*100:.1f}%)")
        
        if passed == total:
            print("🎉 所有测试通过! EduSynapse API 工作正常!")
        else:
            print("⚠️ 部分测试失败，请检查服务器状态和配置")


def main():
    """主函数"""
    print("🚀 EduSynapse API 测试工具")
    
    # 检查服务器地址
    base_url = input("请输入API服务器地址 (默认: http://localhost:8000): ").strip()
    if not base_url:
        base_url = "http://localhost:8000"
    
    # 创建测试器并运行测试
    tester = EduSynapseAPITester(base_url)
    tester.run_all_tests()


if __name__ == "__main__":
    main()
