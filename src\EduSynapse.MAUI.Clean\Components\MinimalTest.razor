<h1>🧪 最简测试</h1>

<p>当前计数: @count</p>
<p>最后点击时间: @lastClickTime</p>
<p>点击次数: @clickCount</p>

<button @onclick="Increment">点击增加</button>

<div style="margin-top: 20px; padding: 10px; background: #f0f0f0; border-radius: 5px;">
    <h3>调试信息</h3>
    <p>组件初始化时间: @initTime</p>
    <p>当前时间: @DateTime.Now.ToString("HH:mm:ss.fff")</p>
</div>

@code {
    private int count = 0;
    private int clickCount = 0;
    private string lastClickTime = "未点击";
    private string initTime = "";

    protected override void OnInitialized()
    {
        initTime = DateTime.Now.ToString("HH:mm:ss.fff");
        System.Diagnostics.Debug.WriteLine($"🎯 MinimalTest 组件初始化: {initTime}");
    }

    private void Increment()
    {
        // 详细的调试输出
        var beforeCount = count;
        var beforeClickCount = clickCount;

        count++;
        clickCount++;
        lastClickTime = DateTime.Now.ToString("HH:mm:ss.fff");

        System.Diagnostics.Debug.WriteLine($"🔥 按钮点击事件触发！");
        System.Diagnostics.Debug.WriteLine($"   - 点击前计数: {beforeCount}");
        System.Diagnostics.Debug.WriteLine($"   - 点击后计数: {count}");
        System.Diagnostics.Debug.WriteLine($"   - 总点击次数: {clickCount}");
        System.Diagnostics.Debug.WriteLine($"   - 点击时间: {lastClickTime}");

        // 强制界面刷新
        StateHasChanged();
        System.Diagnostics.Debug.WriteLine($"   - StateHasChanged() 已调用");
    }
}
