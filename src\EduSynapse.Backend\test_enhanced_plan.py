"""
增强学习计划生成功能测试脚本
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 创建测试应用
app = FastAPI(title="Enhanced Learning Plan Test", version="1.0.0")

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {
        "message": "Enhanced Learning Plan Test",
        "endpoints": [
            "/plan-preview-example",
            "/enhanced-plan-example",
            "/daily-plan-example",
            "/milestones-example"
        ]
    }

@app.get("/plan-preview-example")
async def get_plan_preview_example():
    """学习计划预览示例"""
    return {
        "success": True,
        "topic": "Python编程基础",
        "preview": {
            "what_stage": {
                "core_definition": "Python是一种高级、解释型、通用编程语言，以简洁易读的语法和强大的生态系统著称。",
                "key_features": [
                    "简洁易读的语法",
                    "动态类型系统",
                    "自动内存管理"
                ],
                "learning_objectives": [
                    "理解Python的基本概念和特性",
                    "识别Python的应用场景"
                ],
                "visual_metaphor": "Python就像一把瑞士军刀，简单易用但功能强大。",
                "estimated_time": "30分钟"
            },
            "why_stage": {
                "importance": "Python是当今最流行的编程语言之一，广泛应用于Web开发、数据科学、人工智能等领域。",
                "practical_value": "学习Python可以快速开发原型，解决实际问题，并为进入热门技术领域打下基础。",
                "real_world_applications": [
                    "Web开发（Django, Flask）",
                    "数据分析（Pandas, NumPy）"
                ],
                "career_benefits": [
                    "提高就业竞争力",
                    "进入高薪技术岗位"
                ],
                "estimated_time": "30分钟"
            },
            "how_stage": {
                "learning_methods": [
                    "交互式学习",
                    "项目驱动学习",
                    "阅读官方文档"
                ],
                "step_by_step_guide": [
                    {"step": 1, "title": "安装Python", "description": "下载并安装Python解释器"},
                    {"step": 2, "title": "学习基础语法", "description": "变量、数据类型、控制流"},
                    {"step": 3, "title": "函数和模块", "description": "创建和使用函数、导入模块"}
                ],
                "practice_projects": [
                    {"name": "计算器程序", "description": "创建一个简单的计算器"},
                    {"name": "文件处理工具", "description": "处理文本文件的小工具"}
                ],
                "recommended_resources": [
                    {"type": "官方文档", "title": "Python官方教程"},
                    {"type": "在线课程", "title": "Python基础课程"},
                    {"type": "练习平台", "title": "编程练习网站"}
                ],
                "estimated_total_time": "4小时"
            },
            "personalization": {
                "difficulty_level": "beginner",
                "learner_profile": "visual",
                "adapted_for": "适合visual类型学习者的beginner难度内容"
            }
        },
        "estimated_duration": {
            "recommended_days": 17,
            "time_per_day_minutes": 120,
            "total_hours": 34.0,
            "difficulty_adjustment": 1.2,
            "stage_breakdown": {
                "what_stage": "4天",
                "why_stage": "4天",
                "how_stage": "9天"
            }
        }
    }

@app.get("/enhanced-plan-example")
async def get_enhanced_plan_example():
    """增强学习计划示例"""
    return {
        "success": True,
        "plan_id": "ai_plan_Python编程基础_visual_1642234567",
        "plan": {
            "topic": "Python编程基础",
            "description": "基于WWH框架的Python编程基础个性化学习计划",
            "difficulty_level": "beginner",
            "learner_profile": "visual",
            "duration_days": 17,
            "time_per_day_minutes": 120,
            "learning_goals": [
                "掌握Python基础语法",
                "能够编写简单程序",
                "理解面向对象编程概念"
            ],
            "wwh_framework": {
                "what": {
                    "core_definition": "Python是一种高级、解释型、通用编程语言...",
                    "framework_stage": "what",
                    "version": "2.0"
                },
                "why": {
                    "importance": "Python是当今最流行的编程语言之一...",
                    "framework_stage": "why",
                    "version": "2.0"
                },
                "how": {
                    "learning_methods": ["交互式学习", "项目驱动学习"],
                    "framework_stage": "how",
                    "version": "2.0"
                }
            },
            "milestones": [
                {
                    "id": 1,
                    "title": "概念掌握",
                    "description": "理解核心概念和基本原理",
                    "target_day": 4,
                    "stage": "what"
                },
                {
                    "id": 2,
                    "title": "价值认知",
                    "description": "理解学习价值和应用场景",
                    "target_day": 8,
                    "stage": "why"
                },
                {
                    "id": 3,
                    "title": "实践应用",
                    "description": "应用所学知识解决实际问题",
                    "target_day": 13,
                    "stage": "how"
                },
                {
                    "id": 4,
                    "title": "综合掌握",
                    "description": "综合应用所学知识和技能",
                    "target_day": 17,
                    "stage": "review"
                }
            ],
            "learning_path": [
                {
                    "phase": 1,
                    "title": "概念理解阶段",
                    "duration_percentage": 25,
                    "target_days": 4,
                    "stage": "what"
                },
                {
                    "phase": 2,
                    "title": "价值认知阶段",
                    "duration_percentage": 25,
                    "target_days": 4,
                    "stage": "why"
                },
                {
                    "phase": 3,
                    "title": "方法学习阶段",
                    "duration_percentage": 25,
                    "target_days": 4,
                    "stage": "how"
                },
                {
                    "phase": 4,
                    "title": "实践应用阶段",
                    "duration_percentage": 25,
                    "target_days": 5,
                    "stage": "how"
                }
            ],
            "assessment_strategy": {
                "difficulty_level": "beginner",
                "complexity": "基础",
                "focus": "概念理解和基本应用",
                "methods": ["可视化项目", "图表分析", "概念图评估"],
                "success_criteria": [
                    "掌握核心概念和原理",
                    "能够应用所学知识解决问题",
                    "完成所有实践项目"
                ]
            },
            "metadata": {
                "ai_generated": True,
                "framework_version": "2.0",
                "estimated_total_hours": 34.0,
                "completion_rate_target": 0.85,
                "personalization_level": "high"
            }
        },
        "message": "个性化学习计划生成成功！适合visual类型学习者的beginner难度Python编程基础学习计划",
        "generation_time": 2.45
    }

@app.get("/daily-plan-example")
async def get_daily_plan_example():
    """智能每日计划示例"""
    return {
        "daily_plans": [
            {
                "day": 1,
                "stage": "what",
                "focus": "核心概念和定义",
                "description": "理解核心概念和定义，建立基础认知",
                "learning_objectives": "理解Python的基本概念和特性",
                "activities": ["概念图绘制", "视频学习", "图表分析"],
                "estimated_minutes": 120,
                "resources": [
                    {"type": "concept", "title": "核心概念和定义学习资料"}
                ],
                "completion_criteria": "能够清晰解释核心概念和定义"
            },
            {
                "day": 8,
                "stage": "how",
                "focus": "方法学习",
                "description": "学习实践方法: 安装Python, 学习基础语法",
                "steps": [
                    {"step": 1, "title": "安装Python", "description": "下载并安装Python解释器", "duration": "15分钟"},
                    {"step": 2, "title": "学习基础语法", "description": "变量、数据类型、控制流", "duration": "2小时"}
                ],
                "activities": ["步骤演示", "流程图学习", "可视化实践"],
                "estimated_minutes": 120,
                "resources": [
                    {"type": "tutorial", "title": "实践指南: 安装Python"}
                ],
                "completion_criteria": "完成所有步骤并理解方法"
            },
            {
                "day": 15,
                "stage": "how",
                "focus": "项目实践",
                "description": "实践项目: 计算器程序",
                "projects": [
                    {"name": "计算器程序", "description": "创建一个简单的计算器", "difficulty": "beginner", "estimated_time": "2小时"}
                ],
                "estimated_minutes": 120,
                "resources": [
                    {"type": "project", "title": "项目指南: 计算器程序"}
                ],
                "completion_criteria": "完成项目并应用所学知识"
            }
        ]
    }

@app.get("/milestones-example")
async def get_milestones_example():
    """学习里程碑示例"""
    return {
        "milestones": [
            {
                "id": 1,
                "title": "概念掌握",
                "description": "理解核心概念和基本原理",
                "target_day": 4,
                "completion_criteria": [
                    "能够准确解释核心定义",
                    "理解关键特征和基本要素",
                    "识别相关概念和关系"
                ],
                "assessment_method": "概念测验",
                "stage": "what"
            },
            {
                "id": 2,
                "title": "价值认知",
                "description": "理解学习价值和应用场景",
                "target_day": 8,
                "completion_criteria": [
                    "理解学习主题的重要性",
                    "能够解释实际应用价值",
                    "识别行业相关性和职业益处"
                ],
                "assessment_method": "案例分析",
                "stage": "why"
            },
            {
                "id": 3,
                "title": "实践应用",
                "description": "应用所学知识解决实际问题",
                "target_day": 13,
                "completion_criteria": [
                    "完成实践项目",
                    "解决实际问题",
                    "应用所学技能"
                ],
                "assessment_method": "项目评估",
                "stage": "how"
            },
            {
                "id": 4,
                "title": "综合掌握",
                "description": "综合应用所学知识和技能",
                "target_day": 17,
                "completion_criteria": [
                    "能够综合应用所有概念和方法",
                    "独立完成复杂任务",
                    "解决实际问题并评估结果"
                ],
                "assessment_method": "综合评估",
                "stage": "review"
            }
        ]
    }

if __name__ == "__main__":
    # 检查端口是否可用
    def is_port_available(port):
        import socket
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            s.bind(("0.0.0.0", port))
            s.close()
            return True
        except OSError:
            return False
    
    # 选择可用端口
    port = 8003 if is_port_available(8003) else 8004
    
    print(f"🚀 启动增强学习计划测试服务器: http://localhost:{port}")
    print(f"📚 API文档: http://localhost:{port}/docs")
    
    uvicorn.run(app, host="0.0.0.0", port=port)
