"""
EduSynapse WWH Learning Plan Generation Engine
WWH框架学习计划生成引擎
"""

import json
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import openai
from app.core.config import settings


class WWHEngine:
    """
    WWH (What-Why-How) 学习计划生成引擎

    基于WWH框架生成结构化的个性化学习计划:
    - What: 学习什么 (核心概念、定义、特征)
    - Why: 为什么学 (历史背景、实用价值、学习动机)
    - How: 怎么学 (实践项目、代码示例、练习)
    """

    def __init__(self):
        self.client = None
        self._initialize_client()
        self._setup_prompts()

    def _initialize_client(self):
        """初始化 OpenAI 客户端"""
        try:
            if settings.has_openai_key:
                # 设置自定义 API 基地址
                if settings.openai_api_base:
                    print(
                        f"🔗 使用自定义 OpenAI API 基地址: {settings.openai_api_base}"
                    )

                self.client = openai.OpenAI(
                    api_key=settings.openai_api_key, base_url=settings.openai_api_base
                )
                print(f"✅ OpenAI客户端初始化成功: {settings.openai_model}")
            else:
                print("❌ 未配置OpenAI API密钥")
                raise ValueError("需要配置AI API密钥")
        except Exception as e:
            print(f"❌ OpenAI客户端初始化失败: {e}")
            raise

    def _setup_prompts(self):
        """设置提示词模板"""

        # WWH完整计划生成提示词
        self.wwh_plan_prompt = ChatPromptTemplate.from_messages(
            [
                SystemMessage(
                    content="""你是一位专业的学习规划师，擅长使用WWH框架(What-Why-How)设计个性化学习计划。

WWH框架说明:
- What (学什么): 核心概念、定义、关键特征、基础知识点
- Why (为什么学): 历史背景、实用价值、学习动机、应用场景
- How (怎么学): 实践项目、代码示例、练习题、学习方法

请严格按照JSON格式返回，确保结构完整且实用。"""
                ),
                HumanMessage(
                    content="""请为主题"{topic}"生成一个{duration_days}天的学习计划。

要求:
1. 难度级别: {difficulty_level}
2. 每日学习时长: {daily_hours}小时
3. 学习风格偏好: {learning_style}

请返回以下JSON格式:
{{
    "what": {{
        "core_concepts": ["概念1", "概念2", "概念3"],
        "key_features": ["特征1", "特征2"],
        "definitions": {{
            "概念1": "详细定义...",
            "概念2": "详细定义..."
        }},
        "prerequisites": ["前置知识1", "前置知识2"]
    }},
    "why": {{
        "historical_context": "历史背景和发展...",
        "practical_importance": "实用价值和重要性...",
        "learning_motivation": "学习动机和目标...",
        "career_benefits": "职业发展益处...",
        "real_world_applications": ["应用场景1", "应用场景2"]
    }},
    "how": {{
        "practice_projects": [
            {{"title": "项目1", "description": "项目描述", "difficulty": "easy"}},
            {{"title": "项目2", "description": "项目描述", "difficulty": "medium"}}
        ],
        "code_examples": [
            {{"title": "示例1", "language": "python", "description": "示例说明"}},
            {{"title": "示例2", "language": "python", "description": "示例说明"}}
        ],
        "exercises": [
            {{"title": "练习1", "type": "theory", "description": "练习描述"}},
            {{"title": "练习2", "type": "practical", "description": "练习描述"}}
        ],
        "learning_methods": ["方法1", "方法2", "方法3"],
        "recommended_resources": [
            {{"type": "book", "title": "推荐书籍", "author": "作者"}},
            {{"type": "video", "title": "推荐视频", "url": "链接"}}
        ]
    }},
    "daily_breakdown": [
        {{
            "day": 1,
            "focus": "what",
            "topics": ["今日主题1", "今日主题2"],
            "tasks": [
                {{"task": "任务1", "type": "reading", "estimated_minutes": 60}},
                {{"task": "任务2", "type": "practice", "estimated_minutes": 90}}
            ],
            "goals": ["目标1", "目标2"],
            "assessment": "如何评估今日学习效果"
        }}
    ]
}}

请确保:
1. 内容具体实用，避免空泛描述
2. 难度递进，符合学习规律
3. 理论与实践相结合
4. 每日任务时长符合设定的学习时长"""
                ),
            ]
        )

        # 单独的What阶段提示词
        self.what_prompt = PromptTemplate(
            input_variables=["topic", "difficulty_level"],
            template="""作为学习专家，请详细分析学习主题"{topic}"的核心内容(What阶段)。

难度级别: {difficulty_level}

请提供:
1. 5-8个核心概念
2. 每个概念的准确定义
3. 关键特征和属性
4. 必要的前置知识

以JSON格式返回，确保内容准确专业。""",
        )

    async def generate_learning_plan(
        self,
        topic: str,
        duration_days: int = 14,
        difficulty_level: str = "medium",
        daily_hours: float = 2.0,
        learning_style: str = "balanced",
    ) -> Dict[str, Any]:
        """
        生成完整的WWH学习计划

        Args:
            topic: 学习主题
            duration_days: 学习天数
            difficulty_level: 难度级别 (easy/medium/hard)
            daily_hours: 每日学习时长
            learning_style: 学习风格 (theoretical/practical/balanced)

        Returns:
            包含WWH结构的完整学习计划
        """
        try:
            print(f"🚀 开始生成学习计划: {topic}")

            # 格式化提示词
            messages = self.wwh_plan_prompt.format_messages(
                topic=topic,
                duration_days=duration_days,
                difficulty_level=difficulty_level,
                daily_hours=daily_hours,
                learning_style=learning_style,
            )

            # 调用LLM生成计划
            response = await self._call_llm_async(messages)

            # 解析JSON响应
            plan_data = self._parse_json_response(response.content)

            # 验证和补充计划数据
            validated_plan = self._validate_and_enhance_plan(
                plan_data, topic, duration_days, daily_hours
            )

            print(f"✅ 学习计划生成成功: {topic}")
            return validated_plan

        except Exception as e:
            print(f"❌ 学习计划生成失败: {e}")
            # 返回基础的备用计划
            return self._create_fallback_plan(topic, duration_days, daily_hours)

    async def _call_llm_async(self, messages):
        """异步调用LLM"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.llm, messages)

    def _parse_json_response(self, response_text: str) -> Dict[str, Any]:
        """解析LLM返回的JSON响应"""
        try:
            # 清理响应文本
            cleaned_text = response_text.strip()
            if cleaned_text.startswith("```json"):
                cleaned_text = cleaned_text[7:]
            if cleaned_text.endswith("```"):
                cleaned_text = cleaned_text[:-3]

            return json.loads(cleaned_text)
        except json.JSONDecodeError as e:
            print(f"⚠️ JSON解析失败: {e}")
            print(f"原始响应: {response_text[:200]}...")
            raise ValueError("LLM响应格式不正确")

    def _validate_and_enhance_plan(
        self,
        plan_data: Dict[str, Any],
        topic: str,
        duration_days: int,
        daily_hours: float,
    ) -> Dict[str, Any]:
        """验证和增强学习计划数据"""

        # 确保基本结构存在
        if "what" not in plan_data:
            plan_data["what"] = {"core_concepts": [], "definitions": {}}
        if "why" not in plan_data:
            plan_data["why"] = {"practical_importance": f"学习{topic}的重要性"}
        if "how" not in plan_data:
            plan_data["how"] = {"practice_projects": [], "exercises": []}

        # 生成或补充每日计划
        if (
            "daily_breakdown" not in plan_data
            or len(plan_data["daily_breakdown"]) < duration_days
        ):
            plan_data["daily_breakdown"] = self._generate_daily_breakdown(
                plan_data, duration_days, daily_hours
            )

        # 添加元数据
        plan_data["metadata"] = {
            "generated_at": datetime.now().isoformat(),
            "topic": topic,
            "duration_days": duration_days,
            "daily_hours": daily_hours,
            "total_estimated_hours": duration_days * daily_hours,
        }

        return plan_data

    def _generate_daily_breakdown(
        self, plan_data: Dict[str, Any], duration_days: int, daily_hours: float
    ) -> List[Dict[str, Any]]:
        """生成每日学习计划分解"""
        daily_plans = []
        daily_minutes = int(daily_hours * 60)

        # WWH阶段分配 (前1/3 What, 中1/3 Why, 后1/3 How)
        what_days = max(1, duration_days // 3)
        why_days = max(1, duration_days // 3)
        how_days = duration_days - what_days - why_days

        for day in range(1, duration_days + 1):
            if day <= what_days:
                focus = "what"
                topics = plan_data.get("what", {}).get("core_concepts", [])[:2]
            elif day <= what_days + why_days:
                focus = "why"
                topics = ["理解背景和重要性", "探索应用场景"]
            else:
                focus = "how"
                topics = ["实践练习", "项目实战"]

            daily_plan = {
                "day": day,
                "focus": focus,
                "topics": topics,
                "tasks": [
                    {
                        "task": f"学习{focus}阶段内容",
                        "type": "study",
                        "estimated_minutes": daily_minutes // 2,
                    },
                    {
                        "task": f"完成{focus}阶段练习",
                        "type": "practice",
                        "estimated_minutes": daily_minutes // 2,
                    },
                ],
                "goals": [f"掌握{focus}阶段核心内容"],
                "assessment": f"通过练习验证{focus}阶段理解程度",
            }

            daily_plans.append(daily_plan)

        return daily_plans

    def _create_fallback_plan(
        self, topic: str, duration_days: int, daily_hours: float
    ) -> Dict[str, Any]:
        """创建备用学习计划"""
        return {
            "what": {
                "core_concepts": [f"{topic}基础概念", f"{topic}核心原理"],
                "definitions": {f"{topic}基础概念": f"关于{topic}的基础定义和概念"},
            },
            "why": {
                "practical_importance": f"学习{topic}对个人和职业发展的重要性",
                "learning_motivation": f"掌握{topic}技能的动机和目标",
            },
            "how": {
                "practice_projects": [
                    {"title": f"{topic}入门项目", "description": "基础实践项目"}
                ],
                "exercises": [
                    {"title": f"{topic}基础练习", "description": "基础概念练习"}
                ],
            },
            "daily_breakdown": self._generate_daily_breakdown(
                {}, duration_days, daily_hours
            ),
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "topic": topic,
                "duration_days": duration_days,
                "daily_hours": daily_hours,
                "is_fallback": True,
            },
        }
