@using EduSynapse.MAUI.ViewModels
@using EduSynapse.MAUI.Models
@using MudBlazor
@inject ProgressViewModel ViewModel

<div>
    <!-- 进度历史列表 -->
    @if (ViewModel.ProgressRecords.Any())
    {
        <MudText Typo="Typo.h6" Class="mb-4">
            <MudIcon Icon="@Icons.Material.Filled.History" Class="mr-2" />
            学习历史记录 (@ViewModel.ProgressRecords.Count �?
        </MudText>
        
        <MudGrid>
            @foreach (var progress in ViewModel.ProgressRecords.OrderByDescending(p => p.DayNumber))
            {
                <MudItem xs="12" sm="6" md="4">
                    <MudCard Elevation="2" Class="pa-4 mb-3 elevation-hover">
                        <!-- 卡片头部 -->
                        <div class="d-flex justify-space-between align-center mb-3">
                            <MudChip T="string" Color="Color.Primary" Size="Size.Small">
                                �?@progress.DayNumber �?                            </MudChip>
                            @if (progress.CompletedAt.HasValue)
                            {
                                <MudText Typo="Typo.caption" Color="Color.Secondary">
                                    @progress.CompletedAt.Value.ToString("MM/dd HH:mm")
                                </MudText>
                            }
                        </div>
                        
                        <!-- WWH掌握�?-->
                        <div class="mb-3">
                            <div class="d-flex justify-space-between align-center mb-1">
                                <MudText Typo="Typo.body2">总体掌握度</MudText>
                                <MudText Typo="Typo.body2" Color="Color.Primary">
                                    @progress.OverallMastery.ToString("F1")%
                                </MudText>
                            </div>
                            <MudProgressLinear Color="Color.Primary" 
                                               Value="@progress.OverallMastery" 
                                               Class="mb-2" />
                            
                            <!-- WWH分项显示 -->
                            <div class="d-flex justify-space-between">
                                <MudChip T="string" Size="Size.Small" Color="Color.Info" Variant="Variant.Text">
                                    W: @progress.WhatMastery.ToString("F0")%
                                </MudChip>
                                <MudChip T="string" Size="Size.Small" Color="Color.Warning" Variant="Variant.Text">
                                    W: @progress.WhyMastery.ToString("F0")%
                                </MudChip>
                                <MudChip T="string" Size="Size.Small" Color="Color.Success" Variant="Variant.Text">
                                    H: @progress.HowMastery.ToString("F0")%
                                </MudChip>
                            </div>
                        </div>
                        
                        <!-- 学习时间和体�?-->
                        <div class="d-flex justify-space-between align-center mb-2">
                            <div class="d-flex align-center">
                                <MudIcon Icon="@Icons.Material.Filled.AccessTime" Size="Size.Small" Class="mr-1" />
                                <MudText Typo="Typo.body2">
                                    @progress.TimeSpentTimeSpan.ToString(@"h\:mm")
                                </MudText>
                            </div>
                            
                            @if (progress.MoodScore.HasValue)
                            {
                                <div class="d-flex align-center">
                                    <MudText Typo="Typo.body2" Class="mr-1">
                                        @GetMoodEmoji(progress.MoodScore.Value)
                                    </MudText>
                                </div>
                            }
                        </div>
                        
                        <!-- 学习笔记预览 -->
                        @if (!string.IsNullOrEmpty(progress.Notes))
                        {
                            <MudExpansionPanels Elevation="0">
                                <MudExpansionPanel Text="查看笔记">
                                    <MudText Typo="Typo.body2" Class="pa-2" Style="background-color: var(--mud-palette-background-grey);">
                                        @progress.Notes
                                    </MudText>
                                </MudExpansionPanel>
                            </MudExpansionPanels>
                        }
                        
                        <!-- 操作按钮 -->
                        <div class="d-flex justify-end mt-3">
                            <MudButton Size="Size.Small" 
                                       Variant="Variant.Text" 
                                       Color="Color.Primary"
                                       StartIcon="@Icons.Material.Filled.Edit"
                                       OnClick="@(() => EditProgress(progress.DayNumber))">
                                编辑
                            </MudButton>
                        </div>
                    </MudCard>
                </MudItem>
            }
        </MudGrid>
        
        <!-- 进度趋势�?-->
        <MudCard Elevation="2" Class="pa-4 mt-6">
            <MudText Typo="Typo.h6" Class="mb-4">
                <MudIcon Icon="@Icons.Material.Filled.TrendingUp" Class="mr-2" />
                学习进度趋势
            </MudText>
            
            <!-- WWH掌握度趋�?-->
            <div class="mb-4">
                <MudText Typo="Typo.subtitle1" Class="mb-2">WWH掌握度变化</MudText>
                <div class="progress-chart">
                    @for (int i = 0; i < ViewModel.ProgressRecords.Count; i++)
                    {
                        var progress = ViewModel.ProgressRecords.OrderBy(p => p.DayNumber).ElementAt(i);
                        <div class="chart-point" style="left: @(i * 100.0 / Math.Max(1, ViewModel.ProgressRecords.Count - 1))%">
                            <div class="point-marker" style="background-color: @GetTrendColor(progress.OverallMastery)"></div>
                            <div class="point-label">@progress.DayNumber</div>
                            <div class="point-value">@progress.OverallMastery.ToString("F0")%</div>
                        </div>
                    }
                </div>
            </div>
            
            <!-- 学习时间趋势 -->
            <div class="mb-4">
                <MudText Typo="Typo.subtitle1" Class="mb-2">每日学习时长</MudText>
                <div class="time-chart">
                    @foreach (var progress in ViewModel.ProgressRecords.OrderBy(p => p.DayNumber))
                    {
                        var heightPercent = Math.Max(5, progress.TimeSpent / 240.0 * 100); // 最�?小时
                        <div class="time-bar" style="height: @heightPercent%">
                            <div class="bar-label">@progress.DayNumber</div>
                            <div class="bar-value">@(progress.TimeSpent / 60.0).ToString("F1")h</div>
                        </div>
                    }
                </div>
            </div>
        </MudCard>
    }
    else
    {
        <!-- 无数据提�?-->
        <div class="text-center pa-8">
            <MudIcon Icon="@Icons.Material.Filled.HistoryEdu" Size="Size.Large" Color="Color.Secondary" Class="mb-4" />
            <MudText Typo="Typo.h6" Color="Color.Secondary" Class="mb-2">暂无学习记录</MudText>
            <MudText Typo="Typo.body2" Color="Color.Secondary" Class="mb-4">
                开始记录您的学习进度，追踪学习成果
            </MudText>
            <MudButton Variant="Variant.Filled" 
                       Color="Color.Primary"
                       StartIcon="@Icons.Material.Filled.Add"
                       OnClick="@(() => SwitchToRecordTab())">
                记录今日进度
            </MudButton>
        </div>
    }
</div>

<style>
    .progress-chart {
        position: relative;
        height: 100px;
        border-bottom: 2px solid #e0e0e0;
        margin: 20px 0;
    }
    
    .chart-point {
        position: absolute;
        bottom: 0;
        transform: translateX(-50%);
    }
    
    .point-marker {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin: 0 auto 5px;
        border: 2px solid white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }
    
    .point-label {
        font-size: 12px;
        text-align: center;
        color: #666;
    }
    
    .point-value {
        font-size: 10px;
        text-align: center;
        color: #999;
        margin-top: 2px;
    }
    
    .time-chart {
        display: flex;
        align-items: flex-end;
        height: 120px;
        gap: 8px;
        padding: 10px 0;
        border-bottom: 2px solid #e0e0e0;
    }
    
    .time-bar {
        flex: 1;
        background: linear-gradient(to top, #1976d2, #42a5f5);
        border-radius: 4px 4px 0 0;
        position: relative;
        min-height: 20px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        color: white;
        font-size: 10px;
        padding: 4px 2px;
    }
    
    .bar-label {
        margin-top: auto;
        font-weight: bold;
    }
    
    .bar-value {
        margin-bottom: auto;
        opacity: 0.9;
    }
    
    .elevation-hover {
        transition: box-shadow 0.2s ease-in-out;
    }
    
    .elevation-hover:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
    }
</style>

@code {
    private string GetMoodEmoji(int moodScore) => moodScore switch
    {
        1 => "😞",
        2 => "😕",
        3 => "😐",
        4 => "😊",
        5 => "😄",
        _ => "😐"
    };
    
    private string GetTrendColor(double mastery) => mastery switch
    {
        >= 80 => "#4caf50",
        >= 60 => "#ff9800", 
        >= 40 => "#2196f3",
        _ => "#f44336"
    };
    
    private void EditProgress(int dayNumber)
    {
        ViewModel.SelectDayCommand.ExecuteAsync(dayNumber);
        // 这里需要切换到记录进度标签页的逻辑
        SwitchToRecordTab();
    }
    
    private void SwitchToRecordTab()
    {
        // 这里需要实现切换到记录进度标签页的逻辑
        // 可以通过事件或状态管理来实现
    }
}
