@using EduSynapse.MAUI.ViewModels
@using MudBlazor
@using Microsoft.JSInterop
@using Microsoft.AspNetCore.Components
@inherits LayoutComponentBase
@inject MainViewModel MainViewModel
@inject IJSRuntime JSRuntime

<MudLayout>
    <MudAppBar Elevation="1">
        <MudIconButton Icon="@Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start" OnClick="@ToggleDrawer" />
        <MudSpacer />
        <MudText Typo="Typo.h6">EduSynapse</MudText>
        <MudSpacer />
        
        <!-- API连接状态指示器 -->
        <MudTooltip Text="@MainViewModel.ApiStatus">
            <MudIcon Icon="@(MainViewModel.IsApiConnected ? Icons.Material.Filled.CloudDone : Icons.Material.Filled.CloudOff)" 
                     Color="@(MainViewModel.IsApiConnected ? Color.Primary : Color.Secondary)" />
        </MudTooltip>
        
        <MudIconButton Icon="@Icons.Material.Filled.Refresh" Color="Color.Inherit" OnClick="@MainViewModel.RefreshCommand.ExecuteAsync" />
    </MudAppBar>

    <MudDrawer Open="_drawerOpen"
               OpenChanged="@((bool val) => _drawerOpen = val)"
               Elevation="1"
               Variant="@DrawerVariant.Responsive"
               ClipMode="DrawerClipMode.Always">
        <MudDrawerHeader>
            <MudText Typo="Typo.h6">📚 智能学习系统</MudText>
        </MudDrawerHeader>
        <MudNavMenu>
            <MudNavLink Href="/" Match="NavLinkMatch.All" Icon="@Icons.Material.Filled.Dashboard">
                仪表板
            </MudNavLink>
            <MudNavLink Href="/plans" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.School">
                学习计划
            </MudNavLink>
            <MudNavLink Href="/progress" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.TrendingUp">
                学习进度
            </MudNavLink>
            <MudNavLink Href="/create" Match="NavLinkMatch.Prefix" Icon="@Icons.Material.Filled.Add">
                创建计划
            </MudNavLink>
            
            <MudDivider Class="my-2" />
            
            <MudNavGroup Text="设置" Icon="@Icons.Material.Filled.Settings" Expanded="false">
                <MudNavLink Href="/settings/api" Icon="@Icons.Material.Filled.Api">
                    API配置
                </MudNavLink>
                <MudNavLink Href="/settings/preferences" Icon="@Icons.Material.Filled.Tune">
                    偏好设置
                </MudNavLink>
            </MudNavGroup>
            
            <MudNavGroup Text="帮助" Icon="@Icons.Material.Filled.Help" Expanded="false">
                <MudNavLink Href="/help/guide" Icon="@Icons.Material.Filled.MenuBook">
                    使用指南
                </MudNavLink>
                <MudNavLink Href="/help/about" Icon="@Icons.Material.Filled.Info">
                    关于
                </MudNavLink>
            </MudNavGroup>
        </MudNavMenu>
    </MudDrawer>

    <MudMainContent>
        <MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="my-4">
            <!-- 错误消息显示 -->
            @if (!string.IsNullOrEmpty(MainViewModel.ErrorMessage))
            {
                <MudAlert Severity="Severity.Error" 
                          Variant="Variant.Filled" 
                          ShowCloseIcon="true" 
                          CloseIconClicked="@MainViewModel.ClearErrorCommand.Execute"
                          Class="mb-4">
                    @MainViewModel.ErrorMessage
                </MudAlert>
            }

            <!-- 加载指示器 -->
            @if (MainViewModel.IsLoading)
            {
                <MudProgressLinear Color="Color.Primary" Indeterminate="true" Class="mb-4" />
            }

            <!-- API连接警告 -->
            @if (!MainViewModel.IsApiConnected)
            {
                <MudAlert Severity="Severity.Warning" Variant="Variant.Filled" Class="mb-4">
                    <MudText>⚠️ 无法连接到后端服务</MudText>
                    <MudText Typo="Typo.body2">请确保后端服务正在运行 (http://localhost:8000)</MudText>
                    <MudButton Variant="Variant.Text" 
                               Color="Color.Inherit" 
                               StartIcon="@Icons.Material.Filled.Refresh"
                               OnClick="@MainViewModel.CheckApiConnectionCommand.ExecuteAsync"
                               Class="mt-2">
                        重新连接
                    </MudButton>
                </MudAlert>
            }

            <!-- 页面内容 -->
            <div class="fade-in">
                @ChildContent
            </div>
        </MudContainer>
    </MudMainContent>
</MudLayout>

@code {
    [Parameter] public RenderFragment? ChildContent { get; set; }

    private bool _drawerOpen = true;

    protected override async Task OnInitializedAsync()
    {
        await MainViewModel.InitializeAsync();
    }

    private void ToggleDrawer()
    {
        _drawerOpen = !_drawerOpen;
    }
}
