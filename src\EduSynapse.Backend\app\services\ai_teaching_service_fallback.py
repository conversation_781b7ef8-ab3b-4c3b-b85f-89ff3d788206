"""
EduSynapse AI Teaching Service - Fallback Mode
当AI依赖包不可用时的备用教学服务
"""

import asyncio
import json
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from ..core.config import settings


class TeacherType(str, Enum):
    """AI教师类型枚举"""
    SOCRATIC = "socratic"          # 苏格拉底式
    CASE_DRIVEN = "case_driven"    # 案例驱动
    GAMIFIED = "gamified"          # 游戏化


class LearningStage(str, Enum):
    """学习阶段枚举（WWH框架）"""
    WHAT = "what"  # 是什么
    WHY = "why"    # 为什么
    HOW = "how"    # 怎么做


@dataclass
class TeachingContext:
    """教学上下文"""
    student_id: str
    topic: str
    current_stage: LearningStage
    mastery_level: float  # 0-1
    session_history: List[Dict[str, Any]]
    error_patterns: List[str]
    time_spent: timedelta
    last_interaction: datetime


class WWHFrameworkEngine:
    """WWH教学框架引擎 - 备用模式"""
    
    def __init__(self):
        self.ai_enabled = False
        print("📝 WWH框架引擎运行在备用模式")
        
    async def generate_what_content(self, topic: str, difficulty: str) -> Dict[str, Any]:
        """生成What阶段内容 - 备用版本"""
        return {
            "core_definition": f"{topic}是一个重要的概念，需要深入理解其基本定义和核心特征。",
            "key_features": [
                f"{topic}的主要特征包括其基本属性和行为模式",
                f"{topic}在实际应用中表现出的典型特点",
                f"{topic}与相关概念的区别和联系"
            ],
            "basic_elements": [
                f"{topic}的基本组成部分",
                f"{topic}的核心要素和结构"
            ],
            "related_concepts": [
                f"与{topic}相关的基础概念",
                f"理解{topic}需要掌握的前置知识"
            ],
            "difficulty_level": difficulty,
            "generated_by": "fallback_mode"
        }
    
    async def generate_why_content(self, topic: str, what_content: Dict) -> Dict[str, Any]:
        """生成Why阶段内容 - 备用版本"""
        return {
            "historical_background": f"{topic}的发展历程体现了人类对这一领域认识的不断深化。",
            "importance": f"学习{topic}对于掌握相关技能和知识体系具有重要意义。",
            "practical_value": f"{topic}在实际工作和学习中能够帮助解决具体问题，提高效率。",
            "learning_motivation": f"掌握{topic}将为进一步学习和实践奠定坚实基础。",
            "real_world_applications": [
                f"{topic}在日常工作中的应用场景",
                f"{topic}解决实际问题的典型案例"
            ],
            "generated_by": "fallback_mode"
        }
    
    async def generate_how_content(self, topic: str, what_content: Dict, why_content: Dict) -> Dict[str, Any]:
        """生成How阶段内容 - 备用版本"""
        return {
            "learning_methods": [
                f"通过理论学习掌握{topic}的基本概念",
                f"通过实践练习加深对{topic}的理解",
                f"通过案例分析学习{topic}的应用方法"
            ],
            "practice_projects": [
                f"{topic}基础练习项目",
                f"{topic}进阶应用项目"
            ],
            "code_examples": [
                f"# {topic}基础示例\n# 这里是{topic}的基本用法演示\nprint('学习{topic}的基础示例')"
            ],
            "exercises": [
                f"练习1: 理解{topic}的基本概念",
                f"练习2: 应用{topic}解决简单问题",
                f"练习3: 综合运用{topic}完成项目"
            ],
            "recommended_resources": [
                f"{topic}官方文档和教程",
                f"{topic}相关书籍和在线课程",
                f"{topic}实践项目和开源代码"
            ],
            "generated_by": "fallback_mode"
        }


class MultiAgentTeachingSystem:
    """多代理教学系统 - 备用模式"""
    
    def __init__(self):
        self.ai_enabled = False
        print("📝 多代理教学系统运行在备用模式")
    
    async def conduct_teaching_session(
        self, 
        context: TeachingContext, 
        user_input: str
    ) -> Tuple[str, TeacherType]:
        """进行教学会话 - 备用版本"""
        
        # 根据学习阶段选择教学风格
        if context.current_stage == LearningStage.WHAT:
            teacher_type = TeacherType.CASE_DRIVEN
            response = self._generate_what_response(context.topic, user_input)
        elif context.current_stage == LearningStage.WHY:
            teacher_type = TeacherType.SOCRATIC
            response = self._generate_why_response(context.topic, user_input)
        else:  # HOW
            teacher_type = TeacherType.CASE_DRIVEN
            response = self._generate_how_response(context.topic, user_input)
        
        return response, teacher_type
    
    def _generate_what_response(self, topic: str, user_input: str) -> str:
        """生成What阶段回复"""
        return f"""
关于{topic}的问题："{user_input}"

让我为你解释{topic}的基本概念：

🔍 **定义**: {topic}是一个重要的概念，它具有特定的特征和应用场景。

📋 **主要特点**:
• 具有明确的定义和边界
• 在特定领域中发挥重要作用
• 与其他相关概念有清晰的关系

💡 **理解要点**:
为了更好地理解{topic}，建议从基础概念开始，逐步深入到具体应用。

你还有什么关于{topic}基本概念的问题吗？
        """.strip()
    
    def _generate_why_response(self, topic: str, user_input: str) -> str:
        """生成Why阶段回复"""
        return f"""
关于{topic}的问题："{user_input}"

让我们探讨为什么要学习{topic}：

🎯 **重要性**: 
{topic}在现代学习和工作中扮演着重要角色，掌握它能够：
• 提高解决问题的能力
• 增强专业技能水平
• 为进一步学习奠定基础

📈 **实际价值**:
学习{topic}的实际好处包括：
• 能够应用到实际工作中
• 提高学习和工作效率
• 增强竞争优势

🤔 **思考**: 你认为{topic}在你的学习或工作中可能有哪些具体应用？
        """.strip()
    
    def _generate_how_response(self, topic: str, user_input: str) -> str:
        """生成How阶段回复"""
        return f"""
关于{topic}的问题："{user_input}"

让我为你提供学习{topic}的具体方法：

📚 **学习步骤**:
1. **理论基础**: 先掌握{topic}的基本概念和原理
2. **实践练习**: 通过具体练习加深理解
3. **项目应用**: 在实际项目中应用所学知识

💻 **实践建议**:
• 从简单的例子开始练习
• 逐步增加复杂度
• 多做实际项目来巩固知识

🔧 **学习资源**:
• 查阅官方文档和教程
• 参考优秀的开源项目
• 加入相关的学习社区

你想从哪个具体方面开始学习{topic}？
        """.strip()


class IntelligentTeachingService:
    """智能教学服务 - 备用模式"""
    
    def __init__(self):
        self.wwh_engine = WWHFrameworkEngine()
        self.multi_agent_system = MultiAgentTeachingSystem()
        self.active_sessions: Dict[str, TeachingContext] = {}
        print("📝 智能教学服务运行在备用模式")
        
    async def generate_learning_plan(
        self, 
        topic: str, 
        difficulty_level: str = "intermediate",
        duration_days: int = 14
    ) -> Dict[str, Any]:
        """生成智能学习计划 - 备用版本"""
        
        print(f"📝 生成{topic}的备用学习计划")
        
        # 生成WWH框架内容
        what_content = await self.wwh_engine.generate_what_content(topic, difficulty_level)
        why_content = await self.wwh_engine.generate_why_content(topic, what_content)
        how_content = await self.wwh_engine.generate_how_content(topic, what_content, why_content)
        
        # 构建学习计划
        plan = {
            "topic": topic,
            "description": f"基于WWH框架的{topic}学习计划（备用模式生成）",
            "difficulty_level": difficulty_level,
            "duration_days": duration_days,
            "wwh_framework": {
                "what": what_content,
                "why": why_content,
                "how": how_content
            },
            "daily_plans": self._generate_daily_plans(duration_days),
            "ai_generated": False,  # 标记为非AI生成
            "fallback_mode": True,
            "created_at": datetime.now().isoformat()
        }
        
        return plan
    
    async def start_teaching_session(
        self, 
        student_id: str, 
        topic: str
    ) -> str:
        """开始教学会话 - 备用版本"""
        
        context = TeachingContext(
            student_id=student_id,
            topic=topic,
            current_stage=LearningStage.WHAT,
            mastery_level=0.0,
            session_history=[],
            error_patterns=[],
            time_spent=timedelta(),
            last_interaction=datetime.now()
        )
        
        self.active_sessions[student_id] = context
        
        # 生成开场白
        welcome_message = f"""
🎓 欢迎来到EduSynapse智能教学系统！

我们将一起学习「{topic}」这个主题。
目前系统运行在备用模式，虽然没有高级AI功能，但仍能为你提供结构化的学习指导。

📚 学习框架：
• What - 了解{topic}的基本概念
• Why - 理解学习{topic}的重要性  
• How - 掌握{topic}的具体方法

让我们从「What - 是什么」阶段开始吧！
你对{topic}有什么初步的了解吗？

💡 提示：配置OpenAI API密钥可以启用完整的AI功能。
        """
        
        return welcome_message.strip()
    
    async def continue_teaching_session(
        self, 
        student_id: str, 
        user_input: str
    ) -> Tuple[str, Dict[str, Any]]:
        """继续教学会话 - 备用版本"""
        
        if student_id not in self.active_sessions:
            raise ValueError(f"No active session found for student: {student_id}")
        
        context = self.active_sessions[student_id]
        
        # 更新上下文
        context.last_interaction = datetime.now()
        context.session_history.append({
            "timestamp": datetime.now().isoformat(),
            "user_input": user_input,
            "stage": context.current_stage.value
        })
        
        # 进行教学对话
        response, teacher_type = await self.multi_agent_system.conduct_teaching_session(
            context, user_input
        )
        
        # 更新会话历史
        context.session_history.append({
            "timestamp": datetime.now().isoformat(),
            "ai_response": response,
            "teacher_type": teacher_type.value,
            "stage": context.current_stage.value,
            "fallback_mode": True
        })
        
        # 返回响应和元数据
        metadata = {
            "teacher_type": teacher_type.value,
            "current_stage": context.current_stage.value,
            "mastery_level": context.mastery_level,
            "session_length": len(context.session_history),
            "fallback_mode": True
        }
        
        return response, metadata
    
    def _generate_daily_plans(self, duration_days: int) -> List[Dict[str, Any]]:
        """生成每日学习计划 - 备用版本"""
        daily_plans = []
        
        # 简化版：平均分配WWH阶段
        what_days = max(1, duration_days // 3)
        why_days = max(1, duration_days // 3)  
        how_days = duration_days - what_days - why_days
        
        current_day = 1
        
        # What阶段
        for day in range(what_days):
            daily_plans.append({
                "day": current_day,
                "stage": "what",
                "focus": "概念理解",
                "description": "学习基本概念和定义",
                "estimated_hours": 2.0,
                "fallback_mode": True
            })
            current_day += 1
        
        # Why阶段
        for day in range(why_days):
            daily_plans.append({
                "day": current_day,
                "stage": "why", 
                "focus": "背景理解",
                "description": "理解重要性和应用价值",
                "estimated_hours": 2.0,
                "fallback_mode": True
            })
            current_day += 1
        
        # How阶段
        for day in range(how_days):
            daily_plans.append({
                "day": current_day,
                "stage": "how",
                "focus": "实践应用",
                "description": "学习具体方法和实践技能",
                "estimated_hours": 2.0,
                "fallback_mode": True
            })
            current_day += 1
        
        return daily_plans


# 全局服务实例
teaching_service = IntelligentTeachingService()
