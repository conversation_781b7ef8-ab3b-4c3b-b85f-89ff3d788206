"""
EduSynapse Database Initialization Script
数据库初始化脚本
"""

import os
import sys
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database.database import create_tables, drop_tables, reset_database, SessionLocal
from app.models.learning_plan import LearningPlan, LearningProgress


def create_sample_data():
    """创建示例数据"""
    print("📝 创建示例数据...")
    
    db = SessionLocal()
    try:
        # 示例WWH结构
        sample_wwh = {
            "what": {
                "core_concepts": ["Python基础语法", "数据类型", "控制结构", "函数定义"],
                "key_features": ["简洁易读", "跨平台", "丰富的库生态"],
                "definitions": {
                    "Python": "一种高级编程语言，以其简洁的语法和强大的功能而闻名",
                    "变量": "用于存储数据值的容器"
                },
                "prerequisites": ["计算机基础", "逻辑思维"]
            },
            "why": {
                "historical_context": "Python由Guido van <PERSON>在1989年创建，旨在提高代码可读性",
                "practical_importance": "Python在数据科学、Web开发、自动化等领域广泛应用",
                "learning_motivation": "掌握Python可以快速开发各种应用程序",
                "career_benefits": "Python开发者需求量大，薪资水平较高",
                "real_world_applications": ["数据分析", "机器学习", "Web开发", "自动化脚本"]
            },
            "how": {
                "practice_projects": [
                    {"title": "计算器程序", "description": "创建一个简单的命令行计算器", "difficulty": "easy"},
                    {"title": "待办事项管理", "description": "开发一个任务管理应用", "difficulty": "medium"}
                ],
                "code_examples": [
                    {"title": "Hello World", "language": "python", "description": "第一个Python程序"},
                    {"title": "列表操作", "language": "python", "description": "学习列表的基本操作"}
                ],
                "exercises": [
                    {"title": "变量练习", "type": "theory", "description": "理解不同数据类型"},
                    {"title": "循环练习", "type": "practical", "description": "编写for和while循环"}
                ],
                "learning_methods": ["理论学习", "代码实践", "项目驱动"],
                "recommended_resources": [
                    {"type": "book", "title": "Python编程：从入门到实践", "author": "Eric Matthes"},
                    {"type": "video", "title": "Python基础教程", "url": "https://example.com"}
                ]
            },
            "daily_breakdown": [
                {
                    "day": 1,
                    "focus": "what",
                    "topics": ["Python简介", "安装和环境配置"],
                    "tasks": [
                        {"task": "了解Python历史和特点", "type": "reading", "estimated_minutes": 60},
                        {"task": "安装Python环境", "type": "practice", "estimated_minutes": 90}
                    ],
                    "goals": ["理解Python的基本概念", "搭建开发环境"],
                    "assessment": "能够成功运行第一个Python程序"
                },
                {
                    "day": 2,
                    "focus": "what",
                    "topics": ["基础语法", "变量和数据类型"],
                    "tasks": [
                        {"task": "学习Python语法规则", "type": "reading", "estimated_minutes": 90},
                        {"task": "练习变量定义和使用", "type": "practice", "estimated_minutes": 60}
                    ],
                    "goals": ["掌握基础语法", "理解数据类型"],
                    "assessment": "能够正确定义和使用不同类型的变量"
                }
            ],
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "topic": "Python编程基础",
                "duration_days": 14,
                "daily_hours": 2.5,
                "total_estimated_hours": 35.0
            }
        }
        
        # 创建示例学习计划
        sample_plan = LearningPlan(
            topic="Python编程基础",
            description="适合初学者的Python编程入门课程，涵盖基础语法到实际项目开发",
            wwh_structure=json.dumps(sample_wwh, ensure_ascii=False),
            duration_days=14,
            difficulty_level="easy",
            target_hours_per_day=2.5,
            status="active",
            started_at=datetime.now()
        )
        
        db.add(sample_plan)
        db.commit()
        db.refresh(sample_plan)
        
        # 创建示例学习进度
        sample_progress = LearningProgress(
            plan_id=sample_plan.id,
            day_number=1,
            what_mastery=85.0,
            why_mastery=75.0,
            how_mastery=60.0,
            time_spent=150,  # 2.5小时
            focus_time=120,
            break_count=2,
            notes="今天学习了Python的基本概念，安装了开发环境。对Python的简洁语法印象深刻。",
            mood_score=4,
            difficulty_rating=2,
            completed_at=datetime.now()
        )
        
        db.add(sample_progress)
        db.commit()
        
        print(f"✅ 示例数据创建成功!")
        print(f"   - 学习计划ID: {sample_plan.id}")
        print(f"   - 主题: {sample_plan.topic}")
        print(f"   - 状态: {sample_plan.status}")
        
    except Exception as e:
        print(f"❌ 创建示例数据失败: {e}")
        db.rollback()
    finally:
        db.close()


def main():
    """主函数"""
    print("🚀 EduSynapse 数据库初始化")
    print("=" * 50)
    
    # 检查是否需要重置数据库
    reset = input("是否重置数据库? (y/N): ").lower().strip()
    
    if reset == 'y':
        print("⚠️ 重置数据库...")
        reset_database()
    else:
        print("📊 创建数据库表...")
        create_tables()
    
    # 询问是否创建示例数据
    create_sample = input("是否创建示例数据? (Y/n): ").lower().strip()
    
    if create_sample != 'n':
        create_sample_data()
    
    print("\n✅ 数据库初始化完成!")
    print("🚀 现在可以启动后端服务了:")
    print("   cd src/EduSynapse.Backend")
    print("   python main.py")


if __name__ == "__main__":
    main()
