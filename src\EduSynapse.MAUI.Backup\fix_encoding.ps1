# 修复文件编码和字符问题的脚本

Write-Host "🔧 修复文件编码和字符问题..." -ForegroundColor Cyan

$projectRoot = $PSScriptRoot

# 获取所有 .razor 和 .cs 文件
$files = Get-ChildItem -Path $projectRoot -Include "*.razor", "*.cs" -Recurse

$totalFiles = 0
$modifiedFiles = 0

# 字符替换映射
$replacements = @{
    "进行�?" = "进行中"
    "已完�?" = "已完成"
    "已暂�?" = "已暂停"
    "已取�?" = "已取消"
    "简�?" = "简单"
    "平均完成�?" = "平均完成率"
    "Color\.TextSecondary" = "Color.Secondary"
    "Icons\.Material\.Filled\.SchoolOutlined" = "Icons.Material.Filled.School"
    "Icons\.Material\.Filled\.Eco" = "Icons.Material.Filled.Nature"
}

foreach ($file in $files) {
    $totalFiles++
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    $originalContent = $content
    
    # 应用所有替换
    foreach ($pattern in $replacements.Keys) {
        $replacement = $replacements[$pattern]
        $content = $content -replace $pattern, $replacement
    }
    
    # 如果内容有变化，保存文件
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -NoNewline -Encoding UTF8
        Write-Host "✅ 修复: $($file.Name)" -ForegroundColor Green
        $modifiedFiles++
    }
}

Write-Host "`n📊 修复完成:" -ForegroundColor Cyan
Write-Host "  总文件数: $totalFiles" -ForegroundColor White
Write-Host "  修改文件数: $modifiedFiles" -ForegroundColor Green

if ($modifiedFiles -gt 0) {
    Write-Host "`n🎉 所有编码和字符问题已修复!" -ForegroundColor Green
} else {
    Write-Host "`n✅ 没有发现需要修复的文件" -ForegroundColor Green
}

# 现在尝试编译
Write-Host "`n🔨 尝试编译项目..." -ForegroundColor Yellow
$buildResult = dotnet build --verbosity quiet 2>&1

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ 项目编译成功!" -ForegroundColor Green
} else {
    Write-Host "❌ 项目编译失败，显示详细错误:" -ForegroundColor Red
    dotnet build --verbosity normal
}
