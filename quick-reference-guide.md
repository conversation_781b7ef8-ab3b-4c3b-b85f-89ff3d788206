# EduSynapse 技术栈快速参考卡片

## 🚀 WinForms → MAUI Blazor 快速对照

| WinForms | MAUI Blazor | 示例 |
|----------|-------------|------|
| `Form` | `@page "/path"` | `@page "/students"` |
| `Button.Click` | `@onclick="Method"` | `@onclick="SaveData"` |
| `TextBox.Text` | `@bind-Value="field"` | `@bind-Value="name"` |
| `DataGridView` | `<MudTable>` | `<MudTable Items="@students">` |
| `MessageBox.Show()` | `Snackbar.Add()` | `Snackbar.Add("成功！")` |

## 📦 常用 MudBlazor 组件速查

### 基础组件
```razor
<!-- 按钮 -->
<MudButton Color="Color.Primary" @onclick="Method">按钮</MudButton>

<!-- 文本输入 -->
<MudTextField @bind-Value="text" Label="标签" />

<!-- 数字输入 -->
<MudNumericField @bind-Value="number" Label="数字" />

<!-- 选择框 -->
<MudSelect @bind-Value="selected" Label="选择">
    <MudSelectItem Value="@("选项1")">选项1</MudSelectItem>
</MudSelect>

<!-- 复选框 -->
<MudCheckBox @bind-Checked="isChecked" Label="选项" />

<!-- 开关 -->
<MudSwitch @bind-Checked="isEnabled" Label="启用" />
```

### 布局组件
```razor
<!-- 容器 -->
<MudContainer MaxWidth="MaxWidth.Large">
    内容
</MudContainer>

<!-- 网格 -->
<MudGrid>
    <MudItem xs="12" md="6">左侧</MudItem>
    <MudItem xs="12" md="6">右侧</MudItem>
</MudGrid>

<!-- 卡片 -->
<MudCard>
    <MudCardHeader>
        <CardHeaderContent>
            <MudText Typo="Typo.h6">标题</MudText>
        </CardHeaderContent>
    </MudCardHeader>
    <MudCardContent>内容</MudCardContent>
    <MudCardActions>
        <MudButton>操作</MudButton>
    </MudCardActions>
</MudCard>
```

### 数据显示
```razor
<!-- 表格 -->
<MudTable Items="@items" Hover="true">
    <HeaderContent>
        <MudTh>列名</MudTh>
    </HeaderContent>
    <RowTemplate>
        <MudTd>@context.Property</MudTd>
    </RowTemplate>
</MudTable>

<!-- 列表 -->
<MudList>
    <MudListItem Text="项目1" Icon="@Icons.Material.Filled.Star" />
</MudList>
```

## 🔄 Blazor 核心语法

### 数据绑定
```razor
<!-- 单向绑定 -->
<p>值: @value</p>

<!-- 双向绑定 -->
<MudTextField @bind-Value="value" />

<!-- 事件绑定 -->
<MudButton @onclick="Method">点击</MudButton>
<MudButton @onclick="() => MethodWithParam(id)">参数</MudButton>
```

### 条件渲染
```razor
@if (condition)
{
    <p>条件为真时显示</p>
}
else
{
    <p>条件为假时显示</p>
}
```

### 循环渲染
```razor
@foreach (var item in items)
{
    <MudListItem Text="@item.Name" />
}
```

### 组件参数
```razor
@code {
    [Parameter] public string Title { get; set; } = "";
    [Parameter] public EventCallback<string> OnSave { get; set; }
}
```

## 🏗️ 项目结构模板

```
EduSynapse.MAUI/
├── Components/
│   ├── Layout/
│   │   ├── MainLayout.razor
│   │   └── NavMenu.razor
│   ├── Pages/
│   │   ├── Students/
│   │   └── Courses/
│   └── Shared/
├── Services/
│   ├── IStudentService.cs
│   └── StudentService.cs
├── Models/
│   ├── Student.cs
│   └── ApiResponse.cs
└── wwwroot/
    ├── css/
    └── images/
```

## 🔧 常用代码片段

### 服务注册 (MauiProgram.cs)
```csharp
builder.Services.AddMauiBlazorWebView();
builder.Services.AddMudServices();
builder.Services.AddScoped<IStudentService, StudentService>();
builder.Services.AddHttpClient();
```

### 页面组件模板
```razor
@page "/students"
@inject IStudentService StudentService
@inject ISnackbar Snackbar

<MudText Typo="Typo.h4">学生管理</MudText>

<MudTable Items="@students" Loading="@loading">
    <HeaderContent>
        <MudTh>姓名</MudTh>
        <MudTh>操作</MudTh>
    </HeaderContent>
    <RowTemplate>
        <MudTd>@context.Name</MudTd>
        <MudTd>
            <MudButton Size="Size.Small" @onclick="() => Edit(context)">编辑</MudButton>
        </MudTd>
    </RowTemplate>
</MudTable>

@code {
    private List<Student> students = new();
    private bool loading = true;
    
    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }
    
    private async Task LoadData()
    {
        loading = true;
        try
        {
            students = await StudentService.GetAllAsync();
        }
        catch (Exception ex)
        {
            Snackbar.Add("加载失败", Severity.Error);
        }
        finally
        {
            loading = false;
        }
    }
    
    private void Edit(Student student)
    {
        // 编辑逻辑
    }
}
```

### 表单组件模板
```razor
<EditForm Model="@model" OnValidSubmit="OnSave">
    <DataAnnotationsValidator />
    <MudGrid>
        <MudItem xs="12">
            <MudTextField @bind-Value="model.Name" 
                          Label="姓名" 
                          Required="true"
                          For="@(() => model.Name)" />
        </MudItem>
        <MudItem xs="12">
            <MudButton ButtonType="ButtonType.Submit" 
                       Color="Color.Primary">保存</MudButton>
        </MudItem>
    </MudGrid>
</EditForm>

@code {
    [Parameter] public Student model { get; set; } = new();
    [Parameter] public EventCallback<Student> OnSaved { get; set; }
    
    private async Task OnSave()
    {
        await OnSaved.InvokeAsync(model);
    }
}
```

### 服务类模板
```csharp
public interface IStudentService
{
    Task<List<Student>> GetAllAsync();
    Task<Student?> GetByIdAsync(int id);
    Task<bool> SaveAsync(Student student);
    Task<bool> DeleteAsync(int id);
}

public class StudentService : IStudentService
{
    private readonly HttpClient _httpClient;
    
    public StudentService(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }
    
    public async Task<List<Student>> GetAllAsync()
    {
        try
        {
            var response = await _httpClient.GetFromJsonAsync<List<Student>>("api/students");
            return response ?? new List<Student>();
        }
        catch
        {
            return new List<Student>();
        }
    }
    
    // 其他方法实现...
}
```

## 🐛 常见问题快速解决

| 问题 | 解决方案 |
|------|----------|
| UI 不更新 | 调用 `StateHasChanged()` |
| 服务未找到 | 检查 `MauiProgram.cs` 中的服务注册 |
| 路由不工作 | 确保有 `@page` 指令 |
| 验证不工作 | 添加 `<DataAnnotationsValidator />` |
| 样式不生效 | 检查 CSS 类名和 MudBlazor 主题 |

## 📱 调试技巧

1. **启用开发者工具**
```csharp
#if DEBUG
builder.Services.AddBlazorWebViewDeveloperTools();
#endif
```

2. **添加日志**
```csharp
@inject ILogger<ComponentName> Logger

Logger.LogInformation("操作完成");
Logger.LogError(ex, "操作失败");
```

3. **异常边界**
```razor
<ErrorBoundary>
    <ChildContent>
        <YourComponent />
    </ChildContent>
    <ErrorContent>
        <MudAlert Severity="Severity.Error">发生错误</MudAlert>
    </ErrorContent>
</ErrorBoundary>
```

## 🎯 性能优化提示

- 使用 `@key` 优化列表渲染
- 避免在渲染中进行复杂计算
- 合理使用 `StateHasChanged()`
- 实现 `IDisposable` 清理资源
- 使用 `OnInitializedAsync` 进行异步初始化

---

**💡 提示：** 将此卡片打印或保存为书签，开发时随时查阅！
