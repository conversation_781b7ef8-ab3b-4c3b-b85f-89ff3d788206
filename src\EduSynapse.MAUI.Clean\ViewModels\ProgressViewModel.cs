using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using EduSynapse.MAUI.Services;
using EduSynapse.MAUI.Models;

namespace EduSynapse.MAUI.ViewModels;

/// <summary>
/// 学习进度ViewModel
/// </summary>
public partial class ProgressViewModel : BaseViewModel
{
    [ObservableProperty]
    private LearningPlan? _currentPlan;

    [ObservableProperty]
    private List<LearningProgress> _progressRecords = new();

    [ObservableProperty]
    private ProgressStatsResponse? _progressStats;

    [ObservableProperty]
    private int _selectedDayNumber = 1;

    [ObservableProperty]
    private double _whatMastery = 0.0;

    [ObservableProperty]
    private double _whyMastery = 0.0;

    [ObservableProperty]
    private double _howMastery = 0.0;

    [ObservableProperty]
    private int _timeSpent = 0;

    [ObservableProperty]
    private int _focusTime = 0;

    [ObservableProperty]
    private int _breakCount = 0;

    [ObservableProperty]
    private string _notes = string.Empty;

    [ObservableProperty]
    private int _moodScore = 3;

    [ObservableProperty]
    private int _difficultyRating = 3;

    [ObservableProperty]
    private bool _isRecording;

    [ObservableProperty]
    private DateTime _selectedDate = DateTime.Today;

    [ObservableProperty]
    private List<CalendarDay> _calendarData = new();

    [ObservableProperty]
    private ProgressAnalysis? _analysisData;

    public ProgressViewModel(
        IApiService apiService,
        IStateService stateService,
        IStorageService storageService)
        : base(apiService, stateService, storageService)
    {
        Title = "学习进度跟踪";
    }

    public override async Task InitializeAsync()
    {
        CurrentPlan = _stateService.CurrentPlan;
        if (CurrentPlan != null)
        {
            await LoadProgressDataAsync();
        }
    }

    /// <summary>
    /// 加载进度数据
    /// </summary>
    [RelayCommand]
    private async Task LoadProgressDataAsync()
    {
        if (CurrentPlan == null) return;

        await ExecuteAsync(async () =>
        {
            // 加载进度统计
            await LoadProgressStatsAsync();
            
            // 加载进度记录列表
            await LoadProgressRecordsAsync();
            
            // 加载日历数据
            await LoadCalendarDataAsync();
        }, "加载进度数据失败");
    }

    /// <summary>
    /// 加载进度统计
    /// </summary>
    private async Task LoadProgressStatsAsync()
    {
        if (CurrentPlan == null) return;

        try
        {
            var response = await _apiService.GetProgressStatsAsync(CurrentPlan.Id);
            ProgressStats = response;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"加载进度统计失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 加载进度记录列表
    /// </summary>
    private async Task LoadProgressRecordsAsync()
    {
        if (CurrentPlan == null) return;

        try
        {
            var records = await _apiService.GetProgressListAsync(CurrentPlan.Id);
            if (records != null)
            {
                ProgressRecords = records;
                _stateService.CurrentProgress = records;
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"加载进度记录失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 加载日历数据
    /// </summary>
    private async Task LoadCalendarDataAsync()
    {
        if (CurrentPlan == null) return;

        try
        {
            var calendarResponse = await _apiService.GetProgressCalendarAsync(
                CurrentPlan.Id, 
                SelectedDate.Year, 
                SelectedDate.Month
            );
            
            if (calendarResponse != null)
            {
                CalendarData = calendarResponse.CalendarData;
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"加载日历数据失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 记录学习进度
    /// </summary>
    [RelayCommand]
    private async Task RecordProgressAsync()
    {
        if (CurrentPlan == null)
        {
            SetError("请先选择学习计划");
            return;
        }

        if (WhatMastery == 0 && WhyMastery == 0 && HowMastery == 0)
        {
            SetError("请至少填写一项掌握度");
            return;
        }

        IsRecording = true;

        await ExecuteAsync(async () =>
        {
            var request = new ProgressRecordRequest
            {
                PlanId = CurrentPlan.Id,
                DayNumber = SelectedDayNumber,
                WhatMastery = WhatMastery,
                WhyMastery = WhyMastery,
                HowMastery = HowMastery,
                TimeSpent = TimeSpent,
                FocusTime = FocusTime,
                BreakCount = BreakCount,
                Notes = Notes,
                MoodScore = MoodScore,
                DifficultyRating = DifficultyRating
            };

            var progress = await _apiService.RecordProgressAsync(CurrentPlan.Id, request);
            if (progress != null)
            {
                // 重新加载数据
                await LoadProgressDataAsync();
                
                // 重置表单
                ResetProgressForm();
                
                // 自动增加天数
                if (SelectedDayNumber < CurrentPlan.DurationDays)
                {
                    SelectedDayNumber++;
                }
            }
        }, "记录学习进度失败");

        IsRecording = false;
    }

    /// <summary>
    /// 选择特定天数
    /// </summary>
    [RelayCommand]
    private async Task SelectDayAsync(int dayNumber)
    {
        SelectedDayNumber = dayNumber;
        
        // 加载该天的现有记录
        await LoadDayProgressAsync(dayNumber);
    }

    /// <summary>
    /// 加载特定天数的进度
    /// </summary>
    private async Task LoadDayProgressAsync(int dayNumber)
    {
        if (CurrentPlan == null) return;

        try
        {
            var existingProgress = ProgressRecords.FirstOrDefault(p => p.DayNumber == dayNumber);
            if (existingProgress != null)
            {
                // 填充表单
                WhatMastery = existingProgress.WhatMastery;
                WhyMastery = existingProgress.WhyMastery;
                HowMastery = existingProgress.HowMastery;
                TimeSpent = existingProgress.TimeSpent;
                FocusTime = existingProgress.FocusTime;
                BreakCount = existingProgress.BreakCount;
                Notes = existingProgress.Notes ?? string.Empty;
                MoodScore = existingProgress.MoodScore ?? 3;
                DifficultyRating = existingProgress.DifficultyRating ?? 3;
            }
            else
            {
                ResetProgressForm();
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"加载天数进度失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 加载学习分析
    /// </summary>
    [RelayCommand]
    private async Task LoadAnalysisAsync()
    {
        if (CurrentPlan == null) return;

        await ExecuteAsync(async () =>
        {
            var analysis = await _apiService.GetProgressAnalysisAsync(CurrentPlan.Id);
            AnalysisData = analysis;
        }, "加载学习分析失败");
    }

    /// <summary>
    /// 切换月份
    /// </summary>
    [RelayCommand]
    private async Task ChangeMonthAsync(int monthOffset)
    {
        SelectedDate = SelectedDate.AddMonths(monthOffset);
        await LoadCalendarDataAsync();
    }

    /// <summary>
    /// 重置进度表单
    /// </summary>
    [RelayCommand]
    private void ResetProgressForm()
    {
        WhatMastery = 0.0;
        WhyMastery = 0.0;
        HowMastery = 0.0;
        TimeSpent = 0;
        FocusTime = 0;
        BreakCount = 0;
        Notes = string.Empty;
        MoodScore = 3;
        DifficultyRating = 3;
    }

    /// <summary>
    /// 快速设置学习时间
    /// </summary>
    [RelayCommand]
    private void SetQuickTime(int minutes)
    {
        TimeSpent = minutes;
        FocusTime = (int)(minutes * 0.8); // 假设80%为专注时间
        BreakCount = minutes / 60; // 每小时一次休息
    }

    /// <summary>
    /// 计算总体掌握度
    /// </summary>
    public double OverallMastery => (WhatMastery + WhyMastery + HowMastery) / 3;

    /// <summary>
    /// 获取心情显示文本
    /// </summary>
    public string GetMoodDisplayText(int score) => score switch
    {
        1 => "😞 很差",
        2 => "😕 较差", 
        3 => "😐 一般",
        4 => "😊 良好",
        5 => "😄 很好",
        _ => "未评分"
    };

    /// <summary>
    /// 获取难度显示文本
    /// </summary>
    public string GetDifficultyDisplayText(int rating) => rating switch
    {
        1 => "⭐ 很简单",
        2 => "⭐⭐ 简单",
        3 => "⭐⭐⭐ 适中",
        4 => "⭐⭐⭐⭐ 困难",
        5 => "⭐⭐⭐⭐⭐ 很困难",
        _ => "未评分"
    };

    protected override void OnStateChanged(object? sender, EventArgs e)
    {
        base.OnStateChanged(sender, e);
        
        // 同步当前计划
        if (CurrentPlan != _stateService.CurrentPlan)
        {
            CurrentPlan = _stateService.CurrentPlan;
            if (CurrentPlan != null)
            {
                _ = LoadProgressDataAsync();
            }
        }
    }
}
