#!/usr/bin/env python3
"""
EduSynapse AI Features Test Script
测试AI智能教学功能的脚本
"""

import asyncio
import json
import time
from typing import Dict, Any
import httpx
from datetime import datetime


class EduSynapseAITester:
    """EduSynapse AI功能测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=30.0)
        self.test_results = []
    
    async def test_ai_service_status(self) -> bool:
        """测试AI服务状态"""
        print("🔍 测试AI服务状态...")
        try:
            response = await self.client.get(f"{self.base_url}/api/ai/status")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ AI服务状态: {data.get('ai_service_status')}")
                print(f"   WWH框架引擎: {data.get('wwh_framework_engine', {}).get('status')}")
                print(f"   多代理系统: {data.get('multi_agent_system', {}).get('status')}")
                print(f"   活跃会话: {data.get('active_sessions', 0)}")
                return True
            else:
                print(f"❌ AI服务状态检查失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ AI服务状态检查异常: {e}")
            return False
    
    async def test_ai_generate_plan(self) -> Dict[str, Any]:
        """测试AI学习计划生成"""
        print("\n🚀 测试AI学习计划生成...")
        
        request_data = {
            "topic": "Python编程基础",
            "difficulty_level": "beginner",
            "duration_days": 7
        }
        
        try:
            start_time = time.time()
            response = await self.client.post(
                f"{self.base_url}/api/ai/generate-plan",
                json=request_data
            )
            processing_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ AI计划生成成功 (耗时: {processing_time:.2f}s)")
                print(f"   计划ID: {data.get('plan_id')}")
                print(f"   主题: {data.get('plan', {}).get('topic')}")
                print(f"   天数: {data.get('plan', {}).get('duration_days')}")
                
                # 检查WWH框架内容
                wwh = data.get('plan', {}).get('wwh_framework', {})
                if wwh:
                    print(f"   WWH框架: What({len(str(wwh.get('what', {})))}字符), Why({len(str(wwh.get('why', {})))}字符), How({len(str(wwh.get('how', {})))}字符)")
                
                return data
            else:
                print(f"❌ AI计划生成失败: {response.status_code}")
                print(f"   错误: {response.text}")
                return {}
                
        except Exception as e:
            print(f"❌ AI计划生成异常: {e}")
            return {}
    
    async def test_ai_start_session(self) -> Dict[str, Any]:
        """测试AI教学会话启动"""
        print("\n🎭 测试AI教学会话启动...")
        
        request_data = {
            "student_id": "test_student_ai_001",
            "topic": "Python变量和数据类型"
        }
        
        try:
            response = await self.client.post(
                f"{self.base_url}/api/ai/start-session",
                json=request_data
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ AI会话启动成功")
                print(f"   会话ID: {data.get('session_id')}")
                print(f"   当前教师: {data.get('current_teacher')}")
                print(f"   当前阶段: {data.get('current_stage')}")
                print(f"   欢迎消息: {data.get('welcome_message', '')[:100]}...")
                return data
            else:
                print(f"❌ AI会话启动失败: {response.status_code}")
                print(f"   错误: {response.text}")
                return {}
                
        except Exception as e:
            print(f"❌ AI会话启动异常: {e}")
            return {}
    
    async def test_ai_continue_session(self, student_id: str = "test_student_ai_001") -> Dict[str, Any]:
        """测试AI教学会话继续"""
        print("\n💬 测试AI教学会话继续...")
        
        test_questions = [
            "什么是变量？",
            "Python有哪些基本数据类型？",
            "如何定义一个字符串变量？",
            "整数和浮点数有什么区别？"
        ]
        
        results = []
        for question in test_questions:
            request_data = {
                "student_id": student_id,
                "user_input": question
            }
            
            try:
                start_time = time.time()
                response = await self.client.post(
                    f"{self.base_url}/api/ai/continue-session",
                    json=request_data
                )
                processing_time = time.time() - start_time
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ 问题: {question}")
                    print(f"   AI回复: {data.get('ai_response', '')[:150]}...")
                    print(f"   教师类型: {data.get('current_teacher')}")
                    print(f"   处理时间: {processing_time:.2f}s")
                    results.append(data)
                else:
                    print(f"❌ AI对话失败: {response.status_code}")
                    print(f"   问题: {question}")
                    print(f"   错误: {response.text}")
                
                # 避免请求过快
                await asyncio.sleep(1)
                
            except Exception as e:
                print(f"❌ AI对话异常: {e}")
                print(f"   问题: {question}")
        
        return results
    
    async def test_ai_teachers(self) -> bool:
        """测试AI教师列表"""
        print("\n👥 测试AI教师列表...")
        
        try:
            response = await self.client.get(f"{self.base_url}/api/ai/teachers")
            
            if response.status_code == 200:
                data = response.json()
                teachers = data.get('teachers', [])
                print(f"✅ 获取AI教师列表成功，共 {len(teachers)} 个教师:")
                for teacher in teachers:
                    ai_status = "🤖 AI启用" if teacher.get('ai_enabled') else "📝 备用模式"
                    print(f"   - {teacher.get('name')}: {teacher.get('description')} ({ai_status})")
                return True
            else:
                print(f"❌ 获取AI教师列表失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 获取AI教师列表异常: {e}")
            return False
    
    async def test_wwh_framework(self) -> bool:
        """测试WWH框架信息"""
        print("\n📚 测试WWH框架信息...")
        
        try:
            response = await self.client.get(f"{self.base_url}/api/ai/wwh-framework")
            
            if response.status_code == 200:
                data = response.json()
                framework = data.get('framework')
                stages = data.get('stages', [])
                ai_powered = data.get('ai_powered', False)
                
                print(f"✅ WWH框架信息获取成功:")
                print(f"   框架: {framework}")
                print(f"   AI驱动: {'🤖 是' if ai_powered else '📝 否'}")
                print(f"   阶段数: {len(stages)}")
                
                for stage in stages:
                    print(f"   - {stage.get('name')} ({stage.get('stage')}): {stage.get('description')} (权重: {stage.get('weight')})")
                
                return True
            else:
                print(f"❌ 获取WWH框架信息失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 获取WWH框架信息异常: {e}")
            return False
    
    async def test_ai_health(self) -> bool:
        """测试AI服务健康检查"""
        print("\n🏥 测试AI服务健康检查...")
        
        try:
            response = await self.client.get(f"{self.base_url}/api/ai/health")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ AI服务健康检查通过:")
                print(f"   状态: {data.get('status')}")
                print(f"   服务: {data.get('service')}")
                print(f"   版本: {data.get('version')}")
                
                capabilities = data.get('ai_capabilities', {})
                print(f"   AI能力:")
                print(f"     - WWH框架: {'✅' if capabilities.get('wwh_framework') else '❌'}")
                print(f"     - 多代理教学: {'✅' if capabilities.get('multi_agent_teaching') else '❌'}")
                
                return True
            else:
                print(f"❌ AI服务健康检查失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ AI服务健康检查异常: {e}")
            return False
    
    async def run_all_ai_tests(self) -> Dict[str, bool]:
        """运行所有AI功能测试"""
        print("🧪 开始EduSynapse AI功能测试")
        print("=" * 60)
        
        results = {}
        
        # 1. AI服务状态检查
        results['ai_service_status'] = await self.test_ai_service_status()
        
        # 2. AI健康检查
        results['ai_health_check'] = await self.test_ai_health()
        
        # 3. 获取AI基础信息
        results['ai_teachers'] = await self.test_ai_teachers()
        results['wwh_framework'] = await self.test_wwh_framework()
        
        # 4. AI核心功能测试
        plan_result = await self.test_ai_generate_plan()
        results['ai_generate_plan'] = bool(plan_result)
        
        session_result = await self.test_ai_start_session()
        results['ai_start_session'] = bool(session_result)
        
        if session_result:
            chat_results = await self.test_ai_continue_session()
            results['ai_continue_session'] = bool(chat_results)
        else:
            results['ai_continue_session'] = False
        
        # 输出测试总结
        print("\n" + "=" * 60)
        print("🎯 AI功能测试结果总结:")
        print("=" * 60)
        
        passed = sum(results.values())
        total = len(results)
        
        for test_name, passed_test in results.items():
            status = "✅ 通过" if passed_test else "❌ 失败"
            print(f"{test_name:25} : {status}")
        
        print("-" * 60)
        print(f"总计: {passed}/{total} 个AI测试通过 ({passed/total*100:.1f}%)")
        
        if passed == total:
            print("🎉 所有AI功能测试通过！EduSynapse AI智能教学系统运行正常！")
        elif passed > total * 0.5:
            print("⚠️  部分AI功能可用，系统运行在混合模式（AI + 备用）")
        else:
            print("⚠️  AI功能大部分不可用，系统运行在备用模式")
        
        print("\n💡 提示:")
        print("   - 如需完整AI功能，请确保设置了有效的OPENAI_API_KEY")
        print("   - 系统在AI不可用时会自动切换到备用模式")
        print("   - 查看 /api/ai/status 了解详细的AI服务状态")
        
        await self.client.aclose()
        return results


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="EduSynapse AI功能测试工具")
    parser.add_argument("--url", default="http://localhost:8000", help="API服务器地址")
    parser.add_argument("--test", choices=["all", "status", "plan", "session", "info"], 
                       default="all", help="要运行的测试类型")
    
    args = parser.parse_args()
    
    tester = EduSynapseAITester(args.url)
    
    if args.test == "all":
        await tester.run_all_ai_tests()
    elif args.test == "status":
        await tester.test_ai_service_status()
        await tester.test_ai_health()
    elif args.test == "plan":
        await tester.test_ai_generate_plan()
    elif args.test == "session":
        await tester.test_ai_start_session()
        await tester.test_ai_continue_session()
    elif args.test == "info":
        await tester.test_ai_teachers()
        await tester.test_wwh_framework()


if __name__ == "__main__":
    asyncio.run(main())
