"""
EduSynapse Learning Plan Schemas
学习计划相关的数据传输对象(DTO)
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator


class PlanGenerationRequest(BaseModel):
    """学习计划生成请求"""
    topic: str = Field(..., min_length=1, max_length=200, description="学习主题")
    duration_days: int = Field(14, ge=1, le=365, description="学习天数")
    difficulty_level: str = Field("medium", description="难度级别")
    daily_hours: float = Field(2.0, ge=0.5, le=12.0, description="每日学习时长")
    learning_style: str = Field("balanced", description="学习风格偏好")
    preferences: Optional[Dict[str, Any]] = Field(None, description="额外偏好设置")
    
    @validator("difficulty_level")
    def validate_difficulty(cls, v):
        allowed = ["easy", "medium", "hard"]
        if v not in allowed:
            raise ValueError(f"难度级别必须是: {', '.join(allowed)}")
        return v
    
    @validator("learning_style")
    def validate_learning_style(cls, v):
        allowed = ["theoretical", "practical", "balanced", "project-based"]
        if v not in allowed:
            raise ValueError(f"学习风格必须是: {', '.join(allowed)}")
        return v


class WWHStructure(BaseModel):
    """WWH框架结构"""
    what: Dict[str, Any] = Field(..., description="What阶段内容")
    why: Dict[str, Any] = Field(..., description="Why阶段内容")
    how: Dict[str, Any] = Field(..., description="How阶段内容")
    daily_breakdown: List[Dict[str, Any]] = Field(..., description="每日学习计划")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")


class LearningPlanResponse(BaseModel):
    """学习计划响应"""
    id: int = Field(..., description="计划ID")
    topic: str = Field(..., description="学习主题")
    description: Optional[str] = Field(None, description="计划描述")
    wwh_structure: WWHStructure = Field(..., description="WWH框架结构")
    duration_days: int = Field(..., description="计划天数")
    difficulty_level: str = Field(..., description="难度级别")
    target_hours_per_day: float = Field(..., description="每日目标时长")
    status: str = Field(..., description="计划状态")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")


class LearningPlanSummary(BaseModel):
    """学习计划摘要"""
    id: int
    topic: str
    duration_days: int
    difficulty_level: str
    status: str
    progress_percentage: Optional[float] = 0.0
    created_at: datetime


class LearningPlanUpdate(BaseModel):
    """学习计划更新请求"""
    description: Optional[str] = None
    duration_days: Optional[int] = Field(None, ge=1, le=365)
    difficulty_level: Optional[str] = None
    target_hours_per_day: Optional[float] = Field(None, ge=0.5, le=12.0)
    status: Optional[str] = None
    
    @validator("difficulty_level")
    def validate_difficulty(cls, v):
        if v is not None:
            allowed = ["easy", "medium", "hard"]
            if v not in allowed:
                raise ValueError(f"难度级别必须是: {', '.join(allowed)}")
        return v
    
    @validator("status")
    def validate_status(cls, v):
        if v is not None:
            allowed = ["active", "completed", "paused", "cancelled"]
            if v not in allowed:
                raise ValueError(f"状态必须是: {', '.join(allowed)}")
        return v


class ProgressRecordRequest(BaseModel):
    """学习进度记录请求"""
    plan_id: int = Field(..., description="学习计划ID")
    day_number: int = Field(..., ge=1, description="学习天数")
    what_mastery: float = Field(0.0, ge=0.0, le=100.0, description="What掌握度")
    why_mastery: float = Field(0.0, ge=0.0, le=100.0, description="Why掌握度")
    how_mastery: float = Field(0.0, ge=0.0, le=100.0, description="How掌握度")
    time_spent: int = Field(0, ge=0, description="学习时长(分钟)")
    focus_time: int = Field(0, ge=0, description="专注时长(分钟)")
    break_count: int = Field(0, ge=0, description="休息次数")
    notes: Optional[str] = Field(None, max_length=2000, description="学习笔记")
    mood_score: Optional[int] = Field(None, ge=1, le=5, description="心情评分")
    difficulty_rating: Optional[int] = Field(None, ge=1, le=5, description="难度评价")


class ProgressResponse(BaseModel):
    """学习进度响应"""
    id: int
    plan_id: int
    day_number: int
    what_mastery: float
    why_mastery: float
    how_mastery: float
    overall_mastery: float
    time_spent: int
    focus_time: int
    break_count: int
    notes: Optional[str]
    mood_score: Optional[int]
    difficulty_rating: Optional[int]
    completed_at: Optional[datetime]
    created_at: datetime


class ProgressStatsResponse(BaseModel):
    """学习进度统计响应"""
    plan_id: int
    total_days: int
    completed_days: int
    completion_percentage: float
    average_mastery: Dict[str, float]
    total_time_spent: int
    average_daily_time: float
    mood_trend: List[Optional[int]]
    difficulty_trend: List[Optional[int]]
    streak_days: int
    last_study_date: Optional[datetime]


class ApiResponse(BaseModel):
    """统一API响应格式"""
    success: bool = Field(..., description="操作是否成功")
    data: Optional[Any] = Field(None, description="响应数据")
    message: str = Field("", description="响应消息")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")
    request_id: Optional[str] = Field(None, description="请求ID")


class ErrorResponse(BaseModel):
    """错误响应格式"""
    success: bool = False
    error: Dict[str, Any] = Field(..., description="错误信息")
    timestamp: datetime = Field(default_factory=datetime.now, description="错误时间")
    request_id: Optional[str] = Field(None, description="请求ID")
