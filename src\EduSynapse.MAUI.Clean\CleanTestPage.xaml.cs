using Microsoft.AspNetCore.Components.WebView.Maui;
using Microsoft.AspNetCore.Components.WebView;
using System.Diagnostics;

namespace EduSynapse.MAUI;

public partial class CleanTestPage : ContentPage
{
    public CleanTestPage()
    {
        InitializeComponent();
        Debug.WriteLine("🎯 CleanTestPage 构造函数完成");

        // 添加 Blazor WebView 事件监听
        blazorWebView.BlazorWebViewInitialized += OnBlazorWebViewInitialized;
        blazorWebView.UrlLoading += OnUrlLoading;
    }

    private void OnBlazorWebViewInitialized(object? sender, BlazorWebViewInitializedEventArgs e)
    {
        Debug.WriteLine("🚀 CleanTestPage Blazor WebView 初始化完成");

#if DEBUG && WINDOWS
        // 启用开发者工具
        e.WebView.CoreWebView2.Settings.AreDevToolsEnabled = true;
        e.WebView.CoreWebView2.Settings.AreHostObjectsAllowed = true;
        Debug.WriteLine("✅ 开发者工具已启用");
#endif
    }

    private void OnUrlLoading(object? sender, UrlLoadingEventArgs e)
    {
        Debug.WriteLine($"🌐 URL 加载: {e.Url}");
    }

    protected override void OnAppearing()
    {
        base.OnAppearing();
        Debug.WriteLine("👁️ CleanTestPage 页面显示");
    }

    protected override void OnDisappearing()
    {
        base.OnDisappearing();
        Debug.WriteLine("👋 CleanTestPage 页面隐藏");
    }
}
