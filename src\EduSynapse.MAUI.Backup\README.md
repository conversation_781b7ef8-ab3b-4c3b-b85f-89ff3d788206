# EduSynapse MAUI Frontend

EduSynapse 智能学习系统的跨平台前端应用，基于 .NET MAUI 和 Blazor Hybrid 构建。

## 🚀 快速开始

### 环境要求

- Visual Studio 2022 (17.8 或更高版本)
- .NET 8.0 SDK
- MAUI 工作负载
- Windows 10 版本 1809 或更高版本

### 安装和运行

1. **安装 MAUI 工作负载**
   ```bash
   dotnet workload install maui
   ```

2. **还原 NuGet 包**
   ```bash
   dotnet restore
   ```

3. **运行应用**
   ```bash
   # 使用 Visual Studio
   # 1. 打开 EduSynapse.MAUI.csproj
   # 2. 选择 Windows Machine 作为启动目标
   # 3. 按 F5 启动调试

   # 或使用命令行
   dotnet build
   dotnet run --framework net8.0-windows10.0.19041.0
   ```

## 🏗️ 项目结构

```
EduSynapse.MAUI/
├── Components/
│   ├── Layout/              # 布局组件
│   │   └── MainLayout.razor
│   ├── Pages/               # 页面组件
│   │   └── Home.razor
│   └── App.razor           # 根组件
├── Models/                  # 数据模型
│   ├── LearningPlan.cs
│   └── ApiModels.cs
├── Services/                # 业务服务
│   ├── IApiService.cs
│   ├── ApiService.cs
│   ├── IStateService.cs
│   ├── StateService.cs
│   ├── IStorageService.cs
│   └── StorageService.cs
├── ViewModels/              # 视图模型
│   ├── BaseViewModel.cs
│   ├── MainViewModel.cs
│   └── LearningPlanViewModel.cs
├── Resources/               # 资源文件
│   ├── Styles/
│   ├── Images/
│   └── Raw/
├── wwwroot/                 # Web 资源
│   ├── css/
│   └── index.html
├── Platforms/               # 平台特定代码
└── MauiProgram.cs          # 应用配置
```

## 🎨 技术栈

### 核心框架
- **.NET MAUI**: 跨平台应用框架
- **Blazor Hybrid**: Web UI 在原生容器中运行
- **MudBlazor**: Material Design 组件库

### 状态管理
- **CommunityToolkit.Mvvm**: MVVM 模式支持
- **依赖注入**: 服务管理和生命周期

### 数据和网络
- **HttpClient**: HTTP 通信
- **System.Text.Json**: JSON 序列化
- **本地存储**: 文件系统存储

## 📡 API 集成

### 配置 API 端点

在 `MauiProgram.cs` 中配置 API 基础地址：

```csharp
builder.Services.AddHttpClient("EduSynapseAPI", client =>
{
    client.BaseAddress = new Uri("http://localhost:8000/");
    client.DefaultRequestHeaders.Add("Accept", "application/json");
});
```

### API 服务使用

```csharp
// 注入 API 服务
@inject IApiService ApiService

// 调用 API
var plans = await ApiService.GetLearningPlansAsync();
```

## 🎯 核心功能

### 1. 学习计划管理
- 创建 AI 生成的学习计划
- 查看和编辑计划详情
- 计划状态管理

### 2. 学习进度跟踪
- 记录每日学习进度
- WWH 掌握度评估
- 学习时间统计

### 3. 智能仪表板
- 学习统计概览
- 最近计划展示
- 快速操作入口

### 4. 响应式设计
- 适配不同屏幕尺寸
- Material Design 风格
- 流畅的动画效果

## 🔧 开发指南

### 添加新页面

1. 在 `Components/Pages/` 创建 Razor 组件
2. 添加路由属性 `@page "/your-route"`
3. 在导航菜单中添加链接

### 创建新服务

1. 定义接口 `IYourService.cs`
2. 实现服务 `YourService.cs`
3. 在 `MauiProgram.cs` 中注册服务

### 添加新 ViewModel

1. 继承 `BaseViewModel`
2. 使用 `[ObservableProperty]` 标记属性
3. 使用 `[RelayCommand]` 标记命令方法

## 🎨 UI 组件

### MudBlazor 组件示例

```razor
<!-- 卡片 -->
<MudCard Elevation="2">
    <MudCardContent>
        <MudText Typo="Typo.h6">标题</MudText>
        <MudText Typo="Typo.body2">内容</MudText>
    </MudCardContent>
</MudCard>

<!-- 按钮 -->
<MudButton Variant="Variant.Filled" 
           Color="Color.Primary"
           StartIcon="@Icons.Material.Filled.Add"
           OnClick="@HandleClick">
    创建
</MudButton>

<!-- 表单 -->
<MudTextField @bind-Value="@model.Name" 
              Label="名称" 
              Required="true" />
```

### 自定义样式

在 `wwwroot/css/app.css` 中添加自定义样式：

```css
.custom-card {
    transition: transform 0.2s ease-in-out;
}

.custom-card:hover {
    transform: translateY(-2px);
}
```

## 🔍 调试和测试

### 调试技巧

1. **Blazor 开发者工具**: 在浏览器中按 F12
2. **Visual Studio 调试**: 设置断点并按 F5
3. **日志输出**: 使用 `System.Diagnostics.Debug.WriteLine()`

### 常见问题

1. **API 连接失败**
   - 检查后端服务是否运行
   - 验证 API 基础地址配置

2. **MAUI 工作负载问题**
   ```bash
   dotnet workload repair
   dotnet workload install maui
   ```

3. **包还原失败**
   ```bash
   dotnet nuget locals all --clear
   dotnet restore
   ```

## 📱 平台支持

### 当前支持
- ✅ Windows 10/11

### 未来计划
- 🔄 macOS (Mac Catalyst)
- 🔄 Android
- 🔄 iOS

## 🚀 部署

### Windows 部署

1. **发布应用**
   ```bash
   dotnet publish -c Release -f net8.0-windows10.0.19041.0
   ```

2. **创建安装包**
   - 使用 Visual Studio 发布向导
   - 或使用 MSIX 打包工具

### 配置要求

- Windows 10 版本 1809 或更高
- .NET 8.0 Runtime
- WebView2 Runtime (通常已预装)

## 📄 许可证

本项目为个人学习项目，仅供学习和研究使用。
