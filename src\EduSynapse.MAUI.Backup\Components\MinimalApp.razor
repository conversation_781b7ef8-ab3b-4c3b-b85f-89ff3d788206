@using Microsoft.AspNetCore.Components.Web

<div style="padding: 40px; font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto;">
    <h1 style="color: #2196F3; text-align: center; margin-bottom: 30px;">
        🎓 EduSynapse - 最小化测试版本
    </h1>
    
    <div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h2 style="color: #333; margin-top: 0;">✅ 应用状态</h2>
        <p><strong>启动时间:</strong> @startTime</p>
        <p><strong>当前时间:</strong> @currentTime</p>
        <p><strong>运行状态:</strong> <span style="color: green;">正常运行</span></p>
        <p><strong>框架版本:</strong> .NET MAUI + Blazor</p>
    </div>
    
    <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h2 style="color: #1976d2; margin-top: 0;">🧪 功能测试</h2>
        
        <div style="margin-bottom: 15px;">
            <button @onclick="UpdateTime" 
                    style="padding: 10px 20px; background: #2196F3; color: white; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px;">
                🔄 更新时间
            </button>
            
            <button @onclick="TestCounter" 
                    style="padding: 10px 20px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px;">
                ➕ 计数器 (@counter)
            </button>
            
            <button @onclick="TestAlert" 
                    style="padding: 10px 20px; background: #FF9800; color: white; border: none; border-radius: 4px; cursor: pointer;">
                ⚠️ 测试警告
            </button>
        </div>
    </div>
    
    <div style="background: #fff3e0; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h2 style="color: #f57c00; margin-top: 0;">📝 输入测试</h2>
        <input @bind="userInput" 
               placeholder="在这里输入一些文字..." 
               style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; margin-bottom: 10px;" />
        <p><strong>您输入的内容:</strong> @userInput</p>
    </div>
    
    @if (!string.IsNullOrEmpty(message))
    {
        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; border-left: 4px solid #4CAF50;">
            <p style="margin: 0; color: #2e7d32;"><strong>消息:</strong> @message</p>
        </div>
    }
    
    <div style="background: #fafafa; padding: 20px; border-radius: 8px; margin-top: 20px; border: 1px solid #e0e0e0;">
        <h3 style="color: #666; margin-top: 0;">🔧 调试信息</h3>
        <p style="font-size: 12px; color: #666; font-family: monospace;">
            如果您看到这个页面，说明 .NET MAUI + Blazor 基本功能正常工作！<br/>
            这证明问题不在于 MAUI 框架本身，而可能在于复杂的依赖注入配置。
        </p>
    </div>
</div>

@code {
    private string startTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
    private string currentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
    private string userInput = "";
    private string message = "";
    private int counter = 0;

    protected override void OnInitialized()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("MinimalApp: OnInitialized started");
            startTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            currentTime = startTime;
            message = "应用初始化成功！";
            System.Diagnostics.Debug.WriteLine("MinimalApp: OnInitialized completed successfully");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"MinimalApp OnInitialized error: {ex}");
            message = $"初始化错误: {ex.Message}";
        }
    }

    private void UpdateTime()
    {
        try
        {
            currentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            message = "时间更新成功！";
        }
        catch (Exception ex)
        {
            message = $"更新时间错误: {ex.Message}";
        }
    }

    private void TestCounter()
    {
        try
        {
            counter++;
            message = $"计数器增加到 {counter}";
        }
        catch (Exception ex)
        {
            message = $"计数器错误: {ex.Message}";
        }
    }

    private void TestAlert()
    {
        try
        {
            message = "这是一个测试警告消息！所有功能都在正常工作。";
        }
        catch (Exception ex)
        {
            message = $"警告测试错误: {ex.Message}";
        }
    }
}
