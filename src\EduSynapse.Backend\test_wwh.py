"""
WWH框架测试脚本
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import json
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 创建测试应用
app = FastAPI(title="WWH Framework Test", version="1.0.0")

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 导入WWH框架信息
@app.get("/")
async def root():
    return {
        "message": "WWH Framework Test",
        "endpoints": [
            "/wwh-framework",
            "/wwh-content-example",
            "/wwh-full-framework-example"
        ]
    }

@app.get("/wwh-framework")
async def get_wwh_framework_info():
    """获取WWH教学框架信息"""
    return {
        "framework": "WWH Teaching Framework",
        "description": "What-Why-How结构化教学框架",
        "version": "2.0",
        "stages": [
            {
                "stage": "what",
                "name": "是什么",
                "description": "概念定义、核心特征、基本要素",
                "weight": 0.3,
                "focus": "概念理解",
                "enhanced_features": [
                    "个性化内容生成",
                    "学习者画像适配",
                    "难度级别调整",
                    "视觉化比喻",
                    "常见误解澄清"
                ]
            },
            {
                "stage": "why",
                "name": "为什么",
                "description": "历史背景、重要性、实际价值",
                "weight": 0.3,
                "focus": "背景理解",
                "enhanced_features": [
                    "历史发展脉络",
                    "行业相关性分析",
                    "职业发展益处",
                    "成功案例展示",
                    "学习动机激发"
                ]
            },
            {
                "stage": "how",
                "name": "怎么做",
                "description": "学习方法、实践项目、代码示例",
                "weight": 0.4,
                "focus": "实践应用",
                "enhanced_features": [
                    "分步指导",
                    "实践项目设计",
                    "代码示例生成",
                    "练习题目创建",
                    "资源推荐系统"
                ]
            },
        ],
        "ai_powered": True,
        "supported_profiles": ["visual", "auditory", "kinesthetic", "reading"],
        "difficulty_levels": ["beginner", "intermediate", "advanced"],
    }

@app.get("/wwh-content-example")
async def get_wwh_content_example():
    """获取WWH内容示例"""
    return {
        "success": True,
        "stage": "what",
        "content": {
            "core_definition": "Python是一种高级、解释型、通用编程语言，以简洁易读的语法和强大的生态系统著称。",
            "key_features": [
                "简洁易读的语法",
                "动态类型系统",
                "自动内存管理",
                "丰富的标准库",
                "跨平台兼容性"
            ],
            "basic_elements": [
                "变量和数据类型",
                "控制流结构",
                "函数和模块"
            ],
            "related_concepts": [
                "面向对象编程",
                "函数式编程",
                "脚本语言"
            ],
            "visual_metaphor": "Python就像一把瑞士军刀，简单易用但功能强大，适合解决各种问题。",
            "real_world_examples": [
                "Web开发（Django, Flask）",
                "数据分析（Pandas, NumPy）"
            ],
            "common_misconceptions": [
                "Python太慢不适合生产环境",
                "Python只适合初学者"
            ],
            "difficulty_level": "beginner",
            "learner_profile": "visual",
            "learning_objectives": [
                "理解Python的基本概念和特性",
                "识别Python的应用场景",
                "区分Python与其他编程语言的异同"
            ],
            "prerequisite_knowledge": [
                "基本计算机操作",
                "编程基础概念"
            ],
            "estimated_time": "30分钟",
            "generated_at": "2025-07-15T16:45:00.000Z",
            "ai_generated": True,
            "framework_stage": "what",
            "version": "2.0"
        },
        "metadata": {
            "topic": "Python编程",
            "difficulty": "beginner",
            "learner_profile": "visual",
            "generated_at": "2025-07-15T16:45:00.000Z"
        }
    }

@app.get("/wwh-full-framework-example")
async def get_wwh_full_framework_example():
    """获取完整WWH框架示例"""
    return {
        "success": True,
        "framework": {
            "topic": "Python编程",
            "difficulty": "beginner",
            "learner_profile": "visual",
            "framework": {
                "what": {
                    "core_definition": "Python是一种高级、解释型、通用编程语言，以简洁易读的语法和强大的生态系统著称。",
                    "key_features": [
                        "简洁易读的语法",
                        "动态类型系统",
                        "自动内存管理",
                        "丰富的标准库",
                        "跨平台兼容性"
                    ],
                    "basic_elements": [
                        "变量和数据类型",
                        "控制流结构",
                        "函数和模块"
                    ],
                    "estimated_time": "30分钟"
                },
                "why": {
                    "historical_background": "Python由Guido van Rossum于1991年创建，设计理念是代码可读性和简洁性。",
                    "importance": "Python是当今最流行的编程语言之一，广泛应用于Web开发、数据科学、人工智能等领域。",
                    "practical_value": "学习Python可以快速开发原型，解决实际问题，并为进入热门技术领域打下基础。",
                    "estimated_time": "30分钟"
                },
                "how": {
                    "learning_methods": [
                        "交互式学习",
                        "项目驱动学习",
                        "阅读官方文档",
                        "观看教学视频"
                    ],
                    "step_by_step_guide": [
                        {"step": 1, "title": "安装Python", "description": "下载并安装Python解释器", "duration": "15分钟"},
                        {"step": 2, "title": "学习基础语法", "description": "变量、数据类型、控制流", "duration": "2小时"},
                        {"step": 3, "title": "函数和模块", "description": "创建和使用函数、导入模块", "duration": "1.5小时"}
                    ],
                    "estimated_total_time": "4小时"
                }
            },
            "metadata": {
                "generated_at": "2025-07-15T16:45:00.000Z",
                "framework_version": "2.0",
                "ai_generated": True,
                "total_estimated_time": "5小时",
                "learning_path": [
                    {
                        "stage": "what",
                        "title": "概念理解阶段",
                        "objectives": ["理解Python的基本概念"],
                        "estimated_time": "30分钟"
                    },
                    {
                        "stage": "why",
                        "title": "价值认知阶段",
                        "objectives": ["理解学习Python的价值"],
                        "estimated_time": "30分钟"
                    },
                    {
                        "stage": "how",
                        "title": "实践应用阶段",
                        "objectives": ["掌握Python编程方法"],
                        "estimated_time": "4小时"
                    }
                ]
            }
        }
    }

if __name__ == "__main__":
    # 检查端口是否可用
    def is_port_available(port):
        import socket
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            s.bind(("0.0.0.0", port))
            s.close()
            return True
        except OSError:
            return False
    
    # 选择可用端口
    port = 8001 if is_port_available(8001) else 8002
    
    print(f"🚀 启动WWH框架测试服务器: http://localhost:{port}")
    print(f"📚 API文档: http://localhost:{port}/docs")
    
    uvicorn.run(app, host="0.0.0.0", port=port)
