# EduSynapse Backend Core Dependencies
# 核心依赖包 - 保证基本功能运行

# Web框架 (必需)
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.0.0,<2.6.0  # 限制版本避免与LangChain冲突
pydantic-settings>=2.0.0,<2.6.0

# 数据库 (必需)
sqlalchemy>=2.0.0
alembic>=1.13.0

# 基础工具 (必需)
python-multipart>=0.0.6
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
aiofiles>=23.2.0
python-dotenv>=1.0.0
httpx>=0.25.0

# AI服务 (推荐，但可选)
openai>=1.6.0
anthropic>=0.8.0

# 开发和测试 (可选)
pytest>=7.4.0
pytest-asyncio>=0.21.0
