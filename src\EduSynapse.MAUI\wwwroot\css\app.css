/* EduSynapse Custom Styles */

:root {
    --primary-color: #1976d2;
    --secondary-color: #dc004e;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --error-color: #f44336;
    --info-color: #2196f3;
    --background-color: #fafafa;
    --surface-color: #ffffff;
    --text-primary: rgba(0, 0, 0, 0.87);
    --text-secondary: rgba(0, 0, 0, 0.6);
}

html, body {
    font-family: 'Roboto', sans-serif;
    margin: 0;
    padding: 0;
    height: 100%;
    background-color: var(--background-color);
}

#app {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Loading Styles */
.loading-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error UI */
#blazor-error-ui {
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

#blazor-error-ui .dismiss {
    cursor: pointer;
    position: absolute;
    right: 0.75rem;
    top: 0.5rem;
}

/* Custom Card Styles */
.plan-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    cursor: pointer;
}

.plan-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.status-chip {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 12px;
    text-transform: uppercase;
}

.status-active {
    background-color: #e3f2fd;
    color: #1976d2;
}

.status-completed {
    background-color: #e8f5e8;
    color: #4caf50;
}

.status-paused {
    background-color: #fff3e0;
    color: #ff9800;
}

.status-cancelled {
    background-color: #ffebee;
    color: #f44336;
}

/* Progress Styles */
.progress-circle {
    position: relative;
    display: inline-block;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-weight: 600;
    font-size: 0.875rem;
}

/* WWH Section Styles */
.wwh-section {
    margin-bottom: 24px;
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid;
}

.wwh-what {
    border-left-color: #2196f3;
    background-color: #e3f2fd;
}

.wwh-why {
    border-left-color: #ff9800;
    background-color: #fff3e0;
}

.wwh-how {
    border-left-color: #4caf50;
    background-color: #e8f5e8;
}

.concept-chip {
    margin: 4px;
    background-color: rgba(25, 118, 210, 0.08);
    color: #1976d2;
}

/* Daily Plan Styles */
.daily-plan-card {
    margin-bottom: 16px;
    border-left: 4px solid #1976d2;
}

.task-item {
    padding: 8px 0;
    border-bottom: 1px solid #e0e0e0;
}

.task-item:last-child {
    border-bottom: none;
}

.task-type-badge {
    font-size: 0.75rem;
    padding: 2px 8px;
    border-radius: 12px;
    margin-right: 8px;
}

.task-reading {
    background-color: #e3f2fd;
    color: #1976d2;
}

.task-practice {
    background-color: #e8f5e8;
    color: #4caf50;
}

.task-study {
    background-color: #fff3e0;
    color: #ff9800;
}

.task-project {
    background-color: #f3e5f5;
    color: #9c27b0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .mud-container {
        padding: 8px !important;
    }
    
    .plan-card {
        margin-bottom: 16px;
    }
    
    .wwh-section {
        margin-bottom: 16px;
        padding: 12px;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
    animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Utility Classes */
.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.cursor-pointer {
    cursor: pointer;
}

.elevation-hover {
    transition: box-shadow 0.2s ease-in-out;
}

.elevation-hover:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}
