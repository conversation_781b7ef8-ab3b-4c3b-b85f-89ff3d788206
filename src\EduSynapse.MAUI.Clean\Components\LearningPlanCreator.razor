@page "/learning-plan-creator"
@using EduSynapse.MAUI.Services
@using EduSynapse.MAUI.ViewModels
@using EduSynapse.MAUI.Models
@inject IStateService StateService
@inject StorageService StorageService

<div class="container-fluid p-4">
    <div class="row">
        <div class="col-12">
            <h1 class="text-primary mb-4">📚 学习计划创建器</h1>
            <p class="text-muted">创建个性化的学习计划，开始您的学习之旅</p>
        </div>
    </div>

    <!-- 学习计划表单 -->
    <div class="row">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">🎯 新建学习计划</h5>
                </div>
                <div class="card-body">
                    <form @onsubmit="CreatePlan" @onsubmit:preventDefault="true">
                        <!-- 学习主题 -->
                        <div class="mb-3">
                            <label for="topic" class="form-label">学习主题 *</label>
                            <input @bind="planData.Topic"
                                   type="text"
                                   class="form-control"
                                   id="topic"
                                   placeholder="例如：Python编程基础、英语口语提升..."
                                   required />
                        </div>

                        <!-- 学习时长 -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="duration" class="form-label">学习周期（天）*</label>
                                <input @bind="planData.DurationDays"
                                       type="number"
                                       class="form-control"
                                       id="duration"
                                       min="1"
                                       max="365"
                                       required />
                            </div>
                            <div class="col-md-6">
                                <label for="dailyHours" class="form-label">每日学习时间（小时）*</label>
                                <input @bind="planData.DailyHours"
                                       type="number"
                                       class="form-control"
                                       id="dailyHours"
                                       min="0.5"
                                       max="12"
                                       step="0.5"
                                       required />
                            </div>
                        </div>

                        <!-- 难度级别 -->
                        <div class="mb-3">
                            <label class="form-label">难度级别 *</label>
                            <div class="btn-group w-100" role="group">
                                <input type="radio" class="btn-check" name="difficulty" id="beginner"
                                       checked="@(planData.DifficultyLevel == "easy")"
                                       @onchange="@(() => planData.DifficultyLevel = "easy")" />
                                <label class="btn btn-outline-success" for="beginner">🌱 初级</label>

                                <input type="radio" class="btn-check" name="difficulty" id="intermediate"
                                       checked="@(planData.DifficultyLevel == "medium")"
                                       @onchange="@(() => planData.DifficultyLevel = "medium")" />
                                <label class="btn btn-outline-warning" for="intermediate">🌿 中级</label>

                                <input type="radio" class="btn-check" name="difficulty" id="advanced"
                                       checked="@(planData.DifficultyLevel == "hard")"
                                       @onchange="@(() => planData.DifficultyLevel = "hard")" />
                                <label class="btn btn-outline-danger" for="advanced">🌳 高级</label>
                            </div>
                        </div>

                        <!-- 学习目标 -->
                        <div class="mb-3">
                            <label for="goals" class="form-label">学习目标</label>
                            <textarea @bind="planData.Goals"
                                      class="form-control"
                                      id="goals"
                                      rows="3"
                                      placeholder="描述您希望通过这个学习计划达到的目标..."></textarea>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-outline-secondary" @onclick="ResetForm">
                                🔄 重置
                            </button>
                            <button type="submit" class="btn btn-primary" disabled="@isCreating">
                                @if (isCreating)
                                {
                                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                    <span>创建中...</span>
                                }
                                else
                                {
                                    <span>✨ 创建学习计划</span>
                                }
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建结果 -->
    @if (!string.IsNullOrEmpty(resultMessage))
    {
        <div class="row mt-4">
            <div class="col-lg-8 col-md-10 mx-auto">
                <div class="alert @(isSuccess ? "alert-success" : "alert-danger") alert-dismissible fade show">
                    <strong>@(isSuccess ? "✅ 成功！" : "❌ 错误！")</strong> @resultMessage
                    <button type="button" class="btn-close" @onclick="ClearResult"></button>
                </div>
            </div>
        </div>
    }

    <!-- 已创建的计划预览 -->
    @if (createdPlan != null)
    {
        <div class="row mt-4">
            <div class="col-lg-8 col-md-10 mx-auto">
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">📋 学习计划预览</h5>
                    </div>
                    <div class="card-body">
                        <h6 class="card-title">@createdPlan.Topic</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>学习周期：</strong>@createdPlan.DurationDays 天</p>
                                <p><strong>每日时间：</strong>@createdPlan.TargetHoursPerDay 小时</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>难度级别：</strong>@createdPlan.DifficultyDisplayName</p>
                                <p><strong>状态：</strong>@createdPlan.StatusDisplayName</p>
                            </div>
                        </div>
                        @if (!string.IsNullOrEmpty(planData.Goals))
                        {
                            <p><strong>学习目标：</strong></p>
                            <p class="text-muted">@planData.Goals</p>
                        }
                        <div class="mt-3">
                            <button class="btn btn-outline-primary me-2" @onclick="StartLearning">
                                🚀 开始学习
                            </button>
                            <button class="btn btn-outline-secondary" @onclick="CreateAnother">
                                ➕ 创建另一个计划
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private LearningPlanData planData = new();
    private LearningPlan? createdPlan = null;
    private bool isCreating = false;
    private bool isSuccess = false;
    private string resultMessage = "";

    protected override void OnInitialized()
    {
        ResetForm();
    }

    private async Task CreatePlan()
    {
        if (!ValidateForm())
            return;

        isCreating = true;
        resultMessage = "";

        try
        {
            // 创建学习计划对象
            createdPlan = new LearningPlan
            {
                Id = new Random().Next(1000, 9999), // 生成随机ID
                Topic = planData.Topic,
                DurationDays = planData.DurationDays,
                TargetHoursPerDay = planData.DailyHours,
                DifficultyLevel = planData.DifficultyLevel,
                Description = planData.Goals, // 将目标作为描述
                Status = "active",
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };

            // 保存到本地存储
            var existingPlans = await StorageService.LoadAsync<List<LearningPlan>>("learning_plans") ?? new List<LearningPlan>();
            existingPlans.Add(createdPlan);
            await StorageService.SaveAsync("learning_plans", existingPlans);

            isSuccess = true;
            resultMessage = "学习计划创建成功！您可以开始学习之旅了。";

            System.Diagnostics.Debug.WriteLine($"✅ 学习计划创建成功: {createdPlan.Topic}");
        }
        catch (Exception ex)
        {
            isSuccess = false;
            resultMessage = $"创建学习计划时出错：{ex.Message}";
            System.Diagnostics.Debug.WriteLine($"❌ 创建学习计划失败: {ex}");
        }
        finally
        {
            isCreating = false;
        }
    }

    private bool ValidateForm()
    {
        if (string.IsNullOrWhiteSpace(planData.Topic))
        {
            resultMessage = "请输入学习主题";
            isSuccess = false;
            return false;
        }

        if (planData.DurationDays <= 0)
        {
            resultMessage = "学习周期必须大于0天";
            isSuccess = false;
            return false;
        }

        if (planData.DailyHours <= 0)
        {
            resultMessage = "每日学习时间必须大于0小时";
            isSuccess = false;
            return false;
        }

        if (string.IsNullOrWhiteSpace(planData.DifficultyLevel))
        {
            resultMessage = "请选择难度级别";
            isSuccess = false;
            return false;
        }

        return true;
    }

    private void ResetForm()
    {
        planData = new LearningPlanData
        {
            DurationDays = 30,
            DailyHours = 1.0,
            DifficultyLevel = "easy"
        };
        ClearResult();
        createdPlan = null;
    }

    private void ClearResult()
    {
        resultMessage = "";
        isSuccess = false;
    }

    private void StartLearning()
    {
        // TODO: 导航到学习进度页面
        resultMessage = "学习功能正在开发中...";
        isSuccess = true;
    }

    private void CreateAnother()
    {
        ResetForm();
    }

    // 数据传输对象
    private class LearningPlanData
    {
        public string Topic { get; set; } = "";
        public int DurationDays { get; set; } = 30;
        public double DailyHours { get; set; } = 1.0;
        public string DifficultyLevel { get; set; } = "easy";
        public string Goals { get; set; } = "";
    }
}
