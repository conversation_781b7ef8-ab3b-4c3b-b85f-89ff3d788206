using Microsoft.AspNetCore.Components.WebView.Maui;

namespace EduSynapse.MAUI;

public partial class LearningProgressPage : ContentPage
{
    public LearningProgressPage()
    {
        InitializeComponent();
    }

    public LearningProgressPage(int planId) : this()
    {
        // 可以通过查询参数传递 planId
        var uri = $"learning-progress/{planId}";
        blazorWebView.RootComponents.Clear();
        blazorWebView.RootComponents.Add(new RootComponent
        {
            Selector = "#app",
            ComponentType = typeof(Components.LearningProgressTracker)
        });
    }
}
