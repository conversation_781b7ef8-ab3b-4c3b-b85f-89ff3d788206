"""
EduSynapse Configuration
应用配置管理
"""

from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""

    # 应用基础配置
    app_name: str = "EduSynapse"
    app_version: str = "1.0.0"
    debug: bool = False
    secret_key: str = "your-secret-key-change-in-production"
    host: str = "0.0.0.0"
    port: int = 8000

    # 数据库配置
    database_url: str = "sqlite:///./edusynapse.db"

    # OpenAI API 配置
    openai_api_base: str = "https://api.openai.com/v1"
    openai_api_key: str = ""
    openai_model: str = "gpt-4"
    openai_max_tokens: int = 2000
    openai_temperature: float = 0.7

    # Anthropic Claude API 配置 (可选)
    anthropic_api_key: str = ""
    anthropic_model: str = "claude-3-sonnet-20240229"

    # 日志配置
    log_level: str = "INFO"
    log_file: str = "logs/edusynapse.log"

    # CORS 配置
    allowed_origins: str = (
        "http://localhost:3000,http://localhost:5000,http://localhost:8080"
    )

    # 速率限制
    rate_limit_requests: int = 100
    rate_limit_window: int = 60

    # 缓存配置
    cache_ttl: int = 3600

    # WWH引擎配置
    wwh_max_concepts_per_section: int = 5
    wwh_max_daily_tasks: int = 3
    wwh_default_duration_days: int = 14

    class Config:
        env_file = ".env"
        case_sensitive = False

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        # 验证必需的API密钥
        if not self.openai_api_key and not self.anthropic_api_key:
            print("⚠️ 警告: 未配置AI API密钥，某些功能可能无法正常工作")

        # 显示API配置信息
        if self.openai_api_key:
            print(f"🔗 OpenAI API 基地址: {self.openai_api_base}")
            if self.openai_api_base != "https://api.openai.com/v1":
                print("🔧 使用自定义 OpenAI API 端点")

    @property
    def has_openai_key(self) -> bool:
        """检查是否配置了OpenAI API密钥"""
        return bool(self.openai_api_key and self.openai_api_key.startswith("sk-"))

    @property
    def has_anthropic_key(self) -> bool:
        """检查是否配置了Anthropic API密钥"""
        return bool(self.anthropic_api_key)

    @property
    def preferred_ai_provider(self) -> str:
        """获取首选的AI提供商"""
        if self.has_openai_key:
            return "openai"
        elif self.has_anthropic_key:
            return "anthropic"
        else:
            return "none"

    @property
    def cors_origins(self) -> list[str]:
        """获取CORS允许的源列表"""
        return [
            origin.strip()
            for origin in self.allowed_origins.split(",")
            if origin.strip()
        ]


# 创建全局配置实例
settings = Settings()


# 配置验证
def validate_config():
    """验证配置是否正确"""
    issues = []

    if (
        not settings.secret_key
        or settings.secret_key == "your-secret-key-change-in-production"
    ):
        issues.append("请设置安全的SECRET_KEY")

    if not settings.has_openai_key and not settings.has_anthropic_key:
        issues.append("请配置至少一个AI API密钥 (OpenAI或Anthropic)")

    if settings.openai_temperature < 0 or settings.openai_temperature > 2:
        issues.append("OpenAI temperature应该在0-2之间")

    if issues:
        print("⚠️ 配置问题:")
        for issue in issues:
            print(f"  - {issue}")
        return False

    print("✅ 配置验证通过")
    return True


# 在导入时验证配置
if __name__ != "__main__":
    validate_config()
