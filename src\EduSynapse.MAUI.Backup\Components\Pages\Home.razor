@page "/"
@using EduSynapse.MAUI.ViewModels
@using EduSynapse.MAUI.Models
@using MudBlazor
@using Microsoft.AspNetCore.Components
@inject MainViewModel ViewModel
@inject NavigationManager Navigation

<PageTitle>仪表�?- EduSynapse</PageTitle>

<div class="fade-in">
    <!-- 页面标题 -->
    <MudText Typo="Typo.h4" Class="mb-6">
        <MudIcon Icon="@Icons.Material.Filled.Dashboard" Class="mr-2" />
        学习仪表�?    </MudText>

    <!-- 统计卡片 -->
    <MudGrid Class="mb-6">
        <MudItem xs="12" sm="6" md="3">
            <MudCard Elevation="2" Class="pa-4 text-center">
                <MudIcon Icon="@Icons.Material.Filled.School" Size="Size.Large" Color="Color.Primary" Class="mb-2" />
                <MudText Typo="Typo.h4" Color="Color.Primary">@ViewModel.TotalPlans</MudText>
                <MudText Typo="Typo.body2" Color="Color.Secondary">总计划数</MudText>
            </MudCard>
        </MudItem>
        
        <MudItem xs="12" sm="6" md="3">
            <MudCard Elevation="2" Class="pa-4 text-center">
                <MudIcon Icon="@Icons.Material.Filled.PlayArrow" Size="Size.Large" Color="Color.Success" Class="mb-2" />
                <MudText Typo="Typo.h4" Color="Color.Success">@ViewModel.ActivePlans</MudText>
                <MudText Typo="Typo.body2" Color="Color.Secondary">进行中</MudText>
            </MudCard>
        </MudItem>
        
        <MudItem xs="12" sm="6" md="3">
            <MudCard Elevation="2" Class="pa-4 text-center">
                <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Size="Size.Large" Color="Color.Info" Class="mb-2" />
                <MudText Typo="Typo.h4" Color="Color.Info">@ViewModel.CompletedPlans</MudText>
                <MudText Typo="Typo.body2" Color="Color.Secondary">已完成</MudText>
            </MudCard>
        </MudItem>
        
        <MudItem xs="12" sm="6" md="3">
            <MudCard Elevation="2" Class="pa-4 text-center">
                <MudIcon Icon="@Icons.Material.Filled.TrendingUp" Size="Size.Large" Color="Color.Warning" Class="mb-2" />
                <MudText Typo="Typo.h4" Color="Color.Warning">85%</MudText>
                <MudText Typo="Typo.body2" Color="Color.Secondary">平均完成率</MudText>
            </MudCard>
        </MudItem>
    </MudGrid>

    <!-- 快速操�?-->
    <MudGrid Class="mb-6">
        <MudItem xs="12">
            <MudCard Elevation="2">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">
                            <MudIcon Icon="@Icons.Material.Filled.FlashOn" Class="mr-2" />
                            快速操�?                        </MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudGrid>
                        <MudItem xs="12" sm="6" md="3">
                            <MudButton Variant="Variant.Filled" 
                                       Color="Color.Primary" 
                                       FullWidth="true"
                                       StartIcon="@Icons.Material.Filled.Add"
                                       OnClick="@(() => Navigation.NavigateTo("/create"))">
                                创建新计�?                            </MudButton>
                        </MudItem>
                        <MudItem xs="12" sm="6" md="3">
                            <MudButton Variant="Variant.Outlined" 
                                       Color="Color.Secondary" 
                                       FullWidth="true"
                                       StartIcon="@Icons.Material.Filled.List"
                                       OnClick="@(() => Navigation.NavigateTo("/plans"))">
                                查看所有计�?                            </MudButton>
                        </MudItem>
                        <MudItem xs="12" sm="6" md="3">
                            <MudButton Variant="Variant.Outlined" 
                                       Color="Color.Info" 
                                       FullWidth="true"
                                       StartIcon="@Icons.Material.Filled.Analytics"
                                       OnClick="@(() => Navigation.NavigateTo("/progress"))">
                                学习分析
                            </MudButton>
                        </MudItem>
                        <MudItem xs="12" sm="6" md="3">
                            <MudButton Variant="Variant.Outlined" 
                                       Color="Color.Success" 
                                       FullWidth="true"
                                       StartIcon="@Icons.Material.Filled.Refresh"
                                       OnClick="@ViewModel.LoadDashboardDataCommand.ExecuteAsync">
                                刷新数据
                            </MudButton>
                        </MudItem>
                    </MudGrid>
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>

    <!-- 最近的学习计划 -->
    <MudGrid>
        <MudItem xs="12">
            <MudCard Elevation="2">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">
                            <MudIcon Icon="@Icons.Material.Filled.History" Class="mr-2" />
                            最近的学习计划
                        </MudText>
                    </CardHeaderContent>
                    <CardHeaderActions>
                        <MudButton Variant="Variant.Text" 
                                   Color="Color.Primary"
                                   OnClick="@(() => Navigation.NavigateTo("/plans"))">
                            查看全部
                        </MudButton>
                    </CardHeaderActions>
                </MudCardHeader>
                <MudCardContent>
                    @if (ViewModel.RecentPlans.Any())
                    {
                        <MudGrid>
                            @foreach (var plan in ViewModel.RecentPlans)
                            {
                                <MudItem xs="12" sm="6" md="4">
                                    <MudCard Elevation="1" Class="plan-card" @onclick="@(() => ViewPlanDetails(plan))">
                                        <MudCardContent>
                                            <div class="d-flex justify-space-between align-center mb-2">
                                                <MudChip Size="Size.Small" 
                                                         Color="@GetStatusColor(plan.Status)"
                                                         Variant="Variant.Filled">
                                                    @GetStatusDisplayName(plan.Status)
                                                </MudChip>
                                                <MudText Typo="Typo.caption" Color="Color.Secondary">
                                                    @plan.CreatedAt.ToString("MM/dd")
                                                </MudText>
                                            </div>
                                            
                                            <MudText Typo="Typo.h6" Class="mb-2 text-truncate">
                                                @plan.Topic
                                            </MudText>
                                            
                                            <div class="d-flex justify-space-between align-center mb-2">
                                                <MudText Typo="Typo.body2" Color="Color.Secondary">
                                                    @plan.DurationDays 天计划
                                                </MudText>
                                                <MudText Typo="Typo.body2" Color="Color.Secondary">
                                                    @GetDifficultyDisplayName(plan.DifficultyLevel)
                                                </MudText>
                                            </div>
                                            
                                            <!-- 进度条 -->
                                            <MudProgressLinear Color="Color.Primary" 
                                                               Value="@plan.ProgressPercentage" 
                                                               Class="mb-2" />
                                            <MudText Typo="Typo.caption" Color="Color.Secondary">
                                                进度: @plan.ProgressPercentage.ToString("F1")%
                                            </MudText>
                                        </MudCardContent>
                                    </MudCard>
                                </MudItem>
                            }
                        </MudGrid>
                    }
                    else
                    {
                        <div class="text-center pa-8">
                            <MudIcon Icon="@Icons.Material.Filled.School" Size="Size.Large" Color="Color.Secondary" Class="mb-4" />
                            <MudText Typo="Typo.h6" Color="Color.Secondary" Class="mb-2">还没有学习计划</MudText>
                            <MudText Typo="Typo.body2" Color="Color.Secondary" Class="mb-4">
                                创建您的第一个AI驱动的学习计划，开始智能学习之旅！
                            </MudText>
                            <MudButton Variant="Variant.Filled" 
                                       Color="Color.Primary"
                                       StartIcon="@Icons.Material.Filled.Add"
                                       OnClick="@(() => Navigation.NavigateTo("/create"))">
                                创建学习计划
                            </MudButton>
                        </div>
                    }
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>
</div>

@code {
    protected override async Task OnInitializedAsync()
    {
        await ViewModel.InitializeAsync();
    }

    private void ViewPlanDetails(LearningPlanSummary plan)
    {
        Navigation.NavigateTo($"/plans/{plan.Id}");
    }

    private Color GetStatusColor(string status) => status switch
    {
        "active" => Color.Primary,
        "completed" => Color.Success,
        "paused" => Color.Warning,
        "cancelled" => Color.Error,
        _ => Color.Default
    };

    private string GetStatusDisplayName(string status) => status switch
    {
        "active" => "进行中",
        "completed" => "已完成",
        "paused" => "已暂停",
        "cancelled" => "已取消",
        _ => "未知"
    };

    private string GetDifficultyDisplayName(string difficulty) => difficulty switch
    {
        "easy" => "简单",
        "medium" => "中等",
        "hard" => "困难",
        _ => "未知"
    };
}
