using System.Diagnostics;

namespace EduSynapse.MAUI;

/// <summary>
/// 🧪 第一阶段：最纯净的测试页面
/// 目标：验证 MAUI 基础功能是否正常工作
/// </summary>
public partial class PurePage : ContentPage
{
    private int _testCount = 0;
    private DateTime _startTime;

    public PurePage()
    {
        try
        {
            Debug.WriteLine("🧪 PurePage: 开始初始化...");
            
            InitializeComponent();
            
            _startTime = DateTime.Now;
            UpdateStatus();
            
            Debug.WriteLine("🎉 PurePage: 初始化成功完成！");
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"❌ PurePage 初始化失败: {ex}");
            
            // 即使初始化失败，也尝试显示错误信息
            try
            {
                if (StatusLabel != null)
                {
                    StatusLabel.Text = $"❌ 页面初始化失败: {ex.Message}";
                }
            }
            catch
            {
                // 忽略二次错误
            }
        }
    }

    private void OnTestButtonClicked(object sender, EventArgs e)
    {
        try
        {
            _testCount++;
            
            TestResultLabel.Text = $"✅ 测试成功！点击次数: {_testCount}";
            TestButton.Text = $"🔄 再次测试 ({_testCount})";
            
            UpdateStatus();
            
            Debug.WriteLine($"🧪 按钮测试成功，点击次数: {_testCount}");
        }
        catch (Exception ex)
        {
            TestResultLabel.Text = $"❌ 测试失败: {ex.Message}";
            Debug.WriteLine($"❌ 按钮测试失败: {ex}");
        }
    }

    private void UpdateStatus()
    {
        try
        {
            var now = DateTime.Now;
            var elapsed = now - _startTime;
            
            StatusLabel.Text = "✅ 应用正常运行中...";
            TimeLabel.Text = $"启动时间: {_startTime:HH:mm:ss}\n" +
                           $"当前时间: {now:HH:mm:ss}\n" +
                           $"运行时长: {elapsed.TotalSeconds:F1} 秒\n" +
                           $"测试次数: {_testCount}";
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"❌ 状态更新失败: {ex}");
        }
    }
}
