### 项目框架文档
**1. 项目概览**
```markdown
# EduSynapse - AI驱动自适应学习系统
**核心理念**：通过多AI教师协作与WWH框架，实现个性化高效学习
**技术栈**：
- 前端：.NET MAUI Blazor Hybrid (C#) + MudBlazor UI组件库
- 后端：Python FastAPI + LangChain/AutoGen
- 数据库：SQLite (开发) + SQL Server Express (生产)
- AI框架：LangChain（核心教学流） + AutoGen（多教师协作）
- 部署：Windows桌面应用，个人自用
```

**2. 核心需求实现方案**  
| 需求 | 实现方案 | 技术组件 |  
|------|----------|----------|  
| 学习计划生成 | WWH链式工作流 | LangChain Prompt模板[1] |  
| 多AI教师 | 风格化代理池 | AutoGen教师代理群[1] |  
| 动态调整 | 强化学习策略器 | AutoGen优化模块[1] |  
| 学习笔记 | 自动化文档引擎 | Jinja2模板+Markdown |  

**3. 架构设计**
```mermaid
graph LR
A[MAUI Blazor前端] --> B[HTTP API]
B --> C[FastAPI后端]
C --> D{LangChain核心}
D --> E[WWH教学引擎]
D --> F[SQLite/SQL Server]
C --> G{AutoGen集群}
G --> H[苏格拉底式教师]
G --> I[案例驱动教师]
G --> J[游戏化教师]
A --> K[MudBlazor组件]
```

**4. 开发规范**
- **前端代码规范 (C#/Blazor)**
  ```csharp
  // MAUI Blazor组件示例
  @page "/learning-plan"
  @using MudBlazor

  <MudContainer MaxWidth="MaxWidth.Large">
      <MudText Typo="Typo.h4">📚 学习计划</MudText>
      <!-- WWH框架展示 -->
  </MudContainer>
  ```
- **后端代码规范 (Python)**
  ```python
  # FastAPI端点示例
  @app.post("/api/generate-plan")
  async def generate_whh_plan(request: PlanRequest) -> PlanResponse:
      """遵循WWH框架生成学习计划"""
      # LangChain调用实现
      return await plan_service.generate(request.topic, request.style)
  ```
- **文档标准**
  ```markdown
  ## 每日总结_20250705.md
  ✅ **核心成果**: 掌握OO设计原则
  ⚠️ **实践要点**: 避免过度设计...
  📊 **进度**: WWH框架 - What(90%) Why(75%) How(60%)
  ```

**5. 部署规划**
- **环境需求**：
  - Windows 10/11 (版本 1809 或更高)
  - .NET 8.0 Runtime
  - Python 3.11+ (后端服务)
  - SQLite (默认) 或 SQL Server Express (可选)
- **安装流程**：
  1. 运行 `EduSynapse-Setup.exe` (MAUI应用安装包)
  2. 首次启动自动配置Python后端环境
  3. 配置AI API密钥 (OpenAI/Claude)
  4. 开始使用学习系统

**6. MAUI特有配置**
- **平台支持**：主要Windows，未来可扩展macOS/Linux
- **UI框架**：Blazor Hybrid + MudBlazor Material Design
- **数据存储**：本地SQLite数据库，支持离线使用
- **热重载**：开发时支持实时预览和调试
