@page "/learning-progress/{PlanId:int}"
@using EduSynapse.MAUI.Services
@using EduSynapse.MAUI.Models
@inject StorageService StorageService

<div class="container-fluid p-4">
    @if (currentPlan == null)
    {
        <div class="text-center p-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载学习计划...</p>
        </div>
    }
    else
    {
        <!-- 学习计划信息 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h2 class="mb-2">📚 @currentPlan.Topic</h2>
                                <p class="mb-0">@currentPlan.Description</p>
                            </div>
                            <div class="col-md-4 text-md-end">
                                <div class="row">
                                    <div class="col-6 col-md-12">
                                        <div class="mb-2">
                                            <small>总周期</small>
                                            <div class="h5 mb-0">@currentPlan.DurationDays 天</div>
                                        </div>
                                    </div>
                                    <div class="col-6 col-md-12">
                                        <div>
                                            <small>每日目标</small>
                                            <div class="h5 mb-0">@currentPlan.TargetHoursPerDay 小时</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 今日学习记录 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">📝 今日学习记录 - @DateTime.Today.ToString("yyyy年MM月dd日")</h5>
                    </div>
                    <div class="card-body">
                        <form @onsubmit="SaveTodayProgress" @onsubmit:preventDefault="true">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">学习时间（小时）*</label>
                                        <input @bind="todayRecord.StudyHours" 
                                               type="number" 
                                               class="form-control" 
                                               min="0" 
                                               max="24" 
                                               step="0.5" 
                                               required />
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">学习状态</label>
                                        <select @bind="todayRecord.Status" class="form-select">
                                            <option value="completed">✅ 已完成</option>
                                            <option value="partial">⚠️ 部分完成</option>
                                            <option value="skipped">❌ 未学习</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">学习内容</label>
                                        <input @bind="todayRecord.Content" 
                                               type="text" 
                                               class="form-control" 
                                               placeholder="今天学习了什么内容..." />
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">心情评分（1-10）</label>
                                        <input @bind="todayRecord.MoodScore" 
                                               type="number" 
                                               class="form-control" 
                                               min="1" 
                                               max="10" />
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">学习笔记</label>
                                <textarea @bind="todayRecord.Notes" 
                                          class="form-control" 
                                          rows="3" 
                                          placeholder="记录今天的学习心得、遇到的问题或收获..."></textarea>
                            </div>

                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-outline-secondary" @onclick="ResetTodayRecord">
                                    🔄 重置
                                </button>
                                <button type="submit" class="btn btn-primary" disabled="@isSaving">
                                    @if (isSaving)
                                    {
                                        <span class="spinner-border spinner-border-sm me-2"></span>
                                        <span>保存中...</span>
                                    }
                                    else
                                    {
                                        <span>💾 保存记录</span>
                                    }
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- 学习统计 -->
        <div class="row mb-4">
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-primary">@GetTotalDays()</h4>
                        <p class="mb-0">已学习天数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-success">@GetTotalHours().ToString("F1")</h4>
                        <p class="mb-0">累计学习时间</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-info">@GetAverageHours().ToString("F1")</h4>
                        <p class="mb-0">平均每日时间</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="text-warning">@GetCompletionRate().ToString("F0")%</h4>
                        <p class="mb-0">完成率</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近学习记录 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">📊 最近学习记录</h5>
                    </div>
                    <div class="card-body">
                        @if (progressRecords == null || !progressRecords.Any())
                        {
                            <div class="text-center p-4">
                                <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">还没有学习记录</h6>
                                <p class="text-muted">开始记录您的学习进度吧！</p>
                            </div>
                        }
                        else
                        {
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>日期</th>
                                            <th>学习时间</th>
                                            <th>状态</th>
                                            <th>内容</th>
                                            <th>心情</th>
                                            <th>笔记</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var record in progressRecords.OrderByDescending(r => r.Date).Take(10))
                                        {
                                            <tr>
                                                <td>@record.Date.ToString("MM-dd")</td>
                                                <td>@record.StudyHours.ToString("F1")h</td>
                                                <td>
                                                    <span class="badge @GetStatusBadgeClass(record.Status)">
                                                        @GetStatusDisplayName(record.Status)
                                                    </span>
                                                </td>
                                                <td class="text-truncate" style="max-width: 150px;" title="@record.Content">
                                                    @record.Content
                                                </td>
                                                <td>
                                                    @if (record.MoodScore > 0)
                                                    {
                                                        <span class="badge @GetMoodBadgeClass(record.MoodScore)">
                                                            @record.MoodScore
                                                        </span>
                                                    }
                                                </td>
                                                <td class="text-truncate" style="max-width: 200px;" title="@record.Notes">
                                                    @record.Notes
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- 操作结果提示 -->
    @if (!string.IsNullOrEmpty(resultMessage))
    {
        <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1050;">
            <div class="toast show" role="alert">
                <div class="toast-header">
                    <strong class="me-auto">@(isSuccess ? "✅ 成功" : "❌ 错误")</strong>
                    <button type="button" class="btn-close" @onclick="ClearResult"></button>
                </div>
                <div class="toast-body">
                    @resultMessage
                </div>
            </div>
        </div>
    }
</div>

@code {
    [Parameter] public int PlanId { get; set; }

    private LearningPlan? currentPlan = null;
    private List<ProgressRecord>? progressRecords = null;
    private ProgressRecord todayRecord = new();
    private bool isSaving = false;
    private bool isSuccess = false;
    private string resultMessage = "";

    protected override async Task OnInitializedAsync()
    {
        await LoadPlanAndProgress();
        InitializeTodayRecord();
    }

    private async Task LoadPlanAndProgress()
    {
        try
        {
            // 加载学习计划
            var allPlans = await StorageService.LoadAsync<List<LearningPlan>>("learning_plans") ?? new List<LearningPlan>();
            currentPlan = allPlans.FirstOrDefault(p => p.Id == PlanId);

            if (currentPlan == null)
            {
                ShowMessage("未找到指定的学习计划", false);
                return;
            }

            // 加载进度记录
            var storageKey = $"progress_records_{PlanId}";
            progressRecords = await StorageService.LoadAsync<List<ProgressRecord>>(storageKey) ?? new List<ProgressRecord>();

            System.Diagnostics.Debug.WriteLine($"✅ 加载学习计划和进度: {currentPlan.Topic}, {progressRecords.Count} 条记录");
        }
        catch (Exception ex)
        {
            ShowMessage($"加载数据失败: {ex.Message}", false);
            System.Diagnostics.Debug.WriteLine($"❌ 加载数据失败: {ex}");
        }
    }

    private void InitializeTodayRecord()
    {
        var today = DateTime.Today;
        var existingRecord = progressRecords?.FirstOrDefault(r => r.Date.Date == today);

        if (existingRecord != null)
        {
            todayRecord = new ProgressRecord
            {
                Date = existingRecord.Date,
                StudyHours = existingRecord.StudyHours,
                Status = existingRecord.Status,
                Content = existingRecord.Content,
                MoodScore = existingRecord.MoodScore,
                Notes = existingRecord.Notes,
                PlanId = PlanId
            };
        }
        else
        {
            todayRecord = new ProgressRecord
            {
                Date = today,
                StudyHours = 0,
                Status = "completed",
                Content = "",
                MoodScore = 8,
                Notes = "",
                PlanId = PlanId
            };
        }
    }

    private async Task SaveTodayProgress()
    {
        if (currentPlan == null) return;

        isSaving = true;
        try
        {
            todayRecord.Date = DateTime.Today;
            todayRecord.PlanId = PlanId;

            // 移除今天的旧记录（如果存在）
            progressRecords?.RemoveAll(r => r.Date.Date == DateTime.Today);

            // 添加新记录
            progressRecords ??= new List<ProgressRecord>();
            progressRecords.Add(todayRecord);

            // 保存到存储
            var storageKey = $"progress_records_{PlanId}";
            await StorageService.SaveAsync(storageKey, progressRecords);

            ShowMessage("今日学习记录保存成功！", true);
            System.Diagnostics.Debug.WriteLine($"✅ 保存学习记录: {todayRecord.StudyHours}小时");
        }
        catch (Exception ex)
        {
            ShowMessage($"保存记录失败: {ex.Message}", false);
            System.Diagnostics.Debug.WriteLine($"❌ 保存记录失败: {ex}");
        }
        finally
        {
            isSaving = false;
        }
    }

    private void ResetTodayRecord()
    {
        InitializeTodayRecord();
    }

    private void ShowMessage(string message, bool success)
    {
        resultMessage = message;
        isSuccess = success;

        // 3秒后自动清除消息
        Task.Delay(3000).ContinueWith(_ =>
        {
            resultMessage = "";
            InvokeAsync(StateHasChanged);
        });
    }

    private void ClearResult()
    {
        resultMessage = "";
    }

    // 统计计算方法
    private int GetTotalDays()
    {
        return progressRecords?.Count(r => r.StudyHours > 0) ?? 0;
    }

    private double GetTotalHours()
    {
        return progressRecords?.Sum(r => r.StudyHours) ?? 0;
    }

    private double GetAverageHours()
    {
        var totalDays = GetTotalDays();
        return totalDays > 0 ? GetTotalHours() / totalDays : 0;
    }

    private double GetCompletionRate()
    {
        if (currentPlan == null || progressRecords == null) return 0;

        var daysSinceStart = (DateTime.Today - currentPlan.CreatedAt.Date).Days + 1;
        var studyDays = GetTotalDays();

        return daysSinceStart > 0 ? (double)studyDays / daysSinceStart * 100 : 0;
    }

    // UI 辅助方法
    private string GetStatusBadgeClass(string status)
    {
        return status switch
        {
            "completed" => "bg-success",
            "partial" => "bg-warning",
            "skipped" => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private string GetStatusDisplayName(string status)
    {
        return status switch
        {
            "completed" => "已完成",
            "partial" => "部分完成",
            "skipped" => "未学习",
            _ => "未知"
        };
    }

    private string GetMoodBadgeClass(int moodScore)
    {
        return moodScore switch
        {
            >= 8 => "bg-success",
            >= 6 => "bg-info",
            >= 4 => "bg-warning",
            _ => "bg-danger"
        };
    }

    // 进度记录数据模型
    public class ProgressRecord
    {
        public DateTime Date { get; set; }
        public double StudyHours { get; set; }
        public string Status { get; set; } = "completed";
        public string Content { get; set; } = "";
        public int MoodScore { get; set; } = 8;
        public string Notes { get; set; } = "";
        public int PlanId { get; set; }
    }
}
