#!/usr/bin/env python3
"""
EduSynapse Backend - Stable Startup Script
稳定启动脚本，禁用自动重载
"""

import uvicorn
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """启动服务器"""
    print("🚀 启动EduSynapse Backend (稳定模式)...")
    print("📍 访问地址: http://localhost:8000")
    print("📚 API文档: http://localhost:8000/docs")
    print("🔍 健康检查: http://localhost:8000/health")
    print("🤖 AI状态: http://localhost:8000/api/ai/status")
    print("=" * 50)
    
    try:
        uvicorn.run(
            "main:app",
            host="0.0.0.0",
            port=8000,
            reload=False,  # 禁用自动重载
            log_level="info",
            access_log=True,
        )
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
