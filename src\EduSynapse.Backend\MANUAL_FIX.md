# 🔧 EduSynapse 依赖问题手动修复指南

## 问题描述
LangChain与Pydantic版本冲突导致启动失败。

## 快速修复方案

### 方案1：使用Python修复脚本（推荐）
```bash
cd src\EduSynapse.Backend
python fix_dependencies.py
```

### 方案2：手动执行命令
按顺序执行以下命令：

#### 1. 卸载冲突包
```bash
pip uninstall -y langchain langchain-openai langchain-community pyautogen pydantic pydantic-settings
```

#### 2. 安装兼容版本的Pydantic
```bash
pip install "pydantic>=2.0.0,<2.6.0" "pydantic-settings>=2.0.0,<2.6.0"
```

#### 3. 安装核心依赖
```bash
pip install "fastapi>=0.104.0" "uvicorn[standard]>=0.24.0"
pip install "sqlalchemy>=2.0.0" "alembic>=1.13.0"
pip install "python-multipart>=0.0.6" "python-jose[cryptography]>=3.3.0"
pip install "passlib[bcrypt]>=1.7.4" "aiofiles>=23.2.0"
pip install "python-dotenv>=1.0.0" "httpx>=0.25.0"
```

#### 4. 安装AI服务（可选）
```bash
pip install "openai>=1.6.0" "anthropic>=0.8.0"
```

#### 5. 安装测试工具
```bash
pip install "pytest>=7.4.0" "pytest-asyncio>=0.21.0"
```

## 启动服务
```bash
python main.py
```

## 验证修复
访问以下地址确认服务正常：
- 主页: http://localhost:8000
- API文档: http://localhost:8000/docs
- AI状态: http://localhost:8000/api/ai/status

## 运行模式说明

### 🔄 系统会自动选择运行模式：

1. **AI模式** - 如果LangChain和AutoGen可用
2. **混合模式** - 如果部分AI功能可用
3. **备用模式** - 使用预设模板（当前推荐模式）

### 📝 备用模式功能：
- ✅ 完整的WWH教学框架
- ✅ 学习计划生成
- ✅ 教学会话对话
- ✅ 多教师风格模拟
- ✅ 所有API端点正常工作

## 常见问题

### Q: 为什么不安装LangChain？
A: 当前LangChain版本与新版Pydantic存在兼容性问题，暂时跳过以确保系统稳定运行。

### Q: 备用模式功能完整吗？
A: 是的，备用模式提供完整的教学功能，只是使用预设模板而非AI生成内容。

### Q: 如何启用AI功能？
A: 等待LangChain发布兼容版本，或使用特定的兼容版本组合。

### Q: 系统性能如何？
A: 备用模式响应更快，因为不需要调用外部AI服务。

## 故障排除

### 如果仍然出错：
1. 检查Python版本（需要3.8+）
2. 升级pip：`python -m pip install --upgrade pip`
3. 清理pip缓存：`pip cache purge`
4. 重新创建虚拟环境

### 获取帮助：
- 查看错误日志
- 检查 http://localhost:8000/api/ai/status
- 运行测试：`python -m pytest tests/`

## 成功标志
修复成功后，您应该看到：
- ✅ 服务启动无错误
- ✅ 可以访问API文档
- ✅ AI状态显示"备用模式"
- ✅ 可以生成学习计划
- ✅ 可以进行教学对话
