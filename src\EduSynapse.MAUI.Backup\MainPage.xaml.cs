using System.Diagnostics;

namespace EduSynapse.MAUI;

public partial class MainPage : ContentPage
{
    public MainPage()
    {
        try
        {
            Debug.WriteLine("MainPage: Starting initialization...");
            InitializeComponent();
            Debug.WriteLine("MainPage: InitializeComponent completed");

            // 添加 Blazor WebView 错误处理
            if (blazorWebView != null)
            {
                blazorWebView.BlazorWebViewInitialized += OnBlazorWebViewInitialized;
                blazorWebView.UrlLoading += OnUrlLoading;
                Debug.WriteLine("MainPage: Blazor WebView event handlers attached");
            }

            Debug.WriteLine("MainPage initialized successfully");
        }
        catch (Exception ex)
        {
            var detailedError = $"MainPage initialization failed:\n" +
                               $"Type: {ex.GetType().FullName}\n" +
                               $"Message: {ex.Message}\n" +
                               $"Stack Trace: {ex.StackTrace}\n" +
                               $"Inner Exception: {ex.InnerException}";

            Debug.WriteLine(detailedError);
            Console.WriteLine(detailedError);

            // 创建一个详细的错误页面
            Content = new ScrollView
            {
                Content = new StackLayout
                {
                    Padding = new Thickness(20),
                    Children =
                    {
                        new Label
                        {
                            Text = "MainPage 加载失败",
                            FontSize = 18,
                            FontAttributes = FontAttributes.Bold,
                            HorizontalOptions = LayoutOptions.Center,
                            Margin = new Thickness(0, 0, 0, 20)
                        },
                        new Label
                        {
                            Text = $"错误类型: {ex.GetType().Name}",
                            FontSize = 14,
                            Margin = new Thickness(0, 0, 0, 10)
                        },
                        new Label
                        {
                            Text = $"错误信息: {ex.Message}",
                            FontSize = 14,
                            Margin = new Thickness(0, 0, 0, 10)
                        },
                        new Label
                        {
                            Text = "详细信息:",
                            FontSize = 14,
                            FontAttributes = FontAttributes.Bold,
                            Margin = new Thickness(0, 10, 0, 5)
                        },
                        new Label
                        {
                            Text = ex.StackTrace ?? "无堆栈跟踪信息",
                            FontSize = 12,
                            FontFamily = "Courier",
                            Margin = new Thickness(0, 0, 0, 20)
                        },
                        new Button
                        {
                            Text = "重试初始化",
                            Command = new Command(() => {
                                try
                                {
                                    Debug.WriteLine("MainPage: Retrying initialization...");
                                    InitializeComponent();
                                    Debug.WriteLine("MainPage: Retry successful");
                                }
                                catch (Exception retryEx)
                                {
                                    Debug.WriteLine($"MainPage retry failed: {retryEx}");
                                }
                            })
                        }
                    }
                }
            };
        }
    }

    private void OnBlazorWebViewInitialized(object sender, Microsoft.AspNetCore.Components.WebView.BlazorWebViewInitializedEventArgs e)
    {
        Debug.WriteLine("Blazor WebView initialized successfully");
    }

    private void OnUrlLoading(object sender, Microsoft.AspNetCore.Components.WebView.UrlLoadingEventArgs e)
    {
        Debug.WriteLine($"Blazor WebView loading URL: {e.Url}");
    }
}
