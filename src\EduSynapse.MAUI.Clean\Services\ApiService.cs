using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Http;
using EduSynapse.MAUI.Models;

namespace EduSynapse.MAUI.Services;

/// <summary>
/// API服务实现
/// </summary>
public class ApiService : IApiService
{
    private readonly HttpClient _httpClient;
    private readonly JsonSerializerOptions _jsonOptions;

    public ApiService(IHttpClientFactory httpClientFactory)
    {
        _httpClient = httpClientFactory.CreateClient("EduSynapseAPI");
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower,
            WriteIndented = true
        };
    }

    public async Task<HealthCheckResponse?> CheckHealthAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync("health");
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadFromJsonAsync<HealthCheckResponse>(_jsonOptions);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Health check failed: {ex.Message}");
        }
        return null;
    }

    public async Task<LearningPlan?> GenerateLearningPlanAsync(PlanGenerationRequest request)
    {
        try
        {
            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync("api/learning-plans/generate", content);
            
            if (response.IsSuccessStatusCode)
            {
                var apiResponse = await response.Content.ReadFromJsonAsync<ApiResponse<object>>(_jsonOptions);
                if (apiResponse?.Success == true && apiResponse.Data != null)
                {
                    // 解析嵌套的数据结构
                    var dataJson = JsonSerializer.Serialize(apiResponse.Data, _jsonOptions);
                    var planData = JsonSerializer.Deserialize<Dictionary<string, object>>(dataJson, _jsonOptions);
                    
                    if (planData != null)
                    {
                        return new LearningPlan
                        {
                            Id = GetIntValue(planData, "plan_id"),
                            Topic = GetStringValue(planData, "topic"),
                            DurationDays = GetIntValue(planData, "duration_days"),
                            DifficultyLevel = GetStringValue(planData, "difficulty_level"),
                            Status = GetStringValue(planData, "status"),
                            CreatedAt = GetDateTimeValue(planData, "created_at"),
                            WwhStructure = ParseWwhStructure(planData, "wwh_structure")
                        };
                    }
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Generate plan failed: {ex.Message}");
        }
        return null;
    }

    public async Task<LearningPlansListResponse?> GetLearningPlansAsync(string? statusFilter = null, int limit = 10, int offset = 0)
    {
        try
        {
            var query = $"api/learning-plans/?limit={limit}&offset={offset}";
            if (!string.IsNullOrEmpty(statusFilter))
            {
                query += $"&status_filter={statusFilter}";
            }

            var response = await _httpClient.GetAsync(query);
            if (response.IsSuccessStatusCode)
            {
                var apiResponse = await response.Content.ReadFromJsonAsync<ApiResponse<LearningPlansListResponse>>(_jsonOptions);
                return apiResponse?.Data;
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Get plans failed: {ex.Message}");
        }
        return null;
    }

    public async Task<LearningPlan?> GetLearningPlanAsync(int planId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/learning-plans/{planId}");
            if (response.IsSuccessStatusCode)
            {
                var apiResponse = await response.Content.ReadFromJsonAsync<ApiResponse<object>>(_jsonOptions);
                if (apiResponse?.Success == true && apiResponse.Data != null)
                {
                    var dataJson = JsonSerializer.Serialize(apiResponse.Data, _jsonOptions);
                    var planData = JsonSerializer.Deserialize<Dictionary<string, object>>(dataJson, _jsonOptions);
                    
                    if (planData != null)
                    {
                        return new LearningPlan
                        {
                            Id = GetIntValue(planData, "id"),
                            Topic = GetStringValue(planData, "topic"),
                            Description = GetStringValue(planData, "description"),
                            DurationDays = GetIntValue(planData, "duration_days"),
                            DifficultyLevel = GetStringValue(planData, "difficulty_level"),
                            TargetHoursPerDay = GetDoubleValue(planData, "target_hours_per_day"),
                            Status = GetStringValue(planData, "status"),
                            CreatedAt = GetDateTimeValue(planData, "created_at"),
                            UpdatedAt = GetDateTimeValue(planData, "updated_at"),
                            StartedAt = GetNullableDateTimeValue(planData, "started_at"),
                            CompletedAt = GetNullableDateTimeValue(planData, "completed_at"),
                            WwhStructure = ParseWwhStructure(planData, "wwh_structure")
                        };
                    }
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Get plan failed: {ex.Message}");
        }
        return null;
    }

    public async Task<LearningPlan?> UpdateLearningPlanAsync(int planId, object updateData)
    {
        try
        {
            var json = JsonSerializer.Serialize(updateData, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PutAsync($"api/learning-plans/{planId}", content);
            if (response.IsSuccessStatusCode)
            {
                var apiResponse = await response.Content.ReadFromJsonAsync<ApiResponse<LearningPlan>>(_jsonOptions);
                return apiResponse?.Data;
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Update plan failed: {ex.Message}");
        }
        return null;
    }

    public async Task<bool> DeleteLearningPlanAsync(int planId)
    {
        try
        {
            var response = await _httpClient.DeleteAsync($"api/learning-plans/{planId}");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Delete plan failed: {ex.Message}");
            return false;
        }
    }

    public async Task<LearningProgress?> RecordProgressAsync(int planId, ProgressRecordRequest request)
    {
        try
        {
            var json = JsonSerializer.Serialize(request, _jsonOptions);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync($"api/learning-plans/{planId}/progress", content);
            if (response.IsSuccessStatusCode)
            {
                var apiResponse = await response.Content.ReadFromJsonAsync<ApiResponse<LearningProgress>>(_jsonOptions);
                return apiResponse?.Data;
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Record progress failed: {ex.Message}");
        }
        return null;
    }

    public async Task<List<LearningProgress>?> GetProgressListAsync(int planId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/learning-plans/{planId}/progress");
            if (response.IsSuccessStatusCode)
            {
                var apiResponse = await response.Content.ReadFromJsonAsync<ApiResponse<List<LearningProgress>>>(_jsonOptions);
                return apiResponse?.Data;
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Get progress failed: {ex.Message}");
        }
        return null;
    }

    public async Task<bool> TestConnectionAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync("health");
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    public async Task<ProgressStatsResponse?> GetProgressStatsAsync(int planId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/progress/stats/{planId}");
            if (response.IsSuccessStatusCode)
            {
                var apiResponse = await response.Content.ReadFromJsonAsync<ApiResponse<ProgressStatsResponse>>(_jsonOptions);
                return apiResponse?.Data;
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Get progress stats failed: {ex.Message}");
        }
        return null;
    }

    public async Task<ProgressCalendarResponse?> GetProgressCalendarAsync(int planId, int year, int month)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/progress/calendar/{planId}?year={year}&month={month}");
            if (response.IsSuccessStatusCode)
            {
                var apiResponse = await response.Content.ReadFromJsonAsync<ApiResponse<ProgressCalendarResponse>>(_jsonOptions);
                return apiResponse?.Data;
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Get progress calendar failed: {ex.Message}");
        }
        return null;
    }

    public async Task<ProgressAnalysis?> GetProgressAnalysisAsync(int planId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/progress/analysis/{planId}");
            if (response.IsSuccessStatusCode)
            {
                var apiResponse = await response.Content.ReadFromJsonAsync<ApiResponse<ProgressAnalysis>>(_jsonOptions);
                return apiResponse?.Data;
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Get progress analysis failed: {ex.Message}");
        }
        return null;
    }

    public async Task<LearningProgress?> GetDailyProgressAsync(int planId, int dayNumber)
    {
        try
        {
            var response = await _httpClient.GetAsync($"api/progress/daily/{planId}/{dayNumber}");
            if (response.IsSuccessStatusCode)
            {
                var apiResponse = await response.Content.ReadFromJsonAsync<ApiResponse<LearningProgress>>(_jsonOptions);
                return apiResponse?.Data;
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Get daily progress failed: {ex.Message}");
        }
        return null;
    }

    // 辅助方法
    private static string GetStringValue(Dictionary<string, object> data, string key)
    {
        return data.TryGetValue(key, out var value) ? value?.ToString() ?? string.Empty : string.Empty;
    }

    private static int GetIntValue(Dictionary<string, object> data, string key)
    {
        if (data.TryGetValue(key, out var value))
        {
            if (value is JsonElement element && element.ValueKind == JsonValueKind.Number)
            {
                return element.GetInt32();
            }
            if (int.TryParse(value?.ToString(), out var result))
            {
                return result;
            }
        }
        return 0;
    }

    private static double GetDoubleValue(Dictionary<string, object> data, string key)
    {
        if (data.TryGetValue(key, out var value))
        {
            if (value is JsonElement element && element.ValueKind == JsonValueKind.Number)
            {
                return element.GetDouble();
            }
            if (double.TryParse(value?.ToString(), out var result))
            {
                return result;
            }
        }
        return 0.0;
    }

    private static DateTime GetDateTimeValue(Dictionary<string, object> data, string key)
    {
        if (data.TryGetValue(key, out var value) && DateTime.TryParse(value?.ToString(), out var result))
        {
            return result;
        }
        return DateTime.MinValue;
    }

    private static DateTime? GetNullableDateTimeValue(Dictionary<string, object> data, string key)
    {
        if (data.TryGetValue(key, out var value) && value != null && DateTime.TryParse(value.ToString(), out var result))
        {
            return result;
        }
        return null;
    }

    private WWHStructure? ParseWwhStructure(Dictionary<string, object> data, string key)
    {
        if (data.TryGetValue(key, out var value) && value != null)
        {
            try
            {
                var json = JsonSerializer.Serialize(value, _jsonOptions);
                return JsonSerializer.Deserialize<WWHStructure>(json, _jsonOptions);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Parse WWH structure failed: {ex.Message}");
            }
        }
        return null;
    }
}
