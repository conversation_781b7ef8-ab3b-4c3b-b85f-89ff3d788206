"""
EduSynapse Database Configuration
数据库配置和连接管理
"""

import os
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

# 从配置获取数据库URL
try:
    from app.core.config import settings

    DATABASE_URL = settings.database_url
except ImportError:
    # 如果配置模块不可用，使用环境变量
    DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./edusynapse.db")

# 创建数据库引擎
if DATABASE_URL.startswith("sqlite"):
    # SQLite特定配置
    engine = create_engine(
        DATABASE_URL,
        connect_args={"check_same_thread": False, "timeout": 20},
        poolclass=StaticPool,
        echo=os.getenv("DEBUG", "False").lower() == "true",
    )
else:
    # 其他数据库配置
    engine = create_engine(
        DATABASE_URL, echo=os.getenv("DEBUG", "False").lower() == "true"
    )

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型类
Base = declarative_base()

# 元数据对象
metadata = MetaData()


def get_db():
    """
    获取数据库会话
    用于依赖注入
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def create_tables():
    """
    创建所有数据表
    """
    try:
        # 确保数据库目录存在
        if DATABASE_URL.startswith("sqlite"):
            db_path = DATABASE_URL.replace("sqlite:///", "")
            db_dir = os.path.dirname(db_path)
            if db_dir and not os.path.exists(db_dir):
                os.makedirs(db_dir, exist_ok=True)
                print(f"✅ 创建数据库目录: {db_dir}")

        Base.metadata.create_all(bind=engine)
        print("✅ 数据库表创建完成!")
    except Exception as e:
        print(f"❌ 数据库表创建失败: {e}")
        raise


def drop_tables():
    """
    删除所有数据表 (谨慎使用)
    """
    Base.metadata.drop_all(bind=engine)
    print("⚠️ 数据库表已删除!")


def reset_database():
    """
    重置数据库 (删除并重新创建所有表)
    """
    print("🔄 正在重置数据库...")
    drop_tables()
    create_tables()
    print("✅ 数据库重置完成!")


# 数据库健康检查
def check_database_connection():
    """
    检查数据库连接是否正常
    """
    try:
        from sqlalchemy import text

        print(f"🔍 正在测试数据库连接: {DATABASE_URL}")

        with engine.connect() as connection:
            result = connection.execute(text("SELECT 1"))
            # 获取结果以确保查询真正执行
            result.fetchone()

        print("✅ 数据库连接测试成功")
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        print(f"   数据库URL: {DATABASE_URL}")
        print(f"   错误类型: {type(e).__name__}")
        return False
