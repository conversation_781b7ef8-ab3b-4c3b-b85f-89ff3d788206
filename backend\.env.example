# EduSynapse AI Backend Environment Configuration
# 复制此文件为 .env 并填入实际配置值

# =============================================================================
# 基础配置
# =============================================================================
DEBUG=true
HOST=0.0.0.0
PORT=8000
ENVIRONMENT=development

# =============================================================================
# AI服务配置 (必需)
# =============================================================================
# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_API_BASE=https://api.openai.com/v1
OPENAI_MODEL=gpt-4
OPENAI_TEMPERATURE=0.7
OPENAI_MAX_TOKENS=2000

# =============================================================================
# 数据库配置
# =============================================================================
DATABASE_URL=sqlite:///./edusynapse.db
DATABASE_ECHO=false

# =============================================================================
# 安全配置
# =============================================================================
SECRET_KEY=your-super-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# =============================================================================
# CORS配置
# =============================================================================
ALLOWED_ORIGINS=["http://localhost:3000","http://localhost:8080","http://127.0.0.1:3000"]

# =============================================================================
# 日志配置
# =============================================================================
LOG_LEVEL=INFO
LOG_FORMAT="%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# =============================================================================
# 文件存储配置
# =============================================================================
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=[".pdf",".txt",".docx",".py",".js",".html",".css"]

# =============================================================================
# AI教学引擎配置
# =============================================================================
MAX_SESSION_DURATION=3600
MAX_CONCURRENT_SESSIONS=100
SESSION_CLEANUP_INTERVAL=300

# =============================================================================
# WWH框架权重配置
# =============================================================================
WWH_WHAT_WEIGHT=0.3
WWH_WHY_WEIGHT=0.3
WWH_HOW_WEIGHT=0.4

# =============================================================================
# 进度跟踪配置
# =============================================================================
PROGRESS_UPDATE_INTERVAL=60
MASTERY_THRESHOLD=0.7
ERROR_PATTERN_MIN_FREQUENCY=3

# =============================================================================
# 缓存配置
# =============================================================================
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# =============================================================================
# 监控配置
# =============================================================================
ENABLE_METRICS=true
METRICS_PORT=9090

# =============================================================================
# 开发配置
# =============================================================================
RELOAD=true
WORKERS=1

# =============================================================================
# Redis配置 (可选)
# =============================================================================
# REDIS_URL=redis://localhost:6379
# REDIS_PASSWORD=your_redis_password
