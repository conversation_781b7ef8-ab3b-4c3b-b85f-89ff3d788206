"""
EduSynapse Backend Main Application
EduSynapse 后端主应用程序
"""

import os
import sys
import uvicorn
from datetime import datetime
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings, validate_config
from app.database.database import create_tables, check_database_connection
from app.api.learning_plan import router as learning_plan_router
from app.api.ai_teaching import router as ai_teaching_router


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    print("🚀 EduSynapse Backend 正在启动...")

    # 验证配置
    if not validate_config():
        print("❌ 配置验证失败，请检查配置文件")
        sys.exit(1)

    # 检查数据库连接
    if not check_database_connection():
        print("❌ 数据库连接失败")
        sys.exit(1)

    # 创建数据库表
    try:
        create_tables()
        print("✅ 数据库初始化完成")
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        sys.exit(1)

    print(f"✅ EduSynapse Backend 启动成功!")
    print(f"📊 API文档地址: http://{settings.host}:{settings.port}/docs")
    print(f"🔧 配置信息:")
    print(f"   - 调试模式: {settings.debug}")
    print(f"   - 数据库: {settings.database_url}")
    print(f"   - AI提供商: {settings.preferred_ai_provider}")

    yield

    # 关闭时执行
    print("🛑 EduSynapse Backend 正在关闭...")


# 创建FastAPI应用
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="EduSynapse 智能学习系统后端API",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan,
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 全局异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理"""
    print(f"❌ 未处理的异常: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": {
                "code": "INTERNAL_SERVER_ERROR",
                "message": "服务器内部错误",
                "details": str(exc) if settings.debug else "请联系管理员",
            },
            "timestamp": datetime.now().isoformat(),
        },
    )


# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查"""
    try:
        # 简化的数据库检查
        db_status = True  # 暂时假设数据库正常
        ai_provider = getattr(settings, "preferred_ai_provider", "openai")

        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": settings.app_version,
            "database": "connected",
            "ai_provider": ai_provider,
            "backend_mode": "operational",
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e),
            "backend_mode": "error",
        }


# 根路径
@app.get("/")
async def root():
    """根路径信息"""
    return {
        "message": "🎓 欢迎使用 EduSynapse AI智能教学系统 API",
        "version": settings.app_version,
        "docs": "/docs",
        "health": "/health",
        "ai_status": "/api/ai/status",
        "timestamp": datetime.now().isoformat(),
        "features": [
            "🤖 AI智能教学引擎 (LangChain + AutoGen)",
            "📚 WWH教学框架",
            "🎭 多AI教师协作",
            "📊 智能学习计划生成",
            "📈 学习进度跟踪",
            "🔍 智能分析",
        ],
        "ai_endpoints": {
            "generate_plan": "/api/ai/generate-plan",
            "start_session": "/api/ai/start-session",
            "continue_session": "/api/ai/continue-session",
            "teachers": "/api/ai/teachers",
            "wwh_framework": "/api/ai/wwh-framework",
        },
    }


# 注册路由
app.include_router(learning_plan_router)

# 导入并注册进度路由
from app.api.progress import router as progress_router

app.include_router(progress_router)

# 注册AI教学路由
app.include_router(ai_teaching_router)


# 开发服务器启动
if __name__ == "__main__":
    print("🔧 开发模式启动...")

    # 确保日志目录存在
    log_dir = os.path.dirname(settings.log_file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 启动服务器
    # 使用配置中的端口，如果被占用则尝试备用端口
    port = settings.port
    try:
        import socket

        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.bind((settings.host, port))
        s.close()
    except OSError:
        # 端口被占用，使用备用端口
        backup_port = 8001
        print(f"⚠️ 端口 {port} 已被占用，尝试使用备用端口 {backup_port}")
        port = backup_port

    print(f"🚀 启动服务器: http://{settings.host}:{port}")

    uvicorn.run(
        app,  # 直接传递app对象
        host=settings.host,
        port=port,  # 使用配置中的端口或备用端口
        reload=False,  # 暂时禁用自动重载
        log_level=settings.log_level.lower(),
        access_log=True,
    )
