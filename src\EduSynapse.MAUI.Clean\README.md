# EduSynapse MAUI Frontend (Clean Version)

EduSynapse 智能学习系统的跨平台前端应用，基于 .NET MAUI 和 Blazor Hybrid 构建。

> **项目状态**: ✅ **Blazor 事件问题已解决** - 经过深度调试和重构，JavaScript 事件绑定问题已完全修复

## 🎯 项目概述

这是 EduSynapse 前端的**清洁重构版本**，解决了原版本中的 Blazor WebView 事件绑定问题，并整合了完整的业务逻辑代码。

### 🔧 已解决的关键问题
- ✅ **Blazor 事件绑定失效** - 修复了 `@onclick` 等事件无响应的问题
- ✅ **MudBlazor 依赖冲突** - 清理了导致运行时错误的包依赖
- ✅ **导航架构问题** - 实现了正确的 NavigationPage 配置
- ✅ **业务逻辑完整性** - 迁移了所有 Models、Services、ViewModels

## 🚀 快速开始

### 环境要求

- **Visual Studio 2022** (17.8 或更高版本)
- **.NET 9.0 SDK** (当前使用版本)
- **MAUI 工作负载**
- **Windows 10** 版本 1809 或更高版本
- **WebView2 运行时** (通常已预装)

### 安装和运行

1. **安装 MAUI 工作负载**
   ```bash
   dotnet workload install maui
   ```

2. **还原 NuGet 包**
   ```bash
   dotnet restore
   ```

3. **运行应用**
   ```bash
   # 使用 Visual Studio (推荐)
   # 1. 打开 EduSynapse.MAUI.csproj
   # 2. 选择 Windows Machine 作为启动目标
   # 3. 按 F5 启动调试

   # 或使用命令行
   dotnet build --framework net9.0-windows10.0.19041.0
   dotnet run --framework net9.0-windows10.0.19041.0
   ```

## 🏗️ 项目结构

```
EduSynapse.MAUI.Clean/
├── Components/              # Blazor 组件
│   ├── Layout/              # 布局组件
│   │   ├── MainLayout.razor
│   │   ├── SafeMainLayout.razor
│   │   └── SimpleLayout.razor
│   ├── Pages/               # 页面组件
│   │   ├── Home.razor
│   │   ├── Progress.razor
│   │   └── MinimalBlazorTest.razor
│   ├── Shared/              # 共享组件
│   │   ├── ProgressAnalysis.razor
│   │   ├── ProgressCalendar.razor
│   │   ├── ProgressHistory.razor
│   │   └── ProgressRecordForm.razor
│   ├── App.razor           # 根组件
│   ├── MinimalApp.razor    # 最小应用组件
│   ├── TestApp.razor       # 测试应用组件
│   └── [测试组件...]       # 各种调试测试组件
├── Models/                  # 数据模型
│   ├── LearningPlan.cs     # 学习计划模型
│   └── ApiModels.cs        # API 数据模型
├── Services/                # 业务服务
│   ├── IApiService.cs      # API 服务接口
│   ├── ApiService.cs       # API 服务实现
│   ├── IStateService.cs    # 状态服务接口
│   ├── StateService.cs     # 状态服务实现
│   └── StorageService.cs   # 存储服务
├── ViewModels/              # 视图模型 (MVVM)
│   ├── BaseViewModel.cs    # 基础视图模型
│   ├── MainViewModel.cs    # 主视图模型
│   ├── LearningPlanViewModel.cs # 学习计划视图模型
│   └── ProgressViewModel.cs # 进度视图模型
├── Pages/                   # MAUI 页面
│   ├── MainPage.xaml       # 主页面
│   ├── BlazorTestPage.xaml # Blazor 测试页面
│   ├── CleanTestPage.xaml  # 清洁测试页面
│   ├── PurePage.xaml       # 纯净测试页面
│   └── TestPage.xaml       # 通用测试页面
├── Resources/               # 资源文件
│   ├── Styles/             # 样式文件
│   ├── Images/             # 图片资源
│   ├── Fonts/              # 字体文件
│   └── Raw/                # 原始资源
├── wwwroot/                 # Web 资源
│   ├── css/                # CSS 样式
│   │   ├── bootstrap/
│   │   └── app.css
│   └── index.html          # Blazor 宿主页面
├── Platforms/               # 平台特定代码
│   ├── Windows/            # Windows 平台
│   ├── Android/            # Android 平台
│   └── [其他平台...]
├── MauiProgram.cs          # 应用配置和服务注册
├── App.xaml                # 应用定义
├── AppShell.xaml           # Shell 导航
├── README.md               # 项目文档
└── TROUBLESHOOTING.md      # 故障排除指南
```

## 🎨 技术栈

### 核心框架
- **.NET 9.0**: 最新的 .NET 平台
- **.NET MAUI**: 跨平台应用框架
- **Blazor Hybrid**: Web UI 在原生容器中运行
- **WebView2**: Windows 平台的现代 Web 视图

### 状态管理
- **MVVM 模式**: 视图模型架构
- **依赖注入**: 服务管理和生命周期
- **状态服务**: 应用状态管理

### 数据和网络
- **HttpClient**: HTTP 通信
- **Microsoft.Extensions.Http**: HTTP 客户端工厂
- **System.Text.Json**: JSON 序列化
- **本地存储**: 文件系统存储

### UI 组件
- **原生 Blazor 组件**: 基础 UI 组件
- **Bootstrap**: CSS 框架 (已集成)
- **自定义组件**: 业务特定的 UI 组件

## 🔧 开发历程和问题解决

### 主要里程碑

#### ✅ 第一阶段：基础架构搭建
- 创建 MAUI + Blazor Hybrid 项目
- 实现基本的页面导航
- 集成后端 API 服务

#### ✅ 第二阶段：问题诊断和修复
- **发现问题**: Blazor 事件绑定完全失效
- **深度调试**: 创建多种测试组件进行诊断
- **根因分析**: MudBlazor 依赖冲突导致运行时异常

#### ✅ 第三阶段：架构重构
- **清理依赖**: 移除有问题的 MudBlazor 包
- **简化配置**: 恢复到稳定的 Blazor 配置
- **修复导航**: 实现正确的 NavigationPage 架构

#### ✅ 第四阶段：代码整合
- **业务逻辑迁移**: 完整迁移 Models、Services、ViewModels
- **组件架构完善**: 迁移所有 Blazor 组件和布局
- **文档更新**: 反映当前项目状态

### 解决的关键技术问题

#### 🔴 **Blazor 事件绑定失效**
- **问题**: `@onclick` 等事件完全无响应
- **原因**: MudBlazor 包依赖冲突导致 Blazor 运行时异常
- **解决**: 移除 MudBlazor，使用原生 Blazor 组件

#### 🔴 **页面导航错误**
- **问题**: `PushAsync is not supported, please use a NavigationPage`
- **原因**: 未正确配置 NavigationPage 架构
- **解决**: 在 App.xaml.cs 中使用 NavigationPage 包装主页面

#### 🔴 **内联 JavaScript 错误**
- **问题**: 组件内 `<script>` 标签导致未处理错误
- **原因**: Blazor WebView 安全策略限制
- **解决**: 移除内联脚本，使用纯 Blazor 实现

## 📡 API 集成

### 当前状态
> **注意**: API 服务当前已迁移但暂时禁用，专注于修复基础 Blazor 功能。

### 配置 API 端点

在 `MauiProgram.cs` 中配置 API 基础地址（当前已注释）：

```csharp
// 重新启用时取消注释
// builder.Services.AddHttpClient("EduSynapseAPI", client =>
// {
//     client.BaseAddress = new Uri("http://localhost:8000/");
//     client.DefaultRequestHeaders.Add("Accept", "application/json");
// });

// 注册业务服务（当前已注释）
// builder.Services.AddScoped<IApiService, ApiService>();
// builder.Services.AddSingleton<IStateService, StateService>();
// builder.Services.AddSingleton<StorageService>();
```

### API 服务使用

```csharp
// 注入 API 服务
@inject IApiService ApiService

// 调用 API 示例
var healthCheck = await ApiService.CheckHealthAsync();
var plans = await ApiService.GetLearningPlansAsync();
var newPlan = await ApiService.GenerateLearningPlanAsync(request);
```

### 重新启用 API 服务

要重新启用完整的 API 功能：

1. **取消注释 MauiProgram.cs 中的服务注册**
2. **配置正确的 API 基础地址**
3. **测试与后端的连接**

## 🎯 核心功能

### 1. 学习计划管理
- 创建 AI 生成的学习计划
- 查看和编辑计划详情
- 计划状态管理

### 2. 学习进度跟踪
- 记录每日学习进度
- WWH 掌握度评估
- 学习时间统计

### 3. 智能仪表板
- 学习统计概览
- 最近计划展示
- 快速操作入口

### 4. 响应式设计
- 适配不同屏幕尺寸
- Material Design 风格
- 流畅的动画效果

## 🔧 开发指南

### 添加新页面

1. 在 `Components/Pages/` 创建 Razor 组件
2. 添加路由属性 `@page "/your-route"`
3. 在导航菜单中添加链接

### 创建新服务

1. 定义接口 `IYourService.cs`
2. 实现服务 `YourService.cs`
3. 在 `MauiProgram.cs` 中注册服务

### 添加新 ViewModel

1. 继承 `BaseViewModel`
2. 使用 `[ObservableProperty]` 标记属性
3. 使用 `[RelayCommand]` 标记命令方法

## 🎨 UI 组件

### MudBlazor 组件示例

```razor
<!-- 卡片 -->
<MudCard Elevation="2">
    <MudCardContent>
        <MudText Typo="Typo.h6">标题</MudText>
        <MudText Typo="Typo.body2">内容</MudText>
    </MudCardContent>
</MudCard>

<!-- 按钮 -->
<MudButton Variant="Variant.Filled" 
           Color="Color.Primary"
           StartIcon="@Icons.Material.Filled.Add"
           OnClick="@HandleClick">
    创建
</MudButton>

<!-- 表单 -->
<MudTextField @bind-Value="@model.Name" 
              Label="名称" 
              Required="true" />
```

### 自定义样式

在 `wwwroot/css/app.css` 中添加自定义样式：

```css
.custom-card {
    transition: transform 0.2s ease-in-out;
}

.custom-card:hover {
    transform: translateY(-2px);
}
```

## 🔍 调试和测试

### 测试组件

项目包含多个测试组件用于验证功能：

- **SafeTest.razor**: 安全的事件测试组件
- **MinimalTest.razor**: 最小化测试组件
- **StandardCounter.razor**: 标准计数器组件
- **BasicEventTest.razor**: 基础事件测试

### 调试技巧

1. **Blazor 开发者工具**: 在 Debug 模式下自动启用
2. **Visual Studio 调试**: 设置断点并按 F5
3. **日志输出**: 使用 `System.Diagnostics.Debug.WriteLine()`
4. **组件状态监控**: 查看测试组件的实时状态显示

### 验证功能正常

1. **启动应用**
2. **点击主页面的"🚀 开始使用 EduSynapse"按钮**
3. **进入 CleanTestPage**
4. **测试按钮点击是否有响应**
5. **观察计数器和状态更新**

### 常见问题和解决方案

1. **Blazor 事件无响应**
   - ✅ 已解决：移除 MudBlazor 依赖冲突
   - 如果仍有问题，检查 WebView2 运行时

2. **导航错误**
   - ✅ 已解决：使用 NavigationPage 架构
   - 确保在 App.xaml.cs 中正确配置

3. **未处理错误**
   - ✅ 已解决：移除内联 JavaScript
   - 避免在 Blazor 组件中使用 `<script>` 标签

4. **API 连接失败**
   - 检查后端服务是否运行 (http://localhost:8000)
   - 重新启用 MauiProgram.cs 中的服务注册

5. **MAUI 工作负载问题**
   ```bash
   dotnet workload repair
   dotnet workload install maui
   ```

6. **包还原失败**
   ```bash
   dotnet nuget locals all --clear
   dotnet restore
   ```

## 📱 平台支持

### 当前支持
- ✅ **Windows 10/11** (主要开发和测试平台)

### 未来计划
- 🔄 **macOS** (Mac Catalyst)
- 🔄 **Android**
- 🔄 **iOS**

## 🚀 部署

### Windows 部署

1. **发布应用**
   ```bash
   dotnet publish -c Release -f net9.0-windows10.0.19041.0
   ```

2. **创建安装包**
   - 使用 Visual Studio 发布向导
   - 或使用 MSIX 打包工具

### 配置要求

- **Windows 10** 版本 1809 或更高
- **.NET 9.0 Runtime**
- **WebView2 Runtime** (通常已预装)

## 📊 项目状态

### ✅ 已完成
- [x] 基础 MAUI + Blazor Hybrid 架构
- [x] Blazor 事件绑定问题修复
- [x] 页面导航架构
- [x] 完整业务逻辑迁移 (Models, Services, ViewModels)
- [x] Blazor 组件架构 (Layout, Pages, Shared)
- [x] 测试组件和调试工具
- [x] 项目文档更新

### 🔄 进行中
- [ ] API 服务重新集成
- [ ] MudBlazor 正确集成 (可选)
- [ ] 完整功能测试

### 📋 待办事项
- [ ] 用户界面优化
- [ ] 跨平台适配
- [ ] 性能优化
- [ ] 单元测试覆盖

## 🤝 开发团队指南

### 技术栈学习路径

对于只有 .NET Framework WinForms 经验的团队成员：

1. **第一步：.NET MAUI 基础**
   - 学习 XAML 语法和数据绑定
   - 理解 MAUI 的跨平台概念
   - 掌握页面导航和生命周期

2. **第二步：Blazor Hybrid**
   - 学习 Blazor 组件模型
   - 理解 Razor 语法和事件绑定
   - 掌握组件间通信

3. **第三步：现代 .NET 开发**
   - 依赖注入和服务模式
   - async/await 异步编程
   - MVVM 架构模式

### 推荐学习资源

- **Microsoft Learn**: .NET MAUI 官方教程
- **Blazor 文档**: 组件开发指南
- **本项目**: 实际代码示例和最佳实践

## 📄 许可证

本项目为个人学习项目，仅供学习和研究使用。

---

**最后更新**: 2025年7月7日
**版本**: Clean Version 1.0
**状态**: ✅ Blazor 事件问题已解决，基础功能正常
