"""
EduSynapse Backend - Working Version
确保能工作的版本
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from datetime import datetime
import uvicorn
import sys
import os

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 创建应用
app = FastAPI(
    title="EduSynapse AI Backend",
    description="基于AI的智能教学系统后端API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 基础路由
@app.get("/")
async def root():
    return {
        "message": "🎓 欢迎使用 EduSynapse AI智能教学系统 API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health",
        "ai_status": "/api/ai/status",
        "timestamp": datetime.now().isoformat(),
        "status": "running"
    }

@app.get("/health")
async def health():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

# 尝试加载AI路由
ai_routes_loaded = False
try:
    from app.api.ai_teaching import router as ai_teaching_router
    app.include_router(ai_teaching_router)
    ai_routes_loaded = True
except Exception as e:
    print(f"AI路由加载失败: {e}")

# 路由状态
@app.get("/api/routes/status")
async def routes_status():
    return {
        "ai_teaching_routes": ai_routes_loaded,
        "total_routes": len(app.routes),
        "message": "路由状态检查"
    }

if __name__ == "__main__":
    print("启动EduSynapse Backend...")
    print("访问: http://localhost:8000")
    print("文档: http://localhost:8000/docs")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        reload=False
    )
