@using EduSynapse.MAUI.ViewModels
@using MudBlazor
@using Microsoft.JSInterop
@using Microsoft.AspNetCore.Components
@inherits LayoutComponentBase
@inject IJSRuntime JSRuntime

<MudLayout>
    <MudAppBar Elevation="1">
        <MudIconButton Icon="@Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start" OnClick="@ToggleDrawer" />
        <MudSpacer />
        <MudText Typo="Typo.h6">EduSynapse</MudText>
        <MudSpacer />
        
        <!-- 简化的API连接状态指示器 -->
        <MudTooltip Text="@apiStatus">
            <MudIcon Icon="@(isApiConnected ? Icons.Material.Filled.CloudDone : Icons.Material.Filled.CloudOff)" 
                     Color="@(isApiConnected ? Color.Primary : Color.Secondary)" />
        </MudTooltip>
        
        <MudIconButton Icon="@Icons.Material.Filled.Refresh" Color="Color.Inherit" OnClick="@RefreshStatus" />
    </MudAppBar>

    <MudDrawer Open="_drawerOpen"
               OpenChanged="@((bool val) => _drawerOpen = val)"
               Elevation="1"
               Variant="@DrawerVariant.Responsive"
               ClipMode="DrawerClipMode.Always">
        <MudDrawerHeader>
            <MudText Typo="Typo.h6">EduSynapse</MudText>
            <MudText Typo="Typo.body2" Color="Color.Secondary">智能学习系统</MudText>
        </MudDrawerHeader>
        <MudNavMenu>
            <MudNavLink Href="/" Match="NavLinkMatch.All" Icon="@Icons.Material.Filled.Dashboard">
                仪表板
            </MudNavLink>
            <MudNavGroup Text="学习管理" Icon="@Icons.Material.Filled.School" Expanded="true">
                <MudNavLink Href="/learning-plan-creator" Icon="@Icons.Material.Filled.Add">
                    创建学习计划
                </MudNavLink>
                <MudNavLink Href="/learning-plan-list" Icon="@Icons.Material.Filled.Assignment">
                    我的学习计划
                </MudNavLink>
                <MudNavLink Href="/progress" Icon="@Icons.Material.Filled.Analytics">
                    学习进度
                </MudNavLink>
            </MudNavGroup>
            <MudNavGroup Text="数据分析" Icon="@Icons.Material.Filled.BarChart" Expanded="false">
                <MudNavLink Href="/learning-analytics" Icon="@Icons.Material.Filled.TrendingUp">
                    学习分析
                </MudNavLink>
                <MudNavLink Href="/learning-reminder" Icon="@Icons.Material.Filled.NotificationsActive">
                    智能提醒
                </MudNavLink>
                <MudNavLink Href="/data-verification" Icon="@Icons.Material.Filled.VerifiedUser">
                    数据验证
                </MudNavLink>
            </MudNavGroup>
            <MudNavGroup Text="系统设置" Icon="@Icons.Material.Filled.Settings">
                <MudNavLink Href="/settings" Icon="@Icons.Material.Filled.Tune">
                    系统配置
                </MudNavLink>
                <MudNavLink Href="/about" Icon="@Icons.Material.Filled.Info">
                    关于
                </MudNavLink>
            </MudNavGroup>
        </MudNavMenu>
    </MudDrawer>

    <MudMainContent>
        <MudContainer MaxWidth="MaxWidth.Large" Class="my-4">
            <!-- 页面内容 -->
            <div class="fade-in">
                @ChildContent
            </div>
        </MudContainer>
    </MudMainContent>
</MudLayout>

@code {
    [Parameter] public RenderFragment? ChildContent { get; set; }
    
    private bool _drawerOpen = true;
    private bool isApiConnected = false;
    private string apiStatus = "检查中...";
    
    protected override async Task OnInitializedAsync()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("SafeMainLayout: OnInitializedAsync started");
            
            // 安全地检查API状态，不依赖注入的ViewModel
            await CheckApiStatusSafely();
            
            System.Diagnostics.Debug.WriteLine("SafeMainLayout: OnInitializedAsync completed");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"SafeMainLayout OnInitializedAsync error: {ex}");
            apiStatus = "初始化失败";
        }
    }

    private void ToggleDrawer()
    {
        try
        {
            _drawerOpen = !_drawerOpen;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"SafeMainLayout ToggleDrawer error: {ex}");
        }
    }

    private async Task RefreshStatus()
    {
        try
        {
            await CheckApiStatusSafely();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"SafeMainLayout RefreshStatus error: {ex}");
            apiStatus = "刷新失败";
        }
    }

    private async Task CheckApiStatusSafely()
    {
        try
        {
            apiStatus = "检查中...";
            
            // 简单的连接测试，不依赖复杂的服务
            using var httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromSeconds(5);
            
            var response = await httpClient.GetAsync("http://localhost:8000/health");
            isApiConnected = response.IsSuccessStatusCode;
            apiStatus = isApiConnected ? "API 已连接" : "API 连接失败";
        }
        catch (Exception ex)
        {
            isApiConnected = false;
            apiStatus = $"连接错误: {ex.Message}";
            System.Diagnostics.Debug.WriteLine($"API check error: {ex}");
        }
    }
}
