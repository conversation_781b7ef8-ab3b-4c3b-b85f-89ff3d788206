@using Microsoft.AspNetCore.Components.Web

<div style="padding: 20px; font-family: Arial, sans-serif;">
    <h1 style="color: #2196F3;">EduSynapse Test App</h1>
    
    <div style="margin: 20px 0;">
        <h2>应用状态测试</h2>
        <p>当前时间: @currentTime</p>
        <button @onclick="UpdateTime" style="padding: 10px; background: #2196F3; color: white; border: none; border-radius: 4px;">
            更新时间
        </button>
    </div>
    
    <div style="margin: 20px 0;">
        <h2>错误测试</h2>
        <button @onclick="TestError" style="padding: 10px; background: #f44336; color: white; border: none; border-radius: 4px;">
            测试异常
        </button>
    </div>
    
    <div style="margin: 20px 0;">
        <h2>服务测试</h2>
        <p>服务状态: @serviceStatus</p>
        <button @onclick="TestServices" style="padding: 10px; background: #4CAF50; color: white; border: none; border-radius: 4px;">
            测试服务
        </button>
    </div>
    
    @if (!string.IsNullOrEmpty(errorMessage))
    {
        <div style="margin: 20px 0; padding: 15px; background: #ffebee; border: 1px solid #f44336; border-radius: 4px;">
            <h3 style="color: #f44336;">错误信息:</h3>
            <p style="color: #d32f2f;">@errorMessage</p>
        </div>
    }
</div>

@code {
    private string currentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
    private string serviceStatus = "未测试";
    private string errorMessage = "";

    protected override void OnInitialized()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("TestApp: OnInitialized started");
            currentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            System.Diagnostics.Debug.WriteLine("TestApp: OnInitialized completed");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"TestApp OnInitialized error: {ex}");
            errorMessage = $"初始化错误: {ex.Message}";
        }
    }

    private void UpdateTime()
    {
        try
        {
            currentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            errorMessage = "";
        }
        catch (Exception ex)
        {
            errorMessage = $"更新时间错误: {ex.Message}";
        }
    }

    private void TestError()
    {
        try
        {
            throw new InvalidOperationException("这是一个测试异常");
        }
        catch (Exception ex)
        {
            errorMessage = $"测试异常: {ex.Message}";
            System.Diagnostics.Debug.WriteLine($"TestApp TestError: {ex}");
        }
    }

    private void TestServices()
    {
        try
        {
            serviceStatus = "正在测试...";
            StateHasChanged();
            
            // 这里可以测试依赖注入的服务
            serviceStatus = "服务正常";
            errorMessage = "";
        }
        catch (Exception ex)
        {
            serviceStatus = "服务错误";
            errorMessage = $"服务测试错误: {ex.Message}";
            System.Diagnostics.Debug.WriteLine($"TestApp TestServices error: {ex}");
        }
    }
}
