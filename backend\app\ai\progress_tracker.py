"""
EduSynapse 五维进度跟踪系统
实现知识掌握度、实践完成度、时间投入、错误模式分析、能力发展曲线的智能跟踪
"""

import asyncio
import json
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import re
from collections import defaultdict, Counter

from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from sklearn.metrics.pairwise import cosine_similarity

from ..models.learning_models import (
    LearningStage, ProgressData, ErrorPattern, 
    AbilityLevel, LearningSession, MasteryScore
)
from ..utils.logger import get_logger

logger = get_logger(__name__)


class ErrorType(Enum):
    """错误类型枚举"""
    CONCEPTUAL = "conceptual"      # 概念理解错误
    PROCEDURAL = "procedural"      # 程序逻辑错误
    SYNTACTIC = "syntactic"        # 语法错误
    CARELESS = "careless"          # 粗心错误
    KNOWLEDGE_GAP = "knowledge_gap" # 知识缺口


class AbilityDimension(Enum):
    """能力维度枚举"""
    MEMORY = "memory"              # 记忆能力
    COMPREHENSION = "comprehension" # 理解能力
    APPLICATION = "application"     # 应用能力
    ANALYSIS = "analysis"          # 分析能力
    SYNTHESIS = "synthesis"        # 综合能力
    EVALUATION = "evaluation"      # 评价能力


@dataclass
class FiveDimensionMetrics:
    """五维进度指标"""
    knowledge_mastery: float       # 知识掌握度 (0-1)
    practice_completion: float     # 实践完成度 (0-1)
    time_investment: float         # 时间投入效率 (0-1)
    error_pattern_score: float     # 错误模式改善度 (0-1)
    ability_development: float     # 能力发展指数 (0-1)
    
    @property
    def overall_score(self) -> float:
        """综合评分"""
        weights = [0.25, 0.2, 0.15, 0.2, 0.2]  # 各维度权重
        scores = [
            self.knowledge_mastery,
            self.practice_completion, 
            self.time_investment,
            self.error_pattern_score,
            self.ability_development
        ]
        return sum(w * s for w, s in zip(weights, scores))


class KnowledgeMasteryCalculator:
    """知识掌握度计算器"""
    
    def __init__(self):
        self.wwh_weights = {
            LearningStage.WHAT: 0.3,   # What阶段权重
            LearningStage.WHY: 0.3,    # Why阶段权重  
            LearningStage.HOW: 0.4     # How阶段权重
        }
    
    def calculate_mastery(
        self, 
        wwh_scores: Dict[LearningStage, float],
        concept_assessments: List[Dict[str, Any]],
        time_decay_factor: float = 0.95
    ) -> float:
        """
        计算知识掌握度
        
        Args:
            wwh_scores: WWH各阶段得分
            concept_assessments: 概念评估记录
            time_decay_factor: 时间衰减因子
        """
        
        # 1. WWH阶段掌握度
        wwh_mastery = sum(
            self.wwh_weights[stage] * score 
            for stage, score in wwh_scores.items()
        )
        
        # 2. 概念理解深度
        concept_depth = self._calculate_concept_depth(concept_assessments)
        
        # 3. 时间衰减调整
        time_adjusted_score = self._apply_time_decay(
            wwh_mastery, concept_assessments, time_decay_factor
        )
        
        # 4. 综合计算
        mastery_score = (
            0.5 * time_adjusted_score +
            0.3 * concept_depth +
            0.2 * self._calculate_retention_rate(concept_assessments)
        )
        
        return min(1.0, max(0.0, mastery_score))
    
    def _calculate_concept_depth(self, assessments: List[Dict[str, Any]]) -> float:
        """计算概念理解深度"""
        if not assessments:
            return 0.0
        
        depth_scores = []
        for assessment in assessments:
            # 基于回答质量评估理解深度
            answer_quality = assessment.get('answer_quality', 0.5)
            explanation_depth = assessment.get('explanation_depth', 0.5)
            connection_ability = assessment.get('connection_ability', 0.5)
            
            depth = (answer_quality * 0.4 + 
                    explanation_depth * 0.4 + 
                    connection_ability * 0.2)
            depth_scores.append(depth)
        
        return np.mean(depth_scores)
    
    def _apply_time_decay(
        self, 
        base_score: float, 
        assessments: List[Dict[str, Any]], 
        decay_factor: float
    ) -> float:
        """应用时间衰减"""
        if not assessments:
            return base_score
        
        # 计算最近学习时间
        latest_time = max(
            datetime.fromisoformat(a['timestamp']) 
            for a in assessments
        )
        
        days_since = (datetime.now() - latest_time).days
        decay_multiplier = decay_factor ** days_since
        
        return base_score * decay_multiplier
    
    def _calculate_retention_rate(self, assessments: List[Dict[str, Any]]) -> float:
        """计算知识保持率"""
        if len(assessments) < 2:
            return 0.5
        
        # 按时间排序
        sorted_assessments = sorted(
            assessments, 
            key=lambda x: datetime.fromisoformat(x['timestamp'])
        )
        
        # 计算得分趋势
        scores = [a.get('score', 0.5) for a in sorted_assessments]
        if len(scores) < 2:
            return 0.5
        
        # 线性回归计算趋势
        x = np.arange(len(scores))
        slope = np.polyfit(x, scores, 1)[0]
        
        # 转换为保持率 (斜率越大，保持率越高)
        retention_rate = 0.5 + np.tanh(slope * 5) * 0.5
        return max(0.0, min(1.0, retention_rate))


class PracticeCompletionAnalyzer:
    """实践完成度分析器"""
    
    def calculate_completion_rate(
        self,
        code_submissions: List[Dict[str, Any]],
        practice_tasks: List[Dict[str, Any]]
    ) -> float:
        """计算实践完成度"""
        
        if not practice_tasks:
            return 0.0
        
        # 1. 任务完成率
        completed_tasks = len([
            task for task in practice_tasks 
            if task.get('status') == 'completed'
        ])
        task_completion_rate = completed_tasks / len(practice_tasks)
        
        # 2. 代码质量评分
        code_quality_score = self._analyze_code_quality(code_submissions)
        
        # 3. 实践深度评估
        practice_depth = self._assess_practice_depth(code_submissions)
        
        # 综合评分
        completion_score = (
            0.4 * task_completion_rate +
            0.35 * code_quality_score +
            0.25 * practice_depth
        )
        
        return min(1.0, max(0.0, completion_score))
    
    def _analyze_code_quality(self, submissions: List[Dict[str, Any]]) -> float:
        """分析代码质量"""
        if not submissions:
            return 0.0
        
        quality_scores = []
        for submission in submissions:
            code = submission.get('code', '')
            
            # 基础质量指标
            has_comments = len(re.findall(r'#.*', code)) > 0
            has_functions = 'def ' in code
            has_error_handling = 'try:' in code or 'except:' in code
            proper_naming = self._check_naming_convention(code)
            
            # 计算质量分数
            quality_score = (
                0.2 * has_comments +
                0.3 * has_functions +
                0.2 * has_error_handling +
                0.3 * proper_naming
            )
            
            quality_scores.append(quality_score)
        
        return np.mean(quality_scores)
    
    def _check_naming_convention(self, code: str) -> float:
        """检查命名规范"""
        # 简化的命名规范检查
        variable_names = re.findall(r'\b[a-z_][a-z0-9_]*\b', code)
        if not variable_names:
            return 0.5
        
        # 检查是否使用有意义的变量名
        meaningful_names = [
            name for name in variable_names 
            if len(name) > 2 and name not in ['tmp', 'temp', 'var', 'x', 'y', 'z']
        ]
        
        return len(meaningful_names) / len(variable_names)
    
    def _assess_practice_depth(self, submissions: List[Dict[str, Any]]) -> float:
        """评估实践深度"""
        if not submissions:
            return 0.0
        
        depth_indicators = []
        for submission in submissions:
            code = submission.get('code', '')
            
            # 深度指标
            has_classes = 'class ' in code
            has_imports = 'import ' in code or 'from ' in code
            has_loops = 'for ' in code or 'while ' in code
            has_conditionals = 'if ' in code
            line_count = len(code.split('\n'))
            
            # 计算深度分数
            depth_score = (
                0.2 * has_classes +
                0.15 * has_imports +
                0.2 * has_loops +
                0.15 * has_conditionals +
                0.3 * min(1.0, line_count / 50)  # 代码长度指标
            )
            
            depth_indicators.append(depth_score)
        
        return np.mean(depth_indicators)


class ErrorPatternAnalyzer:
    """错误模式分析器"""
    
    def __init__(self):
        self.error_patterns = defaultdict(list)
        self.improvement_threshold = 0.7
    
    def analyze_error_patterns(
        self,
        error_history: List[Dict[str, Any]]
    ) -> Tuple[float, List[ErrorPattern]]:
        """
        分析错误模式
        
        Returns:
            Tuple[改善度评分, 错误模式列表]
        """
        
        if not error_history:
            return 1.0, []
        
        # 1. 错误分类
        categorized_errors = self._categorize_errors(error_history)
        
        # 2. 模式识别
        patterns = self._identify_patterns(categorized_errors)
        
        # 3. 改善度计算
        improvement_score = self._calculate_improvement(error_history)
        
        return improvement_score, patterns
    
    def _categorize_errors(self, errors: List[Dict[str, Any]]) -> Dict[ErrorType, List]:
        """错误分类"""
        categorized = defaultdict(list)
        
        for error in errors:
            error_type = self._classify_error(error)
            categorized[error_type].append(error)
        
        return categorized
    
    def _classify_error(self, error: Dict[str, Any]) -> ErrorType:
        """错误分类"""
        error_msg = error.get('message', '').lower()
        code = error.get('code', '').lower()
        
        # 语法错误
        if any(keyword in error_msg for keyword in ['syntax', 'invalid syntax', 'unexpected']):
            return ErrorType.SYNTACTIC
        
        # 概念错误
        if any(keyword in error_msg for keyword in ['logic', 'algorithm', 'approach']):
            return ErrorType.CONCEPTUAL
        
        # 程序错误
        if any(keyword in error_msg for keyword in ['runtime', 'exception', 'error']):
            return ErrorType.PROCEDURAL
        
        # 知识缺口
        if any(keyword in error_msg for keyword in ['undefined', 'not found', 'unknown']):
            return ErrorType.KNOWLEDGE_GAP
        
        return ErrorType.CARELESS
    
    def _identify_patterns(self, categorized_errors: Dict[ErrorType, List]) -> List[ErrorPattern]:
        """识别错误模式"""
        patterns = []
        
        for error_type, errors in categorized_errors.items():
            if len(errors) >= 3:  # 至少3次相同类型错误才算模式
                pattern = ErrorPattern(
                    type=error_type,
                    frequency=len(errors),
                    description=f"频繁出现{error_type.value}类型错误",
                    examples=errors[:3],  # 取前3个例子
                    severity=self._calculate_severity(errors)
                )
                patterns.append(pattern)
        
        return patterns
    
    def _calculate_severity(self, errors: List[Dict[str, Any]]) -> float:
        """计算错误严重程度"""
        # 基于错误频率和时间分布计算严重程度
        if not errors:
            return 0.0
        
        # 时间集中度 (错误是否集中在短时间内)
        timestamps = [datetime.fromisoformat(e['timestamp']) for e in errors]
        time_span = (max(timestamps) - min(timestamps)).total_seconds()
        
        if time_span == 0:
            concentration = 1.0
        else:
            concentration = min(1.0, len(errors) / (time_span / 3600))  # 每小时错误数
        
        # 频率严重程度
        frequency_severity = min(1.0, len(errors) / 10)
        
        return (concentration * 0.6 + frequency_severity * 0.4)
    
    def _calculate_improvement(self, error_history: List[Dict[str, Any]]) -> float:
        """计算错误改善度"""
        if len(error_history) < 2:
            return 0.5
        
        # 按时间排序
        sorted_errors = sorted(
            error_history,
            key=lambda x: datetime.fromisoformat(x['timestamp'])
        )
        
        # 分析错误频率趋势
        recent_errors = sorted_errors[-10:]  # 最近10个错误
        early_errors = sorted_errors[:10]    # 最早10个错误
        
        if not early_errors:
            return 0.5
        
        # 计算错误率变化
        recent_error_rate = len(recent_errors) / 10
        early_error_rate = len(early_errors) / 10
        
        if early_error_rate == 0:
            return 1.0
        
        improvement_ratio = 1 - (recent_error_rate / early_error_rate)
        return max(0.0, min(1.0, improvement_ratio))


class AbilityDevelopmentTracker:
    """能力发展跟踪器"""
    
    def __init__(self):
        self.ability_weights = {
            AbilityDimension.MEMORY: 0.15,
            AbilityDimension.COMPREHENSION: 0.25,
            AbilityDimension.APPLICATION: 0.25,
            AbilityDimension.ANALYSIS: 0.15,
            AbilityDimension.SYNTHESIS: 0.1,
            AbilityDimension.EVALUATION: 0.1
        }
    
    def track_ability_development(
        self,
        learning_sessions: List[LearningSession],
        assessment_results: List[Dict[str, Any]]
    ) -> Tuple[float, Dict[AbilityDimension, float]]:
        """
        跟踪能力发展
        
        Returns:
            Tuple[综合能力发展指数, 各维度能力评分]
        """
        
        # 计算各维度能力
        ability_scores = {}
        for dimension in AbilityDimension:
            score = self._calculate_dimension_score(
                dimension, learning_sessions, assessment_results
            )
            ability_scores[dimension] = score
        
        # 计算综合发展指数
        development_index = sum(
            self.ability_weights[dim] * score
            for dim, score in ability_scores.items()
        )
        
        return development_index, ability_scores
    
    def _calculate_dimension_score(
        self,
        dimension: AbilityDimension,
        sessions: List[LearningSession],
        assessments: List[Dict[str, Any]]
    ) -> float:
        """计算特定维度的能力评分"""
        
        if dimension == AbilityDimension.MEMORY:
            return self._assess_memory_ability(sessions, assessments)
        elif dimension == AbilityDimension.COMPREHENSION:
            return self._assess_comprehension_ability(sessions, assessments)
        elif dimension == AbilityDimension.APPLICATION:
            return self._assess_application_ability(sessions, assessments)
        elif dimension == AbilityDimension.ANALYSIS:
            return self._assess_analysis_ability(sessions, assessments)
        elif dimension == AbilityDimension.SYNTHESIS:
            return self._assess_synthesis_ability(sessions, assessments)
        elif dimension == AbilityDimension.EVALUATION:
            return self._assess_evaluation_ability(sessions, assessments)
        
        return 0.5
    
    def _assess_memory_ability(self, sessions: List[LearningSession], assessments: List[Dict[str, Any]]) -> float:
        """评估记忆能力"""
        # 基于知识点回忆准确率
        recall_scores = [a.get('recall_accuracy', 0.5) for a in assessments]
        return np.mean(recall_scores) if recall_scores else 0.5
    
    def _assess_comprehension_ability(self, sessions: List[LearningSession], assessments: List[Dict[str, Any]]) -> float:
        """评估理解能力"""
        # 基于概念解释质量
        comprehension_scores = [a.get('explanation_quality', 0.5) for a in assessments]
        return np.mean(comprehension_scores) if comprehension_scores else 0.5
    
    def _assess_application_ability(self, sessions: List[LearningSession], assessments: List[Dict[str, Any]]) -> float:
        """评估应用能力"""
        # 基于实践任务完成质量
        application_scores = [a.get('application_score', 0.5) for a in assessments]
        return np.mean(application_scores) if application_scores else 0.5
    
    def _assess_analysis_ability(self, sessions: List[LearningSession], assessments: List[Dict[str, Any]]) -> float:
        """评估分析能力"""
        # 基于问题分解和推理能力
        analysis_scores = [a.get('analysis_depth', 0.5) for a in assessments]
        return np.mean(analysis_scores) if analysis_scores else 0.5
    
    def _assess_synthesis_ability(self, sessions: List[LearningSession], assessments: List[Dict[str, Any]]) -> float:
        """评估综合能力"""
        # 基于知识整合和创新能力
        synthesis_scores = [a.get('synthesis_creativity', 0.5) for a in assessments]
        return np.mean(synthesis_scores) if synthesis_scores else 0.5
    
    def _assess_evaluation_ability(self, sessions: List[LearningSession], assessments: List[Dict[str, Any]]) -> float:
        """评估评价能力"""
        # 基于批判性思维和判断能力
        evaluation_scores = [a.get('critical_thinking', 0.5) for a in assessments]
        return np.mean(evaluation_scores) if evaluation_scores else 0.5


class FiveDimensionProgressTracker:
    """五维进度跟踪系统主类"""
    
    def __init__(self):
        self.mastery_calculator = KnowledgeMasteryCalculator()
        self.practice_analyzer = PracticeCompletionAnalyzer()
        self.error_analyzer = ErrorPatternAnalyzer()
        self.ability_tracker = AbilityDevelopmentTracker()
    
    async def calculate_five_dimension_metrics(
        self,
        student_id: str,
        learning_data: Dict[str, Any]
    ) -> FiveDimensionMetrics:
        """计算五维进度指标"""
        
        logger.info(f"Calculating five-dimension metrics for student: {student_id}")
        
        try:
            # 1. 知识掌握度
            knowledge_mastery = self.mastery_calculator.calculate_mastery(
                learning_data.get('wwh_scores', {}),
                learning_data.get('concept_assessments', [])
            )
            
            # 2. 实践完成度
            practice_completion = self.practice_analyzer.calculate_completion_rate(
                learning_data.get('code_submissions', []),
                learning_data.get('practice_tasks', [])
            )
            
            # 3. 时间投入效率
            time_investment = self._calculate_time_efficiency(
                learning_data.get('learning_sessions', [])
            )
            
            # 4. 错误模式改善度
            error_improvement, error_patterns = self.error_analyzer.analyze_error_patterns(
                learning_data.get('error_history', [])
            )
            
            # 5. 能力发展指数
            ability_development, ability_scores = self.ability_tracker.track_ability_development(
                learning_data.get('learning_sessions', []),
                learning_data.get('assessment_results', [])
            )
            
            metrics = FiveDimensionMetrics(
                knowledge_mastery=knowledge_mastery,
                practice_completion=practice_completion,
                time_investment=time_investment,
                error_pattern_score=error_improvement,
                ability_development=ability_development
            )
            
            logger.info(f"Five-dimension metrics calculated: {metrics.overall_score:.3f}")
            return metrics
            
        except Exception as e:
            logger.error(f"Failed to calculate five-dimension metrics: {e}")
            raise
    
    def _calculate_time_efficiency(self, sessions: List[LearningSession]) -> float:
        """计算时间投入效率"""
        if not sessions:
            return 0.5
        
        # 计算学习效率指标
        total_time = sum(s.duration.total_seconds() for s in sessions)
        effective_sessions = len([s for s in sessions if s.effectiveness_score > 0.6])
        
        if len(sessions) == 0:
            return 0.5
        
        # 效率 = 有效会话比例 * 平均效果评分
        efficiency = (effective_sessions / len(sessions)) * np.mean([s.effectiveness_score for s in sessions])
        
        return min(1.0, max(0.0, efficiency))
    
    async def generate_progress_insights(
        self,
        metrics: FiveDimensionMetrics,
        historical_metrics: List[FiveDimensionMetrics]
    ) -> Dict[str, Any]:
        """生成进度洞察"""
        
        insights = {
            "current_performance": {
                "overall_score": metrics.overall_score,
                "strengths": self._identify_strengths(metrics),
                "weaknesses": self._identify_weaknesses(metrics)
            },
            "progress_trend": self._analyze_progress_trend(historical_metrics),
            "recommendations": self._generate_recommendations(metrics),
            "next_focus_areas": self._suggest_focus_areas(metrics)
        }
        
        return insights
    
    def _identify_strengths(self, metrics: FiveDimensionMetrics) -> List[str]:
        """识别优势领域"""
        strengths = []
        threshold = 0.7
        
        if metrics.knowledge_mastery >= threshold:
            strengths.append("知识掌握度优秀")
        if metrics.practice_completion >= threshold:
            strengths.append("实践完成度良好")
        if metrics.time_investment >= threshold:
            strengths.append("时间利用效率高")
        if metrics.error_pattern_score >= threshold:
            strengths.append("错误改善明显")
        if metrics.ability_development >= threshold:
            strengths.append("能力发展良好")
        
        return strengths
    
    def _identify_weaknesses(self, metrics: FiveDimensionMetrics) -> List[str]:
        """识别薄弱环节"""
        weaknesses = []
        threshold = 0.5
        
        if metrics.knowledge_mastery < threshold:
            weaknesses.append("知识掌握需要加强")
        if metrics.practice_completion < threshold:
            weaknesses.append("实践练习不足")
        if metrics.time_investment < threshold:
            weaknesses.append("时间利用效率有待提高")
        if metrics.error_pattern_score < threshold:
            weaknesses.append("错误模式需要改善")
        if metrics.ability_development < threshold:
            weaknesses.append("能力发展需要关注")
        
        return weaknesses
    
    def _analyze_progress_trend(self, historical_metrics: List[FiveDimensionMetrics]) -> Dict[str, Any]:
        """分析进度趋势"""
        if len(historical_metrics) < 2:
            return {"trend": "insufficient_data"}
        
        # 计算趋势
        recent_scores = [m.overall_score for m in historical_metrics[-5:]]
        trend_slope = np.polyfit(range(len(recent_scores)), recent_scores, 1)[0]
        
        if trend_slope > 0.02:
            trend = "improving"
        elif trend_slope < -0.02:
            trend = "declining"
        else:
            trend = "stable"
        
        return {
            "trend": trend,
            "slope": trend_slope,
            "recent_average": np.mean(recent_scores)
        }
    
    def _generate_recommendations(self, metrics: FiveDimensionMetrics) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        if metrics.knowledge_mastery < 0.6:
            recommendations.append("建议增加概念复习时间，重点关注WWH框架的理解")
        
        if metrics.practice_completion < 0.6:
            recommendations.append("建议增加编程练习，完成更多实践项目")
        
        if metrics.error_pattern_score < 0.6:
            recommendations.append("建议分析错误模式，针对性地改善常见错误")
        
        if metrics.ability_development < 0.6:
            recommendations.append("建议多做综合性练习，提升分析和综合能力")
        
        return recommendations
    
    def _suggest_focus_areas(self, metrics: FiveDimensionMetrics) -> List[str]:
        """建议重点关注领域"""
        scores = {
            "knowledge_mastery": metrics.knowledge_mastery,
            "practice_completion": metrics.practice_completion,
            "time_investment": metrics.time_investment,
            "error_pattern_score": metrics.error_pattern_score,
            "ability_development": metrics.ability_development
        }
        
        # 找出得分最低的两个维度
        sorted_scores = sorted(scores.items(), key=lambda x: x[1])
        focus_areas = [area for area, score in sorted_scores[:2]]
        
        return focus_areas
