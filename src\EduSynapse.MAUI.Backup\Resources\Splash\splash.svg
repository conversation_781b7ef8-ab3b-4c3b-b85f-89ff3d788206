<?xml version="1.0" encoding="UTF-8"?>
<svg width="456" height="456" viewBox="0 0 456 456" fill="none" xmlns="http://www.w3.org/2000/svg">
    <!-- Background -->
    <rect width="456" height="456" fill="#512BD4"/>
    
    <!-- Main logo area -->
    <g transform="translate(128, 128)">
        <!-- Central brain/learning icon -->
        <circle cx="100" cy="100" r="80" fill="white" opacity="0.1"/>
        <circle cx="100" cy="100" r="60" fill="white" opacity="0.2"/>
        <circle cx="100" cy="100" r="40" fill="white" opacity="0.3"/>
        
        <!-- Neural network nodes -->
        <circle cx="100" cy="60" r="8" fill="white"/>
        <circle cx="60" cy="100" r="6" fill="white"/>
        <circle cx="140" cy="100" r="6" fill="white"/>
        <circle cx="100" cy="140" r="8" fill="white"/>
        <circle cx="80" cy="80" r="4" fill="white" opacity="0.8"/>
        <circle cx="120" cy="80" r="4" fill="white" opacity="0.8"/>
        <circle cx="80" cy="120" r="4" fill="white" opacity="0.8"/>
        <circle cx="120" cy="120" r="4" fill="white" opacity="0.8"/>
        
        <!-- Connection lines -->
        <line x1="100" y1="60" x2="60" y2="100" stroke="white" stroke-width="2" opacity="0.6"/>
        <line x1="100" y1="60" x2="140" y2="100" stroke="white" stroke-width="2" opacity="0.6"/>
        <line x1="60" y1="100" x2="100" y2="140" stroke="white" stroke-width="2" opacity="0.6"/>
        <line x1="140" y1="100" x2="100" y2="140" stroke="white" stroke-width="2" opacity="0.6"/>
        <line x1="80" y1="80" x2="120" y2="120" stroke="white" stroke-width="1.5" opacity="0.4"/>
        <line x1="120" y1="80" x2="80" y2="120" stroke="white" stroke-width="1.5" opacity="0.4"/>
        
        <!-- Animated pulse rings -->
        <circle cx="100" cy="100" r="20" fill="none" stroke="white" stroke-width="2" opacity="0.5">
            <animate attributeName="r" values="20;35;20" dur="2s" repeatCount="indefinite"/>
            <animate attributeName="opacity" values="0.5;0.1;0.5" dur="2s" repeatCount="indefinite"/>
        </circle>
        <circle cx="100" cy="100" r="25" fill="none" stroke="white" stroke-width="1" opacity="0.3">
            <animate attributeName="r" values="25;40;25" dur="2.5s" repeatCount="indefinite"/>
            <animate attributeName="opacity" values="0.3;0.05;0.3" dur="2.5s" repeatCount="indefinite"/>
        </circle>
    </g>
    
    <!-- App name -->
    <text x="228" y="380" font-family="Arial, sans-serif" font-size="32" font-weight="bold" text-anchor="middle" fill="white">EduSynapse</text>
    <text x="228" y="410" font-family="Arial, sans-serif" font-size="16" text-anchor="middle" fill="white" opacity="0.8">智能学习系统</text>
</svg>
