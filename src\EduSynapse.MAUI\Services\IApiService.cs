using EduSynapse.MAUI.Models;

namespace EduSynapse.MAUI.Services;

/// <summary>
/// API服务接口
/// </summary>
public interface IApiService
{
    /// <summary>
    /// 检查API服务健康状态
    /// </summary>
    Task<HealthCheckResponse?> CheckHealthAsync();

    /// <summary>
    /// 生成学习计划
    /// </summary>
    Task<LearningPlan?> GenerateLearningPlanAsync(PlanGenerationRequest request);

    /// <summary>
    /// 获取学习计划列表
    /// </summary>
    Task<LearningPlansListResponse?> GetLearningPlansAsync(string? statusFilter = null, int limit = 10, int offset = 0);

    /// <summary>
    /// 获取学习计划详情
    /// </summary>
    Task<LearningPlan?> GetLearningPlanAsync(int planId);

    /// <summary>
    /// 更新学习计划
    /// </summary>
    Task<LearningPlan?> UpdateLearningPlanAsync(int planId, object updateData);

    /// <summary>
    /// 删除学习计划
    /// </summary>
    Task<bool> DeleteLearningPlanAsync(int planId);

    /// <summary>
    /// 记录学习进度
    /// </summary>
    Task<LearningProgress?> RecordProgressAsync(int planId, ProgressRecordRequest request);

    /// <summary>
    /// 获取学习进度列表
    /// </summary>
    Task<List<LearningProgress>?> GetProgressListAsync(int planId);

    /// <summary>
    /// 测试API连接
    /// </summary>
    Task<bool> TestConnectionAsync();

    /// <summary>
    /// 获取学习进度统计
    /// </summary>
    Task<ProgressStatsResponse?> GetProgressStatsAsync(int planId);

    /// <summary>
    /// 获取学习进度日历
    /// </summary>
    Task<ProgressCalendarResponse?> GetProgressCalendarAsync(int planId, int year, int month);

    /// <summary>
    /// 获取学习进度分析
    /// </summary>
    Task<ProgressAnalysis?> GetProgressAnalysisAsync(int planId);

    /// <summary>
    /// 获取特定天数的进度详情
    /// </summary>
    Task<LearningProgress?> GetDailyProgressAsync(int planId, int dayNumber);
}
