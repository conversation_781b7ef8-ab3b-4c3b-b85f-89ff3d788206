using Microsoft.UI.Xaml;

namespace EduSynapse.MAUI.WinUI;

public class Program
{
    [STAThread]
    public static void Main(string[] args)
    {
        global::WinRT.ComWrappersSupport.InitializeComWrappers();
        global::Microsoft.UI.Xaml.Application.Start((p) => new App());
    }
}

public partial class App : MauiWinUIApplication
{
    public App()
    {
        // InitializeComponent() 不需要在这里调用，因为这是 WinUI 应用
    }

    protected override MauiApp CreateMauiApp() => MauiProgram.CreateMauiApp();
}
