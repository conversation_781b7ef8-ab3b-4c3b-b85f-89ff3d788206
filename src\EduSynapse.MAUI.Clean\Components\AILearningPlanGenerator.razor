@page "/ai-plan-generator"
@using EduSynapse.MAUI.Services
@using EduSynapse.MAUI.Models
@inject AITeacherService AITeacherService
@inject StorageService StorageService
@inject NavigationManager Navigation

<div class="container-fluid p-4">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-3">🤖 AI智能学习计划生成器</h2>
            <p class="text-muted">基于先进的AI技术，为您生成个性化的WWH框架学习计划</p>
        </div>
    </div>

    @if (isGenerating)
    {
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center p-5">
                        <div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem;" role="status">
                            <span class="visually-hidden">生成中...</span>
                        </div>
                        <h5>🧠 AI正在为您生成学习计划...</h5>
                        <p class="text-muted">这可能需要几秒钟时间，请耐心等待</p>
                        <div class="progress mt-3" style="height: 8px;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 style="width: @generationProgress%"></div>
                        </div>
                        <small class="text-muted">@generationStatus</small>
                    </div>
                </div>
            </div>
        </div>
    }
    else if (generatedPlan != null)
    {
        <!-- 生成结果展示 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">✅ AI学习计划生成成功！</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h6>📚 @generatedPlan.Name</h6>
                                <p class="text-muted">@generatedPlan.Description</p>
                                <div class="row">
                                    <div class="col-sm-6">
                                        <small class="text-muted">学习周期：</small>
                                        <strong>@generatedPlan.DurationDays 天</strong>
                                    </div>
                                    <div class="col-sm-6">
                                        <small class="text-muted">每日时间：</small>
                                        <strong>@generatedPlan.DailyHours 小时</strong>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <button class="btn btn-primary me-2" @onclick="SaveGeneratedPlan">
                                    💾 保存计划
                                </button>
                                <button class="btn btn-outline-secondary" @onclick="RegenerateWithAdjustments">
                                    🔄 重新生成
                                </button>
                            </div>
                        </div>
                        
                        @if (!string.IsNullOrEmpty(generatedPlan.AIContent))
                        {
                            <div class="mt-3">
                                <h6>🤖 AI生成内容预览：</h6>
                                <div class="bg-light p-3 rounded">
                                    <pre style="white-space: pre-wrap; font-size: 0.9rem;">@generatedPlan.AIContent.Substring(0, Math.Min(500, generatedPlan.AIContent.Length))@(generatedPlan.AIContent.Length > 500 ? "..." : "")</pre>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    }
    else
    {
        <!-- 学习计划生成表单 -->
        <div class="row">
            <div class="col-lg-8 col-md-10 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">🎯 告诉AI您的学习需求</h5>
                    </div>
                    <div class="card-body">
                        <form @onsubmit="GenerateAIPlan" @onsubmit:preventDefault="true">
                            <!-- 学习主题 -->
                            <div class="mb-4">
                                <label class="form-label">
                                    <strong>📚 学习主题 *</strong>
                                </label>
                                <input @bind="learningTopic" type="text" class="form-control form-control-lg" 
                                       placeholder="例如：机器学习、Python编程、数据结构..." required />
                                <div class="form-text">请输入您想要学习的具体主题或技能</div>
                            </div>

                            <!-- 学习偏好设置 -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <label class="form-label"><strong>🎚️ 难度级别</strong></label>
                                    <select @bind="preferences.DifficultyLevel" class="form-select">
                                        <option value="beginner">🌱 初学者</option>
                                        <option value="intermediate">🌿 中级</option>
                                        <option value="advanced">🌳 高级</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label"><strong>⏰ 每日学习时间</strong></label>
                                    <select @bind="preferences.DailyHours" class="form-select">
                                        <option value="0.5">30分钟</option>
                                        <option value="1">1小时</option>
                                        <option value="2">2小时</option>
                                        <option value="3">3小时</option>
                                        <option value="4">4小时以上</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <label class="form-label"><strong>🎨 学习风格</strong></label>
                                    <select @bind="preferences.LearningStyle" class="form-select">
                                        <option value="visual">👁️ 视觉型（图表、图像）</option>
                                        <option value="auditory">👂 听觉型（讲解、讨论）</option>
                                        <option value="kinesthetic">✋ 动手型（实践、操作）</option>
                                        <option value="balanced">⚖️ 平衡型（综合方式）</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label"><strong>🤖 AI教师类型</strong></label>
                                    <select @bind="preferences.PreferredTeacherType" class="form-select">
                                        <option value="socratic">🤔 苏格拉底式（启发思考）</option>
                                        <option value="case_driven">💼 案例驱动（实战导向）</option>
                                        <option value="gamified">🎮 游戏化（趣味学习）</option>
                                    </select>
                                </div>
                            </div>

                            <!-- 学习重点 -->
                            <div class="mb-4">
                                <label class="form-label"><strong>🎯 学习重点</strong></label>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" @bind="focusTheory" id="focusTheory">
                                            <label class="form-check-label" for="focusTheory">📖 理论基础</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" @bind="focusPractice" id="focusPractice">
                                            <label class="form-check-label" for="focusPractice">💻 实践练习</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" @bind="focusProjects" id="focusProjects">
                                            <label class="form-check-label" for="focusProjects">🚀 项目实战</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" @bind="focusApplications" id="focusApplications">
                                            <label class="form-check-label" for="focusApplications">🌍 实际应用</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 学习周期 -->
                            <div class="mb-4">
                                <label class="form-label"><strong>📅 学习周期</strong></label>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="duration" value="7" @onchange="() => durationDays = 7" id="duration7">
                                            <label class="form-check-label" for="duration7">⚡ 7天速成</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="duration" value="14" @onchange="() => durationDays = 14" id="duration14" checked>
                                            <label class="form-check-label" for="duration14">🎯 14天标准</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="duration" value="30" @onchange="() => durationDays = 30" id="duration30">
                                            <label class="form-check-label" for="duration30">🏆 30天深度</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 生成按钮 -->
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg" disabled="@(isGenerating || string.IsNullOrWhiteSpace(learningTopic))">
                                    @if (isGenerating)
                                    {
                                        <span class="spinner-border spinner-border-sm me-2"></span>
                                        <span>🧠 AI正在思考...</span>
                                    }
                                    else
                                    {
                                        <span>🚀 生成AI学习计划</span>
                                    }
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- 操作结果提示 -->
    @if (!string.IsNullOrEmpty(resultMessage))
    {
        <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1050;">
            <div class="toast show" role="alert">
                <div class="toast-header">
                    <strong class="me-auto">@(isSuccess ? "✅ 成功" : "❌ 错误")</strong>
                    <button type="button" class="btn-close" @onclick="ClearResult"></button>
                </div>
                <div class="toast-body">
                    @resultMessage
                </div>
            </div>
        </div>
    }
</div>

<style>
    .card:hover {
        transform: translateY(-2px);
        transition: all 0.3s ease;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .form-check-input:checked {
        background-color: #0d6efd;
        border-color: #0d6efd;
    }
    
    .progress-bar-animated {
        animation: progress-bar-stripes 1s linear infinite;
    }
    
    @@keyframes progress-bar-stripes {
        0% { background-position: 1rem 0; }
        100% { background-position: 0 0; }
    }
</style>

@code {
    private bool isGenerating = false;
    private bool isSuccess = false;
    private string resultMessage = "";
    private int generationProgress = 0;
    private string generationStatus = "";

    private string learningTopic = "";
    private int durationDays = 14;
    private LearningPreferences preferences = new();
    private LearningPlan? generatedPlan = null;

    // 学习重点选项
    private bool focusTheory = true;
    private bool focusPractice = true;
    private bool focusProjects = false;
    private bool focusApplications = false;

    protected override void OnInitialized()
    {
        // 设置默认偏好
        preferences.DifficultyLevel = "intermediate";
        preferences.DailyHours = 2.0;
        preferences.LearningStyle = "balanced";
        preferences.PreferredTeacherType = "case_driven";
    }

    private async Task GenerateAIPlan()
    {
        if (string.IsNullOrWhiteSpace(learningTopic))
        {
            ShowMessage("请输入学习主题", false);
            return;
        }

        isGenerating = true;
        generationProgress = 0;
        generatedPlan = null;

        try
        {
            // 更新学习重点
            UpdateFocusAreas();

            // 模拟生成进度
            await SimulateGenerationProgress();

            // 调用AI服务生成计划
            generatedPlan = await AITeacherService.GenerateIntelligentPlanAsync(learningTopic, preferences, durationDays);

            generationProgress = 100;
            generationStatus = "生成完成！";

            ShowMessage("AI学习计划生成成功！", true);
            System.Diagnostics.Debug.WriteLine($"✅ AI生成计划: {generatedPlan.Name}");
        }
        catch (Exception ex)
        {
            ShowMessage($"生成失败: {ex.Message}", false);
            System.Diagnostics.Debug.WriteLine($"❌ AI生成计划失败: {ex}");
        }
        finally
        {
            isGenerating = false;
        }
    }

    private async Task SimulateGenerationProgress()
    {
        var steps = new[]
        {
            (20, "分析学习需求..."),
            (40, "构建知识图谱..."),
            (60, "生成WWH框架..."),
            (80, "优化学习路径..."),
            (95, "完善计划细节...")
        };

        foreach (var (progress, status) in steps)
        {
            generationProgress = progress;
            generationStatus = status;
            StateHasChanged();
            await Task.Delay(800); // 模拟处理时间
        }
    }

    private void UpdateFocusAreas()
    {
        preferences.FocusAreas.Clear();

        if (focusTheory) preferences.FocusAreas.Add("theory");
        if (focusPractice) preferences.FocusAreas.Add("practice");
        if (focusProjects) preferences.FocusAreas.Add("projects");
        if (focusApplications) preferences.FocusAreas.Add("applications");

        // 确保至少有一个重点
        if (!preferences.FocusAreas.Any())
        {
            preferences.FocusAreas.Add("balanced");
        }
    }

    private async Task SaveGeneratedPlan()
    {
        if (generatedPlan == null) return;

        try
        {
            // 加载现有计划
            var existingPlans = await StorageService.LoadAsync<List<LearningPlan>>("learning_plans") ?? new List<LearningPlan>();

            // 确保ID唯一
            generatedPlan.Id = existingPlans.Any() ? existingPlans.Max(p => p.Id) + 1 : 1;
            generatedPlan.CreatedAt = DateTime.Now;
            generatedPlan.UpdatedAt = DateTime.Now;

            // 添加到列表
            existingPlans.Add(generatedPlan);

            // 保存
            await StorageService.SaveAsync("learning_plans", existingPlans);

            ShowMessage($"学习计划 '{generatedPlan.Name}' 已保存！", true);

            // 导航到计划详情页
            await Task.Delay(1500);
            Navigation.NavigateTo($"/learning-plan-detail/{generatedPlan.Id}");
        }
        catch (Exception ex)
        {
            ShowMessage($"保存失败: {ex.Message}", false);
            System.Diagnostics.Debug.WriteLine($"❌ 保存AI计划失败: {ex}");
        }
    }

    private async Task RegenerateWithAdjustments()
    {
        generatedPlan = null;
        await GenerateAIPlan();
    }

    private void ShowMessage(string message, bool success)
    {
        resultMessage = message;
        isSuccess = success;

        Task.Delay(3000).ContinueWith(_ =>
        {
            resultMessage = "";
            InvokeAsync(StateHasChanged);
        });
    }

    private void ClearResult()
    {
        resultMessage = "";
    }
}
