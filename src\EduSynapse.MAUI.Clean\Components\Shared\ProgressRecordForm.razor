@using EduSynapse.MAUI.ViewModels
@using MudBlazor
@inject ProgressViewModel ViewModel

<MudGrid>
    <!-- 天数选择 -->
    <MudItem xs="12" Class="mb-4">
        <MudText Typo="Typo.h6" Class="mb-3">
            <MudIcon Icon="@Icons.Material.Filled.Today" Class="mr-2" />
            选择学习天数
        </MudText>
        
        <div class="d-flex flex-wrap gap-2">
            @if (ViewModel.CurrentPlan != null)
            {
                @for (int day = 1; day <= ViewModel.CurrentPlan.DurationDays; day++)
                {
                    var dayNumber = day; // 避免闭包问题
                    var isCompleted = ViewModel.ProgressRecords.Any(p => p.DayNumber == dayNumber);
                    var isSelected = ViewModel.SelectedDayNumber == dayNumber;
                    
                    <MudChip T="string" Color="@(isSelected ? Color.Primary : (isCompleted ? Color.Success : Color.Default))"
                             Variant="@(isSelected ? Variant.Filled : Variant.Outlined)"
                             OnClick="@(() => ViewModel.SelectDayCommand.ExecuteAsync(dayNumber))"
                             Class="cursor-pointer">
                        �?@dayNumber �?                        @if (isCompleted)
                        {
                            <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Size="Size.Small" Class="ml-1" />
                        }
                    </MudChip>
                }
            }
        </div>
    </MudItem>

    <!-- WWH掌握度评�?-->
    <MudItem xs="12">
        <MudCard Elevation="1" Class="pa-4 mb-4">
            <MudText Typo="Typo.h6" Class="mb-4">
                <MudIcon Icon="@Icons.Material.Filled.Psychology" Class="mr-2" />
                WWH掌握度评�?            </MudText>
            
            <MudGrid>
                <!-- What 掌握�?-->
                <MudItem xs="12" md="4">
                    <div class="wwh-section wwh-what">
                        <MudText Typo="Typo.subtitle1" Class="mb-2">
                            <MudIcon Icon="@Icons.Material.Filled.MenuBook" Class="mr-1" />
                            What (是什�? - @ViewModel.WhatMastery.ToString("F0")%
                        </MudText>
                        <MudSlider Value="@ViewModel.WhatMastery"
                                   ValueChanged="@((double val) => ViewModel.WhatMastery = val)"
                                   Min="0" Max="100" Step="5"
                                   Color="Color.Info"
                                   Variant="Variant.Filled" />
                        <MudText Typo="Typo.caption" Color="Color.Secondary">
                            核心概念、定义、基础知识的理解程�?                        </MudText>
                    </div>
                </MudItem>
                
                <!-- Why 掌握�?-->
                <MudItem xs="12" md="4">
                    <div class="wwh-section wwh-why">
                        <MudText Typo="Typo.subtitle1" Class="mb-2">
                            <MudIcon Icon="@Icons.Material.Filled.QuestionMark" Class="mr-1" />
                            Why (为什�? - @ViewModel.WhyMastery.ToString("F0")%
                        </MudText>
                        <MudSlider Value="@ViewModel.WhyMastery"
                                   ValueChanged="@((double val) => ViewModel.WhyMastery = val)"
                                   Min="0" Max="100" Step="5"
                                   Color="Color.Warning"
                                   Variant="Variant.Filled" />
                        <MudText Typo="Typo.caption" Color="Color.Secondary">
                            背景原理、重要性、应用价值的理解程度
                        </MudText>
                    </div>
                </MudItem>
                
                <!-- How 掌握�?-->
                <MudItem xs="12" md="4">
                    <div class="wwh-section wwh-how">
                        <MudText Typo="Typo.subtitle1" Class="mb-2">
                            <MudIcon Icon="@Icons.Material.Filled.Build" Class="mr-1" />
                            How (怎么�? - @ViewModel.HowMastery.ToString("F0")%
                        </MudText>
                        <MudSlider Value="@ViewModel.HowMastery"
                                   ValueChanged="@((double val) => ViewModel.HowMastery = val)"
                                   Min="0" Max="100" Step="5"
                                   Color="Color.Success"
                                   Variant="Variant.Filled" />
                        <MudText Typo="Typo.caption" Color="Color.Secondary">
                            实践应用、操作技能、项目实战的掌握程度
                        </MudText>
                    </div>
                </MudItem>
            </MudGrid>
            
            <!-- 总体掌握度显�?-->
            <MudDivider Class="my-4" />
            <div class="text-center">
                <MudText Typo="Typo.h6" Color="Color.Primary">
                    总体掌握�? @ViewModel.OverallMastery.ToString("F1")%
                </MudText>
                <MudProgressLinear Color="Color.Primary" 
                                   Value="@ViewModel.OverallMastery" 
                                   Class="mt-2" />
            </div>
        </MudCard>
    </MudItem>

    <!-- 学习时间记录 -->
    <MudItem xs="12" md="6">
        <MudCard Elevation="1" Class="pa-4">
            <MudText Typo="Typo.h6" Class="mb-4">
                <MudIcon Icon="@Icons.Material.Filled.AccessTime" Class="mr-2" />
                学习时间记录
            </MudText>
            
            <!-- 快速时间设�?-->
            <div class="mb-3">
                <MudText Typo="Typo.body2" Class="mb-2">快速设�?</MudText>
                <div class="d-flex flex-wrap gap-2">
                    <MudButton Size="Size.Small" Variant="Variant.Outlined" OnClick="@(() => ViewModel.SetQuickTimeCommand.Execute(30))">30分钟</MudButton>
                    <MudButton Size="Size.Small" Variant="Variant.Outlined" OnClick="@(() => ViewModel.SetQuickTimeCommand.Execute(60))">1小时</MudButton>
                    <MudButton Size="Size.Small" Variant="Variant.Outlined" OnClick="@(() => ViewModel.SetQuickTimeCommand.Execute(90))">1.5小时</MudButton>
                    <MudButton Size="Size.Small" Variant="Variant.Outlined" OnClick="@(() => ViewModel.SetQuickTimeCommand.Execute(120))">2小时</MudButton>
                </div>
            </div>
            
            <MudNumericField Value="@ViewModel.TimeSpent"
                             ValueChanged="@((int val) => ViewModel.TimeSpent = val)"
                             Label="总学习时�?(分钟)"
                             Variant="Variant.Outlined"
                             Min="0" Max="720"
                             Class="mb-3" />

            <MudNumericField Value="@ViewModel.FocusTime"
                             ValueChanged="@((int val) => ViewModel.FocusTime = val)"
                             Label="专注时长 (分钟)"
                             Variant="Variant.Outlined"
                             Min="0" Max="@ViewModel.TimeSpent"
                             Class="mb-3" />

            <MudNumericField Value="@ViewModel.BreakCount"
                             ValueChanged="@((int val) => ViewModel.BreakCount = val)"
                             Label="休息次数"
                             Variant="Variant.Outlined"
                             Min="0" Max="20"
                             Class="mb-3" />
        </MudCard>
    </MudItem>

    <!-- 学习体验评价 -->
    <MudItem xs="12" md="6">
        <MudCard Elevation="1" Class="pa-4">
            <MudText Typo="Typo.h6" Class="mb-4">
                <MudIcon Icon="@Icons.Material.Filled.Mood" Class="mr-2" />
                学习体验评价
            </MudText>
            
            <!-- 心情评分 -->
            <div class="mb-4">
                <MudText Typo="Typo.body1" Class="mb-2">
                    学习心情: @ViewModel.GetMoodDisplayText(ViewModel.MoodScore)
                </MudText>
                <MudRating SelectedValue="@ViewModel.MoodScore"
                           SelectedValueChanged="@((int val) => ViewModel.MoodScore = val)"
                           MaxValue="5"
                           FullIcon="@Icons.Material.Filled.Mood"
                           EmptyIcon="@Icons.Material.Filled.MoodBad"
                           Color="Color.Warning" />
            </div>
            
            <!-- 难度评价 -->
            <div class="mb-4">
                <MudText Typo="Typo.body1" Class="mb-2">
                    内容难度: @ViewModel.GetDifficultyDisplayText(ViewModel.DifficultyRating)
                </MudText>
                <MudRating SelectedValue="@ViewModel.DifficultyRating"
                           SelectedValueChanged="@((int val) => ViewModel.DifficultyRating = val)"
                           MaxValue="5"
                           FullIcon="@Icons.Material.Filled.Star"
                           EmptyIcon="@Icons.Material.Filled.StarBorder"
                           Color="Color.Primary" />
            </div>
        </MudCard>
    </MudItem>

    <!-- 学习笔记 -->
    <MudItem xs="12">
        <MudCard Elevation="1" Class="pa-4">
            <MudText Typo="Typo.h6" Class="mb-3">
                <MudIcon Icon="@Icons.Material.Filled.Notes" Class="mr-2" />
                学习笔记
            </MudText>
            
            <MudTextField Value="@ViewModel.Notes"
                          ValueChanged="@((string val) => ViewModel.Notes = val)"
                          Label="今日学习总结、心得体会、遇到的问题�?
                          Variant="Variant.Outlined"
                          Lines="4"
                          MaxLength="2000"
                          Counter="2000"
                          Placeholder="记录您今天的学习收获、遇到的困难、解决的问题、新的想法等..." />
        </MudCard>
    </MudItem>

    <!-- 操作按钮 -->
    <MudItem xs="12" Class="text-center mt-4">
        <MudButton Variant="Variant.Filled"
                   Color="Color.Primary"
                   Size="Size.Large"
                   StartIcon="@Icons.Material.Filled.Save"
                   OnClick="@ViewModel.RecordProgressCommand.ExecuteAsync"
                   Disabled="@ViewModel.IsRecording"
                   Class="mr-3">
            @if (ViewModel.IsRecording)
            {
                <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                <MudText Class="ms-2">保存�?..</MudText>
            }
            else
            {
                <MudText>保存进度</MudText>
            }
        </MudButton>
        
        <MudButton Variant="Variant.Outlined"
                   Color="Color.Secondary"
                   Size="Size.Large"
                   StartIcon="@Icons.Material.Filled.Refresh"
                   OnClick="@ViewModel.ResetProgressFormCommand.Execute">
            重置表单
        </MudButton>
    </MudItem>
</MudGrid>

<style>
    .wwh-section {
        padding: 16px;
        border-radius: 8px;
        border-left: 4px solid;
        margin-bottom: 16px;
    }
    
    .wwh-what {
        border-left-color: #2196f3;
        background-color: rgba(33, 150, 243, 0.05);
    }
    
    .wwh-why {
        border-left-color: #ff9800;
        background-color: rgba(255, 152, 0, 0.05);
    }
    
    .wwh-how {
        border-left-color: #4caf50;
        background-color: rgba(76, 175, 80, 0.05);
    }
    
    .cursor-pointer {
        cursor: pointer;
    }
    
    .gap-2 > * {
        margin: 4px;
    }
</style>
