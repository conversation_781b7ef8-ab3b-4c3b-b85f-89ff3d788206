using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using EduSynapse.MAUI.Services;

namespace EduSynapse.MAUI.ViewModels;

/// <summary>
/// ViewModel基类
/// </summary>
public abstract partial class BaseViewModel : ObservableObject
{
    protected readonly IApiService _apiService;
    protected readonly IStateService _stateService;
    protected readonly IStorageService _storageService;

    [ObservableProperty]
    private bool _isLoading;

    [ObservableProperty]
    private string? _errorMessage;

    [ObservableProperty]
    private string _title = string.Empty;

    protected BaseViewModel(
        IApiService apiService,
        IStateService stateService,
        IStorageService storageService)
    {
        _apiService = apiService;
        _stateService = stateService;
        _storageService = storageService;

        // 订阅状态变化
        _stateService.StateChanged += OnStateChanged;
    }

    /// <summary>
    /// 状态变化处理
    /// </summary>
    protected virtual void OnStateChanged(object? sender, EventArgs e)
    {
        IsLoading = _stateService.IsLoading;
        ErrorMessage = _stateService.ErrorMessage;
    }

    /// <summary>
    /// 设置错误消息
    /// </summary>
    protected void SetError(string message)
    {
        ErrorMessage = message;
        _stateService.SetError(message);
    }

    /// <summary>
    /// 清除错误消息
    /// </summary>
    [RelayCommand]
    protected void ClearError()
    {
        ErrorMessage = null;
        _stateService.ClearError();
    }

    /// <summary>
    /// 设置加载状态
    /// </summary>
    protected void SetLoading(bool loading)
    {
        IsLoading = loading;
        _stateService.IsLoading = loading;
    }

    /// <summary>
    /// 执行异步操作并处理错误
    /// </summary>
    protected async Task ExecuteAsync(Func<Task> operation, string? errorMessage = null)
    {
        try
        {
            SetLoading(true);
            ClearError();
            await operation();
        }
        catch (Exception ex)
        {
            var message = errorMessage ?? ex.Message;
            SetError(message);
            System.Diagnostics.Debug.WriteLine($"Operation failed: {ex}");
        }
        finally
        {
            SetLoading(false);
        }
    }

    /// <summary>
    /// 执行异步操作并返回结果
    /// </summary>
    protected async Task<T?> ExecuteAsync<T>(Func<Task<T?>> operation, string? errorMessage = null)
    {
        try
        {
            SetLoading(true);
            ClearError();
            return await operation();
        }
        catch (Exception ex)
        {
            var message = errorMessage ?? ex.Message;
            SetError(message);
            System.Diagnostics.Debug.WriteLine($"Operation failed: {ex}");
            return default;
        }
        finally
        {
            SetLoading(false);
        }
    }

    /// <summary>
    /// 初始化ViewModel
    /// </summary>
    public virtual async Task InitializeAsync()
    {
        // 子类可以重写此方法进行初始化
        await Task.CompletedTask;
    }

    /// <summary>
    /// 刷新数据
    /// </summary>
    [RelayCommand]
    public virtual async Task RefreshAsync()
    {
        await InitializeAsync();
    }
}
