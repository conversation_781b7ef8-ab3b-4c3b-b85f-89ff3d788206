@using MudBlazor
@using EduSynapse.MAUI.Components.Layout

<MudThemeProvider />
<MudDialogProvider />
<MudSnackbarProvider />

<Router AppAssembly="@typeof(App).Assembly">
    <Found Context="routeData">
        <RouteView RouteData="@routeData" DefaultLayout="@typeof(SafeMainLayout)" />
    </Found>
    <NotFound>
        <PageTitle>页面未找到</PageTitle>
        <LayoutView Layout="@typeof(SafeMainLayout)">
            <div class="d-flex justify-center align-center" style="height: 50vh;">
                <MudPaper Class="pa-8 text-center" Elevation="2">
                    <MudIcon Icon="@Icons.Material.Filled.Error" Size="Size.Large" Color="Color.Error" Class="mb-4" />
                    <MudText Typo="Typo.h4" Class="mb-4">页面未找到</MudText>
                    <MudText Typo="Typo.body1" Class="mb-4">抱歉，您访问的页面不存在。</MudText>
                    <MudButton Variant="Variant.Filled" Color="Color.Primary" Href="/">返回首页</MudButton>
                </MudPaper>
            </div>
        </LayoutView>
    </NotFound>
</Router>
