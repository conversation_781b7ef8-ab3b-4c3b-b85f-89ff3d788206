@page "/"
@using Microsoft.AspNetCore.Components.Web

<h1>🎯 最小Blazor测试</h1>

<div style="padding: 20px; border: 2px solid #007bff; border-radius: 8px; margin: 10px;">
    <h3>📊 基础功能测试</h3>
    <p><strong>计数器:</strong> @count</p>
    <p><strong>当前时间:</strong> @currentTime</p>
    
    <div style="margin: 10px 0;">
        <button @onclick="IncrementCount" 
                style="background: #007bff; color: white; border: none; padding: 12px 24px; margin: 5px; border-radius: 4px; cursor: pointer;">
            增加计数
        </button>
        
        <button @onclick="ResetCount" 
                style="background: #dc3545; color: white; border: none; padding: 12px 24px; margin: 5px; border-radius: 4px; cursor: pointer;">
            重置计数
        </button>
    </div>
    
    <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin-top: 10px;">
        <p><strong>状态:</strong> @status</p>
        <p><strong>上次操作:</strong> @lastAction</p>
    </div>
</div>

@code {
    private int count = 0;
    private string currentTime = DateTime.Now.ToString("HH:mm:ss");
    private string status = "正常";
    private string lastAction = "无";

    protected override void OnInitialized()
    {
        try
        {
            status = "初始化成功";
            currentTime = DateTime.Now.ToString("HH:mm:ss");
            System.Diagnostics.Debug.WriteLine("✅ MinimalBlazorTest 组件初始化成功");
        }
        catch (Exception ex)
        {
            status = $"初始化失败: {ex.Message}";
            System.Diagnostics.Debug.WriteLine($"❌ MinimalBlazorTest 初始化失败: {ex}");
        }
    }

    private void IncrementCount()
    {
        try
        {
            count++;
            currentTime = DateTime.Now.ToString("HH:mm:ss");
            lastAction = "增加计数";
            status = "计数增加成功";
            System.Diagnostics.Debug.WriteLine($"✅ 计数增加到: {count}");
        }
        catch (Exception ex)
        {
            status = $"增加计数失败: {ex.Message}";
            System.Diagnostics.Debug.WriteLine($"❌ 增加计数失败: {ex}");
        }
    }

    private void ResetCount()
    {
        try
        {
            count = 0;
            currentTime = DateTime.Now.ToString("HH:mm:ss");
            lastAction = "重置计数";
            status = "计数重置成功";
            System.Diagnostics.Debug.WriteLine("✅ 计数重置成功");
        }
        catch (Exception ex)
        {
            status = $"重置计数失败: {ex.Message}";
            System.Diagnostics.Debug.WriteLine($"❌ 重置计数失败: {ex}");
        }
    }
}
