@page "/learning-analytics"
@using EduSynapse.MAUI.Services
@using EduSynapse.MAUI.Models
@inject StorageService StorageService
@inject IJSRuntime JSRuntime

<div class="container-fluid p-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2>📊 学习数据分析</h2>
                    <p class="text-muted">深度分析您的学习数据，发现学习模式和趋势</p>
                </div>
                <div class="d-flex gap-2">
                    <select @bind="selectedTimeRange" @bind:after="RefreshAnalytics" class="form-select" style="width: auto;">
                        <option value="7">最近7天</option>
                        <option value="30">最近30天</option>
                        <option value="90">最近90天</option>
                        <option value="0">全部时间</option>
                    </select>
                    <button class="btn btn-outline-primary" @onclick="RefreshAnalytics">
                        🔄 刷新数据
                    </button>
                </div>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center p-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">分析数据中...</span>
            </div>
            <p class="mt-2">正在分析学习数据...</p>
        </div>
    }
    else if (analyticsData == null)
    {
        <div class="alert alert-warning">
            <h5>📈 暂无学习数据</h5>
            <p>开始记录学习进度后，这里将显示详细的数据分析。</p>
            <a href="/learning-plan-list" class="btn btn-primary">开始学习</a>
        </div>
    }
    else
    {
        <!-- 总体统计卡片 -->
        <div class="row mb-4">
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h3>@analyticsData.TotalStudyDays</h3>
                        <p class="mb-0">学习天数</p>
                        <small>最近 @selectedTimeRange 天</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h3>@analyticsData.TotalStudyHours.ToString("F1")</h3>
                        <p class="mb-0">学习时间</p>
                        <small>累计小时数</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h3>@analyticsData.AverageHoursPerDay.ToString("F1")</h3>
                        <p class="mb-0">平均时间</p>
                        <small>每日小时数</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h3>@analyticsData.AverageMoodScore.ToString("F1")</h3>
                        <p class="mb-0">平均心情</p>
                        <small>满分10分</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- 学习趋势图表 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">📈 学习时间趋势</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container" style="height: 300px;">
                            <canvas id="studyTrendChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 学习状态分布 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">📊 学习状态分布</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container" style="height: 250px;">
                            <canvas id="statusPieChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">😊 心情评分分布</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container" style="height: 250px;">
                            <canvas id="moodBarChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 学习计划表现 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">🎯 学习计划表现</h5>
                    </div>
                    <div class="card-body">
                        @if (analyticsData.PlanStatusData?.Any() == true)
                        {
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>状态</th>
                                            <th>数量</th>
                                            <th>百分比</th>
                                            <th>-</th>
                                            <th>-</th>
                                            <th>-</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var plan in analyticsData.PlanStatusData)
                                        {
                                            <tr>
                                                <td>@GetStatusDisplayName(plan.Status)</td>
                                                <td>@plan.Count</td>
                                                <td>@plan.Percentage.ToString("F1")%</td>
                                                <td>-</td>
                                                <td>-</td>
                                                <td>-</td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                        else
                        {
                            <div class="text-center p-4">
                                <p class="text-muted">暂无学习计划数据</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- 学习洞察 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">💡 学习洞察</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @foreach (var insight in analyticsData.Insights)
                            {
                                <div class="col-md-6 mb-3">
                                    <div class="alert @GetInsightAlertClass(insight.Type)">
                                        <h6>@insight.Icon @insight.Title</h6>
                                        <p class="mb-0">@insight.Description</p>
                                        @if (!string.IsNullOrEmpty(insight.Suggestion))
                                        {
                                            <small class="d-block mt-2"><strong>建议：</strong>@insight.Suggestion</small>
                                        }
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 导出功能 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">📤 导出分析报告</h6>
                                <small class="text-muted">将学习分析数据导出为报告</small>
                            </div>
                            <div class="d-flex gap-2">
                                <button class="btn btn-outline-success" @onclick="ExportToJson">
                                    📄 导出JSON
                                </button>
                                <button class="btn btn-outline-info" @onclick="ExportToConsole">
                                    🖥️ 控制台查看
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- 操作结果提示 -->
    @if (!string.IsNullOrEmpty(resultMessage))
    {
        <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1050;">
            <div class="toast show" role="alert">
                <div class="toast-header">
                    <strong class="me-auto">@(isSuccess ? "✅ 成功" : "❌ 错误")</strong>
                    <button type="button" class="btn-close" @onclick="ClearResult"></button>
                </div>
                <div class="toast-body">
                    @resultMessage
                </div>
            </div>
        </div>
    }
</div>

@code {
    private bool isLoading = false;
    private bool isSuccess = false;
    private string resultMessage = "";
    private int selectedTimeRange = 30;

    private AnalyticsData? analyticsData = null;
    private List<LearningPlan> allPlans = new();
    private Dictionary<int, List<ProgressRecord>> allProgressRecords = new();

    protected override async Task OnInitializedAsync()
    {
        await RefreshAnalytics();
    }

    private async Task RefreshAnalytics()
    {
        isLoading = true;
        try
        {
            // 加载所有学习计划
            allPlans = await StorageService.LoadAsync<List<LearningPlan>>("learning_plans") ?? new List<LearningPlan>();

            // 加载所有进度记录
            allProgressRecords.Clear();
            foreach (var plan in allPlans)
            {
                var storageKey = $"progress_records_{plan.Id}";
                var records = await StorageService.LoadAsync<List<ProgressRecord>>(storageKey) ?? new List<ProgressRecord>();
                if (records.Any())
                {
                    allProgressRecords[plan.Id] = records;
                }
            }

            // 生成分析数据
            analyticsData = GenerateAnalyticsData();

            ShowMessage("学习数据分析完成", true);
            System.Diagnostics.Debug.WriteLine($"✅ 分析数据: {allPlans.Count} 个计划，{allProgressRecords.Sum(kvp => kvp.Value.Count)} 条记录");
        }
        catch (Exception ex)
        {
            ShowMessage($"分析数据失败: {ex.Message}", false);
            System.Diagnostics.Debug.WriteLine($"❌ 分析数据失败: {ex}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private AnalyticsData GenerateAnalyticsData()
    {
        var cutoffDate = selectedTimeRange > 0 ? DateTime.Today.AddDays(-selectedTimeRange) : DateTime.MinValue;
        var filteredRecords = allProgressRecords
            .SelectMany(kvp => kvp.Value.Where(r => r.Date >= cutoffDate))
            .OrderBy(r => r.Date)
            .ToList();

        if (!filteredRecords.Any())
        {
            return new AnalyticsData();
        }

        // 基础统计
        var totalStudyHours = filteredRecords.Sum(r => r.StudyHours);
        var totalStudyDays = filteredRecords.Count(r => r.StudyHours > 0);
        var averageHoursPerDay = totalStudyDays > 0 ? totalStudyHours / totalStudyDays : 0;
        var averageMoodScore = filteredRecords.Where(r => r.MoodScore > 0).Average(r => (double)r.MoodScore);

        // 每日数据
        var dailyData = filteredRecords
            .GroupBy(r => r.Date.Date)
            .Select(g => new DailyData
            {
                Date = g.Key,
                StudyHours = g.Sum(r => r.StudyHours),
                MoodScore = g.Where(r => r.MoodScore > 0).DefaultIfEmpty().Average(r => r?.MoodScore ?? 0),
                RecordCount = g.Count()
            })
            .OrderBy(d => d.Date)
            .ToList();

        // 计划状态分布
        var planStatusData = allPlans
            .GroupBy(p => p.Status)
            .Select(g => new StatusData
            {
                Status = g.Key,
                Count = g.Count(),
                Percentage = (double)g.Count() / allPlans.Count * 100
            })
            .ToList();

        // 生成洞察
        var insights = GenerateInsights(filteredRecords, dailyData);

        return new AnalyticsData
        {
            TotalPlans = allPlans.Count,
            ActivePlans = allPlans.Count(p => p.Status == "active"),
            TotalStudyHours = totalStudyHours,
            TotalStudyDays = totalStudyDays,
            AverageHoursPerDay = averageHoursPerDay,
            AverageMoodScore = averageMoodScore,
            DailyData = dailyData,
            PlanStatusData = planStatusData,
            Insights = insights
        };
    }

    private List<LearningInsight> GenerateInsights(List<ProgressRecord> records, List<DailyData> dailyData)
    {
        var insights = new List<LearningInsight>();

        // 学习连续性分析
        var studyStreak = CalculateStudyStreak(dailyData);
        if (studyStreak >= 7)
        {
            insights.Add(new LearningInsight
            {
                Type = "success",
                Icon = "🔥",
                Title = "学习连续性优秀",
                Description = $"您已连续学习 {studyStreak} 天，保持了良好的学习习惯！",
                Suggestion = "继续保持这种节奏，适当安排休息日避免疲劳。"
            });
        }
        else if (studyStreak < 3)
        {
            insights.Add(new LearningInsight
            {
                Type = "warning",
                Icon = "⚠️",
                Title = "学习连续性需要改善",
                Description = $"最近的学习连续性较低，只有 {studyStreak} 天。",
                Suggestion = "尝试制定每日学习计划，即使每天只学习30分钟也能保持连续性。"
            });
        }

        // 学习时间分析
        var avgHours = dailyData.Where(d => d.StudyHours > 0).Average(d => d.StudyHours);
        if (avgHours >= 2)
        {
            insights.Add(new LearningInsight
            {
                Type = "success",
                Icon = "⏰",
                Title = "学习时间充足",
                Description = $"平均每日学习 {avgHours:F1} 小时，时间投入充足。",
                Suggestion = "可以考虑提高学习效率，或者增加一些实践项目。"
            });
        }
        else if (avgHours < 1)
        {
            insights.Add(new LearningInsight
            {
                Type = "info",
                Icon = "📚",
                Title = "可以增加学习时间",
                Description = $"平均每日学习 {avgHours:F1} 小时，还有提升空间。",
                Suggestion = "尝试每天增加30分钟学习时间，积少成多效果显著。"
            });
        }

        // 心情趋势分析
        var recentMood = dailyData.TakeLast(7).Where(d => d.MoodScore > 0).Average(d => d.MoodScore);
        if (recentMood >= 8)
        {
            insights.Add(new LearningInsight
            {
                Type = "success",
                Icon = "😊",
                Title = "学习状态良好",
                Description = $"最近的学习心情评分为 {recentMood:F1}，状态很好！",
                Suggestion = "保持当前的学习方式和节奏。"
            });
        }
        else if (recentMood < 6)
        {
            insights.Add(new LearningInsight
            {
                Type = "warning",
                Icon = "😔",
                Title = "学习状态需要调整",
                Description = $"最近的学习心情评分为 {recentMood:F1}，可能需要调整。",
                Suggestion = "尝试改变学习环境、调整学习内容难度，或者适当休息。"
            });
        }

        return insights;
    }

    private int CalculateStudyStreak(List<DailyData> dailyData)
    {
        var streak = 0;
        var currentDate = DateTime.Today;

        for (int i = 0; i < 30; i++) // 检查最近30天
        {
            var checkDate = currentDate.AddDays(-i);
            var dayData = dailyData.FirstOrDefault(d => d.Date.Date == checkDate);

            if (dayData != null && dayData.StudyHours > 0)
            {
                streak++;
            }
            else
            {
                break;
            }
        }

        return streak;
    }

    private async Task ExportToJson()
    {
        try
        {
            var json = System.Text.Json.JsonSerializer.Serialize(analyticsData, new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
            });

            await JSRuntime.InvokeVoidAsync("console.log", "学习分析数据:", json);
            ShowMessage("分析数据已导出到浏览器控制台", true);
        }
        catch (Exception ex)
        {
            ShowMessage($"导出失败: {ex.Message}", false);
        }
    }

    private async Task ExportToConsole()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("console.log", "学习分析数据:", analyticsData);
            ShowMessage("分析数据已输出到控制台", true);
        }
        catch (Exception ex)
        {
            ShowMessage($"输出失败: {ex.Message}", false);
        }
    }

    private string GetInsightAlertClass(string type)
    {
        return type switch
        {
            "success" => "alert-success",
            "warning" => "alert-warning",
            "info" => "alert-info",
            "danger" => "alert-danger",
            _ => "alert-secondary"
        };
    }

    private void ShowMessage(string message, bool success)
    {
        resultMessage = message;
        isSuccess = success;

        Task.Delay(3000).ContinueWith(_ =>
        {
            resultMessage = "";
            InvokeAsync(StateHasChanged);
        });
    }

    private void ClearResult()
    {
        resultMessage = "";
    }

    private string GetStatusDisplayName(string status)
    {
        return status switch
        {
            "active" => "进行中",
            "paused" => "已暂停",
            "completed" => "已完成",
            "cancelled" => "已取消",
            _ => "未知"
        };
    }

    // 数据模型
    public class AnalyticsData
    {
        public int TotalPlans { get; set; }
        public int ActivePlans { get; set; }
        public double TotalStudyHours { get; set; }
        public int TotalStudyDays { get; set; }
        public double AverageHoursPerDay { get; set; }
        public double AverageMoodScore { get; set; }
        public List<DailyData> DailyData { get; set; } = new();
        public List<StatusData> PlanStatusData { get; set; } = new();
        public List<LearningInsight> Insights { get; set; } = new();
    }

    public class DailyData
    {
        public DateTime Date { get; set; }
        public double StudyHours { get; set; }
        public double MoodScore { get; set; }
        public int RecordCount { get; set; }
    }

    public class StatusData
    {
        public string Status { get; set; } = "";
        public int Count { get; set; }
        public double Percentage { get; set; }
    }

    public class LearningInsight
    {
        public string Type { get; set; } = "";
        public string Icon { get; set; } = "";
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public string Suggestion { get; set; } = "";
    }

    public class ProgressRecord
    {
        public DateTime Date { get; set; }
        public double StudyHours { get; set; }
        public string Status { get; set; } = "completed";
        public string Content { get; set; } = "";
        public int MoodScore { get; set; } = 8;
        public string Notes { get; set; } = "";
        public int PlanId { get; set; }
    }
}
