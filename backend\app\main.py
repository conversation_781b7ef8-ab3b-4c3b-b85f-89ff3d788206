"""
EduSynapse AI Backend Main Application
基于FastAPI的智能教学系统后端服务
"""

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import uvicorn
from typing import Dict, List, Optional, Any
import asyncio
import logging

from .ai.teaching_engine import IntelligentTeachingEngine
from .ai.progress_tracker import FiveDimensionProgressTracker
from .models.api_models import (
    GeneratePlanRequest, GeneratePlanResponse,
    StartSessionRequest, StartSessionResponse,
    ContinueSessionRequest, ContinueSessionResponse,
    TrackProgressRequest, TrackProgressResponse,
    ProcessResourceRequest, ProcessResourceResponse
)
from .models.learning_models import LearningPreferences, TeacherType, LearningStage
from .core.config import settings
from .utils.logger import setup_logging, get_logger

# 设置日志
setup_logging()
logger = get_logger(__name__)

# 全局变量
teaching_engine: Optional[IntelligentTeachingEngine] = None
progress_tracker: Optional[FiveDimensionProgressTracker] = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global teaching_engine, progress_tracker
    
    # 启动时初始化
    logger.info("🚀 Starting EduSynapse AI Backend...")
    
    try:
        # 初始化AI引擎
        teaching_engine = IntelligentTeachingEngine()
        progress_tracker = FiveDimensionProgressTracker()
        
        logger.info("✅ AI engines initialized successfully")
        
        yield
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize AI engines: {e}")
        raise
    finally:
        # 关闭时清理
        logger.info("🛑 Shutting down EduSynapse AI Backend...")


# 创建FastAPI应用
app = FastAPI(
    title="EduSynapse AI Backend",
    description="基于LangChain和AutoGen的智能教学系统后端API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 依赖注入
def get_teaching_engine() -> IntelligentTeachingEngine:
    """获取教学引擎实例"""
    if teaching_engine is None:
        raise HTTPException(status_code=503, detail="Teaching engine not initialized")
    return teaching_engine


def get_progress_tracker() -> FiveDimensionProgressTracker:
    """获取进度跟踪器实例"""
    if progress_tracker is None:
        raise HTTPException(status_code=503, detail="Progress tracker not initialized")
    return progress_tracker


# API路由
@app.get("/")
async def root():
    """根路径 - 健康检查"""
    return {
        "message": "🎓 EduSynapse AI Backend is running!",
        "version": "1.0.0",
        "status": "healthy",
        "features": [
            "智能学习计划生成",
            "多AI教师协作",
            "五维进度跟踪",
            "WWH教学框架",
            "错误模式分析"
        ]
    }


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "teaching_engine": teaching_engine is not None,
        "progress_tracker": progress_tracker is not None,
        "timestamp": "2025-07-14T12:00:00Z"
    }


@app.post("/api/v1/teaching/generate-plan", response_model=GeneratePlanResponse)
async def generate_learning_plan(
    request: GeneratePlanRequest,
    engine: IntelligentTeachingEngine = Depends(get_teaching_engine)
):
    """
    🚀 智能生成学习计划
    
    基于WWH教学框架和LangChain技术，为用户生成个性化的学习计划
    """
    try:
        logger.info(f"Generating plan for topic: {request.topic}")
        
        # 转换请求参数
        preferences = LearningPreferences(
            difficulty_level=request.difficulty_level,
            daily_hours=request.daily_hours,
            learning_style=request.learning_style,
            focus_areas=request.focus_areas,
            preferred_teacher=request.preferred_teacher_type
        )
        
        # 生成学习计划
        plan = await engine.generate_learning_plan(
            topic=request.topic,
            preferences=preferences,
            duration_days=request.duration_days
        )
        
        response = GeneratePlanResponse(
            success=True,
            plan_id=f"plan_{plan.topic}_{plan.created_at.timestamp()}",
            plan=plan,
            message="学习计划生成成功！"
        )
        
        logger.info(f"✅ Plan generated successfully for: {request.topic}")
        return response
        
    except Exception as e:
        logger.error(f"❌ Failed to generate plan: {e}")
        raise HTTPException(status_code=500, detail=f"计划生成失败: {str(e)}")


@app.post("/api/v1/teaching/start-session", response_model=StartSessionResponse)
async def start_teaching_session(
    request: StartSessionRequest,
    engine: IntelligentTeachingEngine = Depends(get_teaching_engine)
):
    """
    🎭 开始AI教师会话
    
    启动多AI教师协作的智能教学会话
    """
    try:
        logger.info(f"Starting session for student: {request.student_id}")
        
        preferences = LearningPreferences(
            difficulty_level=request.difficulty_level,
            daily_hours=request.daily_hours,
            learning_style=request.learning_style,
            preferred_teacher=request.preferred_teacher_type
        )
        
        # 开始教学会话
        welcome_message = await engine.start_teaching_session(
            student_id=request.student_id,
            topic=request.topic,
            preferences=preferences
        )
        
        response = StartSessionResponse(
            success=True,
            session_id=f"session_{request.student_id}_{request.topic}",
            welcome_message=welcome_message,
            current_teacher=TeacherType.CASE_DRIVEN,
            current_stage=LearningStage.WHAT
        )
        
        logger.info(f"✅ Session started for student: {request.student_id}")
        return response
        
    except Exception as e:
        logger.error(f"❌ Failed to start session: {e}")
        raise HTTPException(status_code=500, detail=f"会话启动失败: {str(e)}")


@app.post("/api/v1/teaching/continue-session", response_model=ContinueSessionResponse)
async def continue_teaching_session(
    request: ContinueSessionRequest,
    engine: IntelligentTeachingEngine = Depends(get_teaching_engine)
):
    """
    💬 继续AI教师对话
    
    与AI教师进行持续的智能对话学习
    """
    try:
        logger.info(f"Continuing session for student: {request.student_id}")
        
        # 继续教学会话
        ai_response, metadata = await engine.continue_teaching_session(
            student_id=request.student_id,
            user_input=request.user_input
        )
        
        response = ContinueSessionResponse(
            success=True,
            ai_response=ai_response,
            current_teacher=TeacherType(metadata["teacher_type"]),
            current_stage=LearningStage(metadata["current_stage"]),
            mastery_level=metadata["mastery_level"],
            session_length=metadata["session_length"],
            suggested_actions=["继续提问", "请求解释", "查看示例"]
        )
        
        logger.info(f"✅ Session continued for student: {request.student_id}")
        return response
        
    except ValueError as e:
        logger.warning(f"⚠️ Session not found: {e}")
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"❌ Failed to continue session: {e}")
        raise HTTPException(status_code=500, detail=f"对话继续失败: {str(e)}")


@app.post("/api/v1/progress/track", response_model=TrackProgressResponse)
async def track_learning_progress(
    request: TrackProgressRequest,
    tracker: FiveDimensionProgressTracker = Depends(get_progress_tracker)
):
    """
    📊 五维进度跟踪
    
    分析学习者的五维进度：知识掌握度、实践完成度、时间投入、错误模式、能力发展
    """
    try:
        logger.info(f"Tracking progress for student: {request.student_id}")
        
        # 计算五维指标
        metrics = await tracker.calculate_five_dimension_metrics(
            student_id=request.student_id,
            learning_data=request.learning_data
        )
        
        # 生成进度洞察
        insights = await tracker.generate_progress_insights(
            metrics=metrics,
            historical_metrics=request.historical_metrics or []
        )
        
        response = TrackProgressResponse(
            success=True,
            student_id=request.student_id,
            metrics=metrics,
            insights=insights,
            recommendations=insights["recommendations"],
            next_focus_areas=insights["next_focus_areas"]
        )
        
        logger.info(f"✅ Progress tracked for student: {request.student_id}")
        return response
        
    except Exception as e:
        logger.error(f"❌ Failed to track progress: {e}")
        raise HTTPException(status_code=500, detail=f"进度跟踪失败: {str(e)}")


@app.post("/api/v1/resources/process", response_model=ProcessResourceResponse)
async def process_learning_resource(
    request: ProcessResourceRequest,
    background_tasks: BackgroundTasks
):
    """
    📚 多模态资源处理
    
    智能处理PDF、视频、代码等学习资源，提取关键信息
    """
    try:
        logger.info(f"Processing resource: {request.resource_type}")
        
        # 这里应该调用资源处理引擎
        # 暂时返回模拟响应
        processed_content = {
            "key_concepts": ["概念1", "概念2", "概念3"],
            "code_examples": ["示例代码1", "示例代码2"],
            "practice_exercises": ["练习1", "练习2"],
            "difficulty_level": "intermediate"
        }
        
        response = ProcessResourceResponse(
            success=True,
            resource_id=f"resource_{request.resource_type}_{hash(request.content)}",
            processed_content=processed_content,
            extraction_quality=0.85,
            processing_time=2.5
        )
        
        logger.info(f"✅ Resource processed: {request.resource_type}")
        return response
        
    except Exception as e:
        logger.error(f"❌ Failed to process resource: {e}")
        raise HTTPException(status_code=500, detail=f"资源处理失败: {str(e)}")


@app.get("/api/v1/teaching/teachers")
async def get_available_teachers():
    """获取可用的AI教师类型"""
    return {
        "teachers": [
            {
                "type": "socratic",
                "name": "苏格拉底式教师",
                "description": "通过连续追问引导深入思考",
                "best_for": ["概念理解", "批判性思维", "逻辑推理"]
            },
            {
                "type": "case_driven", 
                "name": "案例驱动教师",
                "description": "基于真实项目场景进行教学",
                "best_for": ["实践技能", "项目经验", "应用能力"]
            },
            {
                "type": "gamified",
                "name": "游戏化教师", 
                "description": "通过游戏化元素激发学习兴趣",
                "best_for": ["学习动机", "趣味学习", "成就感"]
            }
        ]
    }


@app.get("/api/v1/progress/dimensions")
async def get_progress_dimensions():
    """获取五维进度跟踪的维度说明"""
    return {
        "dimensions": [
            {
                "name": "knowledge_mastery",
                "display_name": "知识掌握度",
                "description": "基于WWH框架的概念理解程度",
                "weight": 0.25
            },
            {
                "name": "practice_completion",
                "display_name": "实践完成度", 
                "description": "代码练习和项目完成情况",
                "weight": 0.20
            },
            {
                "name": "time_investment",
                "display_name": "时间投入效率",
                "description": "学习时间的有效利用程度",
                "weight": 0.15
            },
            {
                "name": "error_pattern_score",
                "display_name": "错误模式改善度",
                "description": "错误类型和频率的改善情况",
                "weight": 0.20
            },
            {
                "name": "ability_development",
                "display_name": "能力发展指数",
                "description": "记忆、理解、应用等能力的发展",
                "weight": 0.20
            }
        ]
    }


# 异常处理
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """HTTP异常处理器"""
    logger.error(f"HTTP Exception: {exc.status_code} - {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": exc.detail, "status_code": exc.status_code}
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """通用异常处理器"""
    logger.error(f"Unexpected error: {exc}")
    return JSONResponse(
        status_code=500,
        content={"error": "Internal server error", "status_code": 500}
    )


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
