@using EduSynapse.MAUI.ViewModels
@using EduSynapse.MAUI.Models
@using MudBlazor
@inject ProgressViewModel ViewModel

<div>
    <!-- 日历头部 -->
    <div class="d-flex justify-space-between align-center mb-4">
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.CalendarMonth" Class="mr-2" />
            学习日历 - @ViewModel.SelectedDate.ToString("yyyy年MM月")
        </MudText>
        
        <div class="d-flex align-center gap-2">
            <MudIconButton Icon="@Icons.Material.Filled.ChevronLeft"
                           Color="Color.Primary"
                           OnClick="@(() => ViewModel.ChangeMonthCommand.ExecuteAsync(-1))" />
            
            <MudButton Variant="Variant.Text" 
                       Color="Color.Primary"
                       OnClick="@(() => GoToCurrentMonth())">
                今天
            </MudButton>
            
            <MudIconButton Icon="@Icons.Material.Filled.ChevronRight"
                           Color="Color.Primary"
                           OnClick="@(() => ViewModel.ChangeMonthCommand.ExecuteAsync(1))" />
        </div>
    </div>
    
    <!-- 日历网格 -->
    <MudCard Elevation="2" Class="pa-4">
        <!-- 星期标题 -->
        <div class="calendar-grid">
            <div class="calendar-header">
                <div class="day-header">日</div>
                <div class="day-header">一</div>
                <div class="day-header">二</div>
                <div class="day-header">三</div>
                <div class="day-header">四</div>
                <div class="day-header">五</div>
                <div class="day-header">六</div>
            </div>
            
            <!-- 日历天数 -->
            <div class="calendar-body">
                @{
                    var firstDay = new DateTime(ViewModel.SelectedDate.Year, ViewModel.SelectedDate.Month, 1);
                    var lastDay = firstDay.AddMonths(1).AddDays(-1);
                    var startDate = firstDay.AddDays(-(int)firstDay.DayOfWeek);
                    var endDate = lastDay.AddDays(6 - (int)lastDay.DayOfWeek);
                }
                
                @for (var date = startDate; date <= endDate; date = date.AddDays(1))
                {
                    var currentDate = date;
                    var isCurrentMonth = currentDate.Month == ViewModel.SelectedDate.Month;
                    var isToday = currentDate.Date == DateTime.Today;
                    var calendarDay = ViewModel.CalendarData.FirstOrDefault(d => d.Date.Date == currentDate.Date);
                    var hasProgress = calendarDay != null;
                    
                    <div class="calendar-day @(isCurrentMonth ? "current-month" : "other-month") @(isToday ? "today" : "") @(hasProgress ? "has-progress" : "")"
                         @onclick="@(() => SelectDate(currentDate))">
                        
                        <div class="day-number">@currentDate.Day</div>
                        
                        @if (hasProgress && calendarDay != null)
                        {
                            <!-- 学习进度指示器 -->
                            <div class="progress-indicator">
                                <div class="mastery-circle" style="background: @GetMasteryColor(calendarDay.OverallMastery)">
                                    <span class="mastery-text">@calendarDay.OverallMastery.ToString("F0")</span>
                                </div>
                                
                                <!-- 学习时间 -->
                                <div class="time-indicator">
                                    @(calendarDay.TimeSpent / 60.0).ToString("F1")h
                                </div>
                                
                                <!-- 心情指示器 -->
                                @if (calendarDay.MoodScore.HasValue)
                                {
                                    <div class="mood-indicator">
                                        @GetMoodEmoji(calendarDay.MoodScore.Value)
                                    </div>
                                }
                                
                                <!-- 笔记指示器 -->
                                @if (calendarDay.HasNotes)
                                {
                                    <MudIcon Icon="@Icons.Material.Filled.Notes" 
                                             Size="Size.Small" 
                                             Class="notes-indicator" />
                                }
                            </div>
                        }
                        else if (isCurrentMonth)
                        {
                            <!-- 未学习的当月日期 -->
                            <div class="no-progress">
                                <MudIcon Icon="@Icons.Material.Filled.Add" 
                                         Size="Size.Small" 
                                         Color="Color.Tertiary" 
                                         Class="add-icon" />
                            </div>
                        }
                    </div>
                }
            </div>
        </div>
    </MudCard>
    
    <!-- 日历图例 -->
    <MudCard Elevation="1" Class="pa-4 mt-4">
        <MudText Typo="Typo.subtitle1" Class="mb-3">图例说明</MudText>
        
        <MudGrid>
            <MudItem xs="12" sm="6" md="3">
                <div class="d-flex align-center mb-2">
                    <div class="legend-circle" style="background: #4caf50;"></div>
                    <MudText Typo="Typo.body2" Class="ml-2">优秀 (80%+)</MudText>
                </div>
            </MudItem>
            
            <MudItem xs="12" sm="6" md="3">
                <div class="d-flex align-center mb-2">
                    <div class="legend-circle" style="background: #2196f3;"></div>
                    <MudText Typo="Typo.body2" Class="ml-2">良好 (60-79%)</MudText>
                </div>
            </MudItem>
            
            <MudItem xs="12" sm="6" md="3">
                <div class="d-flex align-center mb-2">
                    <div class="legend-circle" style="background: #ff9800;"></div>
                    <MudText Typo="Typo.body2" Class="ml-2">一般 (40-59%)</MudText>
                </div>
            </MudItem>
            
            <MudItem xs="12" sm="6" md="3">
                <div class="d-flex align-center mb-2">
                    <div class="legend-circle" style="background: #f44336;"></div>
                    <MudText Typo="Typo.body2" Class="ml-2">需改进 (&lt;40%)</MudText>
                </div>
            </MudItem>
        </MudGrid>
        
        <MudDivider Class="my-3" />
        
        <div class="d-flex align-center flex-wrap gap-4">
            <div class="d-flex align-center">
                <MudIcon Icon="@Icons.Material.Filled.Notes" Size="Size.Small" Class="mr-1" />
                <MudText Typo="Typo.body2">有学习笔记</MudText>
            </div>
            
            <div class="d-flex align-center">
                <MudText Typo="Typo.body2" Class="mr-1">😊</MudText>
                <MudText Typo="Typo.body2">学习心情</MudText>
            </div>
            
            <div class="d-flex align-center">
                <MudText Typo="Typo.body2" Class="mr-1">2.5h</MudText>
                <MudText Typo="Typo.body2">学习时长</MudText>
            </div>
        </div>
    </MudCard>
    
    <!-- 选中日期详情 -->
    @if (selectedDateDetails != null)
    {
        <MudDialog IsVisible="@showDateDetails"
                   IsVisibleChanged="@((bool val) => showDateDetails = val)"
                   Options="dialogOptions">
            <DialogContent>
                <MudText Typo="Typo.h6" Class="mb-4">
                    @selectedDateDetails.Date.ToString("yyyy年MM月dd日") 学习详情
                </MudText>
                
                <MudGrid>
                    <MudItem xs="12" sm="6">
                        <MudCard Elevation="1" Class="pa-3">
                            <MudText Typo="Typo.subtitle1" Class="mb-2">掌握度</MudText>
                            <MudProgressCircular Color="Color.Primary" 
                                                 Size="Size.Medium" 
                                                 Value="@selectedDateDetails.OverallMastery">
                                @selectedDateDetails.OverallMastery.ToString("F0")%
                            </MudProgressCircular>
                        </MudCard>
                    </MudItem>
                    
                    <MudItem xs="12" sm="6">
                        <MudCard Elevation="1" Class="pa-3">
                            <MudText Typo="Typo.subtitle1" Class="mb-2">学习时长</MudText>
                            <MudText Typo="Typo.h5" Color="Color.Primary">
                                @(selectedDateDetails.TimeSpent / 60.0).ToString("F1") 小时
                            </MudText>
                        </MudCard>
                    </MudItem>
                    
                    @if (selectedDateDetails.MoodScore.HasValue)
                    {
                        <MudItem xs="12" sm="6">
                            <MudCard Elevation="1" Class="pa-3">
                                <MudText Typo="Typo.subtitle1" Class="mb-2">学习心情</MudText>
                                <MudText Typo="Typo.h5">
                                    @GetMoodEmoji(selectedDateDetails.MoodScore.Value) @GetMoodText(selectedDateDetails.MoodScore.Value)
                                </MudText>
                            </MudCard>
                        </MudItem>
                    }
                    
                    @if (selectedDateDetails.DifficultyRating.HasValue)
                    {
                        <MudItem xs="12" sm="6">
                            <MudCard Elevation="1" Class="pa-3">
                                <MudText Typo="Typo.subtitle1" Class="mb-2">内容难度</MudText>
                                <MudRating ReadOnly="true" 
                                           SelectedValue="@selectedDateDetails.DifficultyRating.Value"
                                           MaxValue="5" />
                            </MudCard>
                        </MudItem>
                    }
                </MudGrid>
            </DialogContent>
            
            <DialogActions>
                <MudButton OnClick="@(() => showDateDetails = false)">关闭</MudButton>
                <MudButton Color="Color.Primary" 
                           Variant="Variant.Filled"
                           OnClick="@(() => EditDateProgress())">
                    编辑进度
                </MudButton>
            </DialogActions>
        </MudDialog>
    }
</div>

<style>
    .calendar-grid {
        width: 100%;
    }
    
    .calendar-header {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 1px;
        margin-bottom: 8px;
    }
    
    .day-header {
        text-align: center;
        font-weight: 600;
        padding: 12px 4px;
        color: #666;
        background-color: #f5f5f5;
        border-radius: 4px;
    }
    
    .calendar-body {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 2px;
    }
    
    .calendar-day {
        aspect-ratio: 1;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative;
        display: flex;
        flex-direction: column;
        background: white;
    }
    
    .calendar-day:hover {
        border-color: #1976d2;
        box-shadow: 0 2px 8px rgba(25, 118, 210, 0.2);
    }
    
    .calendar-day.other-month {
        opacity: 0.3;
        background: #fafafa;
    }
    
    .calendar-day.today {
        border-color: #1976d2;
        border-width: 2px;
        background: #e3f2fd;
    }
    
    .calendar-day.has-progress {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }
    
    .day-number {
        font-weight: 600;
        font-size: 14px;
        margin-bottom: 4px;
    }
    
    .progress-indicator {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 2px;
    }
    
    .mastery-circle {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 10px;
        margin-bottom: 2px;
    }
    
    .mastery-text {
        font-size: 9px;
    }
    
    .time-indicator {
        font-size: 9px;
        color: #666;
        font-weight: 500;
    }
    
    .mood-indicator {
        font-size: 12px;
        margin-top: 2px;
    }
    
    .notes-indicator {
        position: absolute;
        top: 4px;
        right: 4px;
        color: #666;
    }
    
    .no-progress {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0.5;
    }
    
    .add-icon {
        opacity: 0;
        transition: opacity 0.2s ease;
    }
    
    .calendar-day:hover .add-icon {
        opacity: 1;
    }
    
    .legend-circle {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        display: inline-block;
    }
    
    .gap-2 > * {
        margin: 4px;
    }
    
    .gap-4 > * {
        margin: 8px;
    }
</style>

@code {
    private CalendarDay? selectedDateDetails;
    private bool showDateDetails = false;
    private DialogOptions dialogOptions = new() { MaxWidth = MaxWidth.Medium, FullWidth = true };
    
    private string GetMasteryColor(double mastery) => mastery switch
    {
        >= 80 => "#4caf50",
        >= 60 => "#2196f3",
        >= 40 => "#ff9800",
        _ => "#f44336"
    };
    
    private string GetMoodEmoji(int moodScore) => moodScore switch
    {
        1 => "😞",
        2 => "😕",
        3 => "😐",
        4 => "😊",
        5 => "😄",
        _ => "😐"
    };
    
    private string GetMoodText(int moodScore) => moodScore switch
    {
        1 => "很差",
        2 => "较差",
        3 => "一般",
        4 => "良好",
        5 => "很好",
        _ => "一般"
    };
    
    private void SelectDate(DateTime date)
    {
        var calendarDay = ViewModel.CalendarData.FirstOrDefault(d => d.Date.Date == date.Date);
        if (calendarDay != null)
        {
            selectedDateDetails = calendarDay;
            showDateDetails = true;
        }
        else if (date.Month == ViewModel.SelectedDate.Month)
        {
            // 如果是当月但没有记录，可以跳转到记录页面
            // 这里可以实现跳转逻辑
        }
    }
    
    private void GoToCurrentMonth()
    {
        ViewModel.SelectedDate = DateTime.Today;
        ViewModel.ChangeMonthCommand.ExecuteAsync(0);
    }
    
    private void EditDateProgress()
    {
        if (selectedDateDetails != null)
        {
            ViewModel.SelectDayCommand.ExecuteAsync(selectedDateDetails.DayNumber);
            showDateDetails = false;
            // 切换到记录进度标签页
        }
    }
}
