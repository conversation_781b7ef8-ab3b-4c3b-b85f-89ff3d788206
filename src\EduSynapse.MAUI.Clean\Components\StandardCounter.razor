@page "/counter"

<h1>🧪 Blazor Counter Test</h1>

<div style="padding: 20px; border: 2px solid #007bff; border-radius: 8px; margin: 10px;">
    <h3>📊 计数器状态</h3>
    <p role="status" style="font-size: 18px; font-weight: bold; color: #007bff;">
        当前计数: @currentCount
    </p>

    <h3>🔘 事件测试</h3>
    <button class="btn btn-primary" @onclick="IncrementCount" style="margin: 5px;">
        🚀 点击增加计数
    </button>

    <button class="btn btn-secondary" @onclick="ResetCount" style="margin: 5px;">
        🔄 重置计数
    </button>

    <h3>📝 调试信息</h3>
    <p style="font-size: 12px; color: #666;">
        组件渲染时间: @renderTime<br/>
        最后点击时间: @lastClickTime<br/>
        点击次数: @clickCount
    </p>
</div>

@code {
    private int currentCount = 0;
    private int clickCount = 0;
    private string renderTime = "";
    private string lastClickTime = "未点击";

    protected override void OnInitialized()
    {
        renderTime = DateTime.Now.ToString("HH:mm:ss.fff");
        System.Diagnostics.Debug.WriteLine($"🎯 StandardCounter 组件初始化完成: {renderTime}");
    }

    private void IncrementCount()
    {
        System.Diagnostics.Debug.WriteLine($"🔥 IncrementCount 方法被调用！当前计数: {currentCount}");

        currentCount++;
        clickCount++;
        lastClickTime = DateTime.Now.ToString("HH:mm:ss.fff");

        System.Diagnostics.Debug.WriteLine($"✅ 计数已更新: {currentCount}, 点击次数: {clickCount}");

        // 强制重新渲染
        StateHasChanged();
    }

    private void ResetCount()
    {
        System.Diagnostics.Debug.WriteLine($"🔄 ResetCount 方法被调用！");

        currentCount = 0;
        clickCount = 0;
        lastClickTime = DateTime.Now.ToString("HH:mm:ss.fff");

        System.Diagnostics.Debug.WriteLine($"✅ 计数已重置");

        // 强制重新渲染
        StateHasChanged();
    }
}
