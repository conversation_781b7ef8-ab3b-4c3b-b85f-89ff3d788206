@page "/learning-reminder"
@using EduSynapse.MAUI.Services
@using EduSynapse.MAUI.Models
@inject StorageService StorageService
@inject IJSRuntime JSRuntime

<div class="container-fluid p-4">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="mb-3">⏰ 智能学习提醒</h2>
            <p class="text-muted">设置个性化的学习提醒，养成良好的学习习惯</p>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center p-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在加载提醒设置...</p>
        </div>
    }
    else
    {
        <!-- 今日学习状态 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card @GetTodayStatusCardClass()">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h5 class="text-white mb-2">📅 今日学习状态</h5>
                                <p class="text-white mb-2">@GetTodayStatusMessage()</p>
                                <div class="d-flex gap-2">
                                    @if (!hasTodayRecord)
                                    {
                                        <span class="badge bg-light text-dark">还未开始今日学习</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-light text-dark">已学习 @todayStudyHours.ToString("F1") 小时</span>
                                        <span class="badge bg-light text-dark">心情评分 @todayMoodScore</span>
                                    }
                                </div>
                            </div>
                            <div class="col-md-4 text-md-end">
                                @if (!hasTodayRecord)
                                {
                                    <button class="btn btn-light" @onclick="StartTodayStudy">
                                        🚀 开始今日学习
                                    </button>
                                }
                                else
                                {
                                    <button class="btn btn-outline-light" @onclick="ViewTodayProgress">
                                        📊 查看今日进度
                                    </button>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 智能提醒设置 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">🔔 提醒设置</h6>
                    </div>
                    <div class="card-body">
                        <form @onsubmit="SaveReminderSettings" @onsubmit:preventDefault="true">
                            <div class="mb-3">
                                <label class="form-label">启用学习提醒</label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" @bind="reminderSettings.IsEnabled" id="enableReminder">
                                    <label class="form-check-label" for="enableReminder">
                                        @(reminderSettings.IsEnabled ? "已启用" : "已禁用")
                                    </label>
                                </div>
                            </div>

                            @if (reminderSettings.IsEnabled)
                            {
                                <div class="mb-3">
                                    <label class="form-label">提醒时间</label>
                                    <input type="text" class="form-control" @bind="reminderTimeString" placeholder="09:00" />
                                    <small class="text-muted">格式：HH:mm（如 09:00）</small>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">提醒频率</label>
                                    <select class="form-select" @bind="reminderSettings.Frequency">
                                        <option value="daily">每日提醒</option>
                                        <option value="weekdays">工作日提醒</option>
                                        <option value="custom">自定义</option>
                                    </select>
                                </div>

                                @if (reminderSettings.Frequency == "custom")
                                {
                                    <div class="mb-3">
                                        <label class="form-label">自定义提醒日期</label>
                                        <div class="d-flex flex-wrap gap-2">
                                            @foreach (var day in daysOfWeek)
                                            {
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" 
                                                           @bind="day.IsSelected" id="<EMAIL>">
                                                    <label class="form-check-label" for="<EMAIL>">
                                                        @day.Name
                                                    </label>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                }

                                <div class="mb-3">
                                    <label class="form-label">提醒消息</label>
                                    <textarea class="form-control" rows="3" @bind="reminderSettings.Message" 
                                              placeholder="输入自定义提醒消息..."></textarea>
                                </div>
                            }

                            <button type="submit" class="btn btn-primary" disabled="@isSaving">
                                @if (isSaving)
                                {
                                    <span class="spinner-border spinner-border-sm me-2"></span>
                                    <span>保存中...</span>
                                }
                                else
                                {
                                    <span>💾 保存设置</span>
                                }
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">📈 学习习惯分析</h6>
                    </div>
                    <div class="card-body">
                        @if (habitAnalysis != null)
                        {
                            <div class="mb-3">
                                <small class="text-muted">学习连续性</small>
                                <div class="progress mb-2">
                                    <div class="progress-bar @GetStreakProgressClass()" 
                                         style="width: @GetStreakPercentage()%"></div>
                                </div>
                                <small>连续学习 @habitAnalysis.StudyStreak 天 / 目标 @habitAnalysis.StreakGoal 天</small>
                            </div>

                            <div class="mb-3">
                                <small class="text-muted">本周学习完成度</small>
                                <div class="progress mb-2">
                                    <div class="progress-bar bg-info" 
                                         style="width: @habitAnalysis.WeeklyCompletionRate%"></div>
                                </div>
                                <small>@habitAnalysis.WeeklyCompletionRate.ToString("F0")% 完成</small>
                            </div>

                            <div class="mb-3">
                                <small class="text-muted">最佳学习时段</small>
                                <p class="mb-1"><strong>@habitAnalysis.BestStudyTime</strong></p>
                                <small class="text-muted">基于历史学习数据分析</small>
                            </div>

                            <div class="alert alert-info">
                                <strong>💡 个性化建议</strong><br/>
                                @habitAnalysis.Recommendation
                            </div>
                        }
                        else
                        {
                            <div class="text-center p-3">
                                <i class="fas fa-chart-bar fa-2x text-muted mb-2"></i>
                                <p class="text-muted">需要更多学习数据来生成习惯分析</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近提醒历史 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">📋 提醒历史</h6>
                        <button class="btn btn-sm btn-outline-secondary" @onclick="ClearReminderHistory">
                            🗑️ 清空历史
                        </button>
                    </div>
                    <div class="card-body">
                        @if (reminderHistory.Any())
                        {
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>时间</th>
                                            <th>类型</th>
                                            <th>消息</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var reminder in reminderHistory.Take(10))
                                        {
                                            <tr>
                                                <td>@reminder.Timestamp.ToString("MM-dd HH:mm")</td>
                                                <td>
                                                    <span class="badge @GetReminderTypeBadgeClass(reminder.Type)">
                                                        @reminder.Type
                                                    </span>
                                                </td>
                                                <td>@reminder.Message</td>
                                                <td>
                                                    <span class="badge @GetReminderStatusBadgeClass(reminder.Status)">
                                                        @reminder.Status
                                                    </span>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                        else
                        {
                            <div class="text-center p-3">
                                <p class="text-muted">暂无提醒历史</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h6 class="mb-3">🚀 快速操作</h6>
                        <div class="d-flex flex-wrap gap-2">
                            <button class="btn btn-outline-primary" @onclick="TestReminder">
                                🔔 测试提醒
                            </button>
                            <button class="btn btn-outline-success" @onclick="CreateStudyPlan">
                                📚 创建学习计划
                            </button>
                            <button class="btn btn-outline-info" @onclick="ViewAnalytics">
                                📊 查看分析
                            </button>
                            <button class="btn btn-outline-warning" @onclick="ExportSettings">
                                📤 导出设置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- 操作结果提示 -->
    @if (!string.IsNullOrEmpty(resultMessage))
    {
        <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1050;">
            <div class="toast show" role="alert">
                <div class="toast-header">
                    <strong class="me-auto">@(isSuccess ? "✅ 成功" : "❌ 错误")</strong>
                    <button type="button" class="btn-close" @onclick="ClearResult"></button>
                </div>
                <div class="toast-body">
                    @resultMessage
                </div>
            </div>
        </div>
    }
</div>

@code {
    private bool isLoading = false;
    private bool isSuccess = false;
    private string resultMessage = "";
    private bool isSaving = false;

    private ReminderSettings reminderSettings = new();
    private string reminderTimeString = "09:00";
    private List<DayOfWeekOption> daysOfWeek = new();
    private HabitAnalysis? habitAnalysis = null;
    private List<ReminderHistoryItem> reminderHistory = new();

    // 今日学习状态
    private bool hasTodayRecord = false;
    private double todayStudyHours = 0;
    private int todayMoodScore = 0;

    protected override async Task OnInitializedAsync()
    {
        InitializeDaysOfWeek();
        await LoadData();
    }

    private void InitializeDaysOfWeek()
    {
        daysOfWeek = new List<DayOfWeekOption>
        {
            new() { Name = "周一", Value = "Monday", IsSelected = true },
            new() { Name = "周二", Value = "Tuesday", IsSelected = true },
            new() { Name = "周三", Value = "Wednesday", IsSelected = true },
            new() { Name = "周四", Value = "Thursday", IsSelected = true },
            new() { Name = "周五", Value = "Friday", IsSelected = true },
            new() { Name = "周六", Value = "Saturday", IsSelected = false },
            new() { Name = "周日", Value = "Sunday", IsSelected = false }
        };
    }

    private async Task LoadData()
    {
        isLoading = true;
        try
        {
            // 加载提醒设置
            reminderSettings = await StorageService.LoadAsync<ReminderSettings>("reminder_settings") ?? new ReminderSettings();
            reminderTimeString = reminderSettings.ReminderTime.ToString(@"hh\:mm");

            // 加载提醒历史
            reminderHistory = await StorageService.LoadAsync<List<ReminderHistoryItem>>("reminder_history") ?? new List<ReminderHistoryItem>();

            // 加载今日学习状态
            await LoadTodayStudyStatus();

            // 生成习惯分析
            await GenerateHabitAnalysis();

            System.Diagnostics.Debug.WriteLine($"✅ 加载提醒数据完成");
        }
        catch (Exception ex)
        {
            ShowMessage($"加载数据失败: {ex.Message}", false);
            System.Diagnostics.Debug.WriteLine($"❌ 加载数据失败: {ex}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task LoadTodayStudyStatus()
    {
        try
        {
            var allPlans = await StorageService.LoadAsync<List<LearningPlan>>("learning_plans") ?? new List<LearningPlan>();
            var activePlans = allPlans.Where(p => p.Status == "active").ToList();

            double totalHours = 0;
            int totalMoodScore = 0;
            int recordCount = 0;

            foreach (var plan in activePlans)
            {
                var storageKey = $"progress_records_{plan.Id}";
                var records = await StorageService.LoadAsync<List<ProgressRecord>>(storageKey) ?? new List<ProgressRecord>();
                var todayRecord = records.FirstOrDefault(r => r.Date.Date == DateTime.Today);

                if (todayRecord != null)
                {
                    totalHours += todayRecord.StudyHours;
                    totalMoodScore += todayRecord.MoodScore;
                    recordCount++;
                }
            }

            hasTodayRecord = recordCount > 0;
            todayStudyHours = totalHours;
            todayMoodScore = recordCount > 0 ? totalMoodScore / recordCount : 0;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ 加载今日学习状态失败: {ex}");
        }
    }

    private async Task GenerateHabitAnalysis()
    {
        try
        {
            var allPlans = await StorageService.LoadAsync<List<LearningPlan>>("learning_plans") ?? new List<LearningPlan>();
            var allRecords = new List<ProgressRecord>();

            foreach (var plan in allPlans)
            {
                var storageKey = $"progress_records_{plan.Id}";
                var records = await StorageService.LoadAsync<List<ProgressRecord>>(storageKey) ?? new List<ProgressRecord>();
                allRecords.AddRange(records);
            }

            if (allRecords.Count < 3) // 需要至少3条记录才能分析
            {
                habitAnalysis = null;
                return;
            }

            // 计算学习连续性
            var studyStreak = CalculateStudyStreak(allRecords);

            // 计算本周完成度
            var weekStart = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek);
            var weekRecords = allRecords.Where(r => r.Date >= weekStart && r.Date <= DateTime.Today).ToList();
            var weeklyCompletionRate = weekRecords.Count > 0 ? (double)weekRecords.Count(r => r.StudyHours > 0) / 7 * 100 : 0;

            // 分析最佳学习时段（基于心情评分）
            var bestStudyTime = AnalyzeBestStudyTime(allRecords);

            // 生成个性化建议
            var recommendation = GenerateRecommendation(studyStreak, weeklyCompletionRate, allRecords);

            habitAnalysis = new HabitAnalysis
            {
                StudyStreak = studyStreak,
                StreakGoal = 30,
                WeeklyCompletionRate = weeklyCompletionRate,
                BestStudyTime = bestStudyTime,
                Recommendation = recommendation
            };
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ 生成习惯分析失败: {ex}");
            habitAnalysis = null;
        }
    }

    private int CalculateStudyStreak(List<ProgressRecord> records)
    {
        var streak = 0;
        var currentDate = DateTime.Today;

        for (int i = 0; i < 30; i++)
        {
            var checkDate = currentDate.AddDays(-i);
            var hasRecord = records.Any(r => r.Date.Date == checkDate && r.StudyHours > 0);

            if (hasRecord)
            {
                streak++;
            }
            else
            {
                break;
            }
        }

        return streak;
    }

    private string AnalyzeBestStudyTime(List<ProgressRecord> records)
    {
        // 简化分析：基于记录时间分布
        var morningRecords = records.Count(r => r.Date.Hour < 12);
        var afternoonRecords = records.Count(r => r.Date.Hour >= 12 && r.Date.Hour < 18);
        var eveningRecords = records.Count(r => r.Date.Hour >= 18);

        if (morningRecords >= afternoonRecords && morningRecords >= eveningRecords)
            return "上午 (9:00-12:00)";
        else if (afternoonRecords >= eveningRecords)
            return "下午 (14:00-17:00)";
        else
            return "晚上 (19:00-22:00)";
    }

    private string GenerateRecommendation(int streak, double weeklyRate, List<ProgressRecord> records)
    {
        if (streak >= 7 && weeklyRate >= 80)
            return "您的学习习惯非常好！建议保持当前节奏，可以考虑增加学习内容的深度。";
        else if (streak < 3)
            return "建议设置固定的学习时间，每天至少学习30分钟来建立学习习惯。";
        else if (weeklyRate < 50)
            return "本周学习频率较低，建议调整学习计划，设置更容易达成的小目标。";
        else
            return "学习进展不错，建议继续保持，可以尝试增加学习时间或难度。";
    }

    private async Task SaveReminderSettings()
    {
        isSaving = true;
        try
        {
            // 解析时间字符串
            if (TimeSpan.TryParse(reminderTimeString, out var time))
            {
                reminderSettings.ReminderTime = time;
            }

            // 保存自定义日期设置
            if (reminderSettings.Frequency == "custom")
            {
                reminderSettings.CustomDays = daysOfWeek.Where(d => d.IsSelected).Select(d => d.Value).ToList();
            }

            await StorageService.SaveAsync("reminder_settings", reminderSettings);

            // 添加到历史记录
            var historyItem = new ReminderHistoryItem
            {
                Timestamp = DateTime.Now,
                Type = "设置更新",
                Message = "提醒设置已更新",
                Status = "成功"
            };
            reminderHistory.Insert(0, historyItem);
            await StorageService.SaveAsync("reminder_history", reminderHistory);

            ShowMessage("提醒设置保存成功", true);
            System.Diagnostics.Debug.WriteLine($"✅ 保存提醒设置: 启用={reminderSettings.IsEnabled}, 时间={reminderSettings.ReminderTime}");
        }
        catch (Exception ex)
        {
            ShowMessage($"保存设置失败: {ex.Message}", false);
            System.Diagnostics.Debug.WriteLine($"❌ 保存设置失败: {ex}");
        }
        finally
        {
            isSaving = false;
        }
    }

    private async Task TestReminder()
    {
        try
        {
            var testMessage = reminderSettings.IsEnabled ?
                (string.IsNullOrEmpty(reminderSettings.Message) ? "这是一个测试提醒！" : reminderSettings.Message) :
                "提醒功能当前已禁用";

            await JSRuntime.InvokeVoidAsync("alert", $"🔔 测试提醒\n\n{testMessage}");

            // 添加到历史记录
            var historyItem = new ReminderHistoryItem
            {
                Timestamp = DateTime.Now,
                Type = "测试提醒",
                Message = testMessage,
                Status = "已发送"
            };
            reminderHistory.Insert(0, historyItem);
            await StorageService.SaveAsync("reminder_history", reminderHistory);

            ShowMessage("测试提醒已发送", true);
        }
        catch (Exception ex)
        {
            ShowMessage($"发送测试提醒失败: {ex.Message}", false);
        }
    }

    private void StartTodayStudy()
    {
        // 导航到学习计划列表
        ShowMessage("正在跳转到学习计划列表...", true);
    }

    private void ViewTodayProgress()
    {
        ShowMessage($"今日已学习 {todayStudyHours:F1} 小时，心情评分 {todayMoodScore}", true);
    }

    private void CreateStudyPlan()
    {
        ShowMessage("正在跳转到学习计划创建器...", true);
    }

    private void ViewAnalytics()
    {
        ShowMessage("正在跳转到学习分析页面...", true);
    }

    private async Task ExportSettings()
    {
        try
        {
            var exportData = new
            {
                ReminderSettings = reminderSettings,
                HabitAnalysis = habitAnalysis,
                ReminderHistory = reminderHistory.Take(20),
                ExportTime = DateTime.Now
            };

            var json = System.Text.Json.JsonSerializer.Serialize(exportData, new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
            });

            await JSRuntime.InvokeVoidAsync("console.log", "提醒设置导出:", json);
            ShowMessage("设置已导出到浏览器控制台", true);
        }
        catch (Exception ex)
        {
            ShowMessage($"导出设置失败: {ex.Message}", false);
        }
    }

    private async Task ClearReminderHistory()
    {
        try
        {
            reminderHistory.Clear();
            await StorageService.SaveAsync("reminder_history", reminderHistory);
            ShowMessage("提醒历史已清空", true);
        }
        catch (Exception ex)
        {
            ShowMessage($"清空历史失败: {ex.Message}", false);
        }
    }

    // UI 辅助方法
    private string GetTodayStatusCardClass()
    {
        if (!hasTodayRecord)
            return "bg-warning";
        else if (todayStudyHours >= 2)
            return "bg-success";
        else
            return "bg-info";
    }

    private string GetTodayStatusMessage()
    {
        if (!hasTodayRecord)
            return "今天还没有开始学习，现在开始吧！";
        else if (todayStudyHours >= 2)
            return "今天的学习进展很好，继续保持！";
        else
            return "今天已经开始学习了，可以再努力一点！";
    }

    private string GetStreakProgressClass()
    {
        if (habitAnalysis == null) return "bg-secondary";

        var percentage = (double)habitAnalysis.StudyStreak / habitAnalysis.StreakGoal * 100;
        return percentage switch
        {
            >= 80 => "bg-success",
            >= 60 => "bg-info",
            >= 40 => "bg-warning",
            _ => "bg-danger"
        };
    }

    private double GetStreakPercentage()
    {
        if (habitAnalysis == null) return 0;
        return Math.Min((double)habitAnalysis.StudyStreak / habitAnalysis.StreakGoal * 100, 100);
    }

    private string GetReminderTypeBadgeClass(string type)
    {
        return type switch
        {
            "测试提醒" => "bg-info",
            "设置更新" => "bg-success",
            "学习提醒" => "bg-primary",
            _ => "bg-secondary"
        };
    }

    private string GetReminderStatusBadgeClass(string status)
    {
        return status switch
        {
            "成功" => "bg-success",
            "已发送" => "bg-info",
            "失败" => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private void ShowMessage(string message, bool success)
    {
        resultMessage = message;
        isSuccess = success;

        Task.Delay(3000).ContinueWith(_ =>
        {
            resultMessage = "";
            InvokeAsync(StateHasChanged);
        });
    }

    private void ClearResult()
    {
        resultMessage = "";
    }

    // 数据模型
    public class ReminderSettings
    {
        public bool IsEnabled { get; set; } = true;
        public TimeSpan ReminderTime { get; set; } = new TimeSpan(9, 0, 0);
        public string Frequency { get; set; } = "daily";
        public List<string> CustomDays { get; set; } = new();
        public string Message { get; set; } = "该学习了！今天的学习目标是什么？";
    }

    public class DayOfWeekOption
    {
        public string Name { get; set; } = "";
        public string Value { get; set; } = "";
        public bool IsSelected { get; set; } = false;
    }

    public class HabitAnalysis
    {
        public int StudyStreak { get; set; }
        public int StreakGoal { get; set; }
        public double WeeklyCompletionRate { get; set; }
        public string BestStudyTime { get; set; } = "";
        public string Recommendation { get; set; } = "";
    }

    public class ReminderHistoryItem
    {
        public DateTime Timestamp { get; set; }
        public string Type { get; set; } = "";
        public string Message { get; set; } = "";
        public string Status { get; set; } = "";
    }

    public class ProgressRecord
    {
        public DateTime Date { get; set; }
        public double StudyHours { get; set; }
        public string Status { get; set; } = "completed";
        public string Content { get; set; } = "";
        public int MoodScore { get; set; } = 8;
        public string Notes { get; set; } = "";
        public int PlanId { get; set; }
    }
}
