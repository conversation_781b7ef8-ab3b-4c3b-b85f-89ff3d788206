using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using EduSynapse.MAUI.Services;
using EduSynapse.MAUI.Models;

namespace EduSynapse.MAUI.ViewModels;

/// <summary>
/// 学习计划ViewModel
/// </summary>
public partial class LearningPlanViewModel : BaseViewModel
{
    [ObservableProperty]
    private string _topic = string.Empty;

    [ObservableProperty]
    private int _durationDays = 14;

    [ObservableProperty]
    private string _difficultyLevel = "medium";

    [ObservableProperty]
    private double _dailyHours = 2.0;

    [ObservableProperty]
    private string _learningStyle = "balanced";

    [ObservableProperty]
    private bool _isGenerating;

    [ObservableProperty]
    private LearningPlan? _currentPlan;

    [ObservableProperty]
    private List<LearningPlanSummary> _allPlans = new();

    [ObservableProperty]
    private string _selectedFilter = "all";

    // 选项列表
    public List<string> DifficultyOptions { get; } = new() { "easy", "medium", "hard" };
    public List<string> LearningStyleOptions { get; } = new() { "theoretical", "practical", "balanced", "project-based" };
    public List<string> FilterOptions { get; } = new() { "all", "active", "completed", "paused" };

    public LearningPlanViewModel(
        IApiService apiService,
        IStateService stateService,
        IStorageService storageService)
        : base(apiService, stateService, storageService)
    {
        Title = "学习计划管理";
    }

    public override async Task InitializeAsync()
    {
        await LoadPlansAsync();
        
        // 如果有当前计划，显示它
        if (_stateService.CurrentPlan != null)
        {
            CurrentPlan = _stateService.CurrentPlan;
        }
    }

    /// <summary>
    /// 生成学习计划
    /// </summary>
    [RelayCommand]
    private async Task GeneratePlanAsync()
    {
        if (string.IsNullOrWhiteSpace(Topic))
        {
            SetError("请输入学习主题");
            return;
        }

        IsGenerating = true;
        
        await ExecuteAsync(async () =>
        {
            var request = new PlanGenerationRequest
            {
                Topic = Topic.Trim(),
                DurationDays = DurationDays,
                DifficultyLevel = DifficultyLevel,
                DailyHours = DailyHours,
                LearningStyle = LearningStyle,
                Preferences = new Dictionary<string, object>
                {
                    ["include_projects"] = true,
                    ["focus_areas"] = new List<string>()
                }
            };

            var plan = await _apiService.GenerateLearningPlanAsync(request);
            if (plan != null)
            {
                CurrentPlan = plan;
                _stateService.CurrentPlan = plan;
                
                // 重新加载计划列表
                await LoadPlansAsync();
                
                // 清空表单
                ResetForm();
            }
            else
            {
                SetError("学习计划生成失败，请检查网络连接和API配置");
            }
        }, "生成学习计划时发生错误");

        IsGenerating = false;
    }

    /// <summary>
    /// 加载学习计划列表
    /// </summary>
    [RelayCommand]
    private async Task LoadPlansAsync()
    {
        await ExecuteAsync(async () =>
        {
            var statusFilter = SelectedFilter == "all" ? null : SelectedFilter;
            var response = await _apiService.GetLearningPlansAsync(statusFilter, limit: 50);
            
            if (response != null)
            {
                AllPlans = response.Plans;
                _stateService.LearningPlans = response.Plans;
            }
        }, "加载学习计划列表失败");
    }

    /// <summary>
    /// 选择学习计划
    /// </summary>
    [RelayCommand]
    private async Task SelectPlanAsync(LearningPlanSummary planSummary)
    {
        if (planSummary == null) return;

        await ExecuteAsync(async () =>
        {
            var plan = await _apiService.GetLearningPlanAsync(planSummary.Id);
            if (plan != null)
            {
                CurrentPlan = plan;
                _stateService.CurrentPlan = plan;
            }
        }, "获取学习计划详情失败");
    }

    /// <summary>
    /// 删除学习计划
    /// </summary>
    [RelayCommand]
    private async Task DeletePlanAsync(LearningPlanSummary planSummary)
    {
        if (planSummary == null) return;

        await ExecuteAsync(async () =>
        {
            var success = await _apiService.DeleteLearningPlanAsync(planSummary.Id);
            if (success)
            {
                // 如果删除的是当前计划，清空当前计划
                if (CurrentPlan?.Id == planSummary.Id)
                {
                    CurrentPlan = null;
                    _stateService.CurrentPlan = null;
                }
                
                // 重新加载列表
                await LoadPlansAsync();
            }
            else
            {
                SetError("删除学习计划失败");
            }
        }, "删除学习计划时发生错误");
    }

    /// <summary>
    /// 更新计划状态
    /// </summary>
    [RelayCommand]
    private async Task UpdatePlanStatusAsync(string status)
    {
        if (CurrentPlan == null) return;

        await ExecuteAsync(async () =>
        {
            var updateData = new { status };
            var updatedPlan = await _apiService.UpdateLearningPlanAsync(CurrentPlan.Id, updateData);
            
            if (updatedPlan != null)
            {
                CurrentPlan = updatedPlan;
                _stateService.CurrentPlan = updatedPlan;
                await LoadPlansAsync();
            }
        }, "更新计划状态失败");
    }

    /// <summary>
    /// 筛选变化
    /// </summary>
    [RelayCommand]
    private async Task OnFilterChangedAsync()
    {
        await LoadPlansAsync();
    }

    /// <summary>
    /// 重置表单
    /// </summary>
    [RelayCommand]
    private void ResetForm()
    {
        Topic = string.Empty;
        DurationDays = 14;
        DifficultyLevel = "medium";
        DailyHours = 2.0;
        LearningStyle = "balanced";
        ClearError();
    }

    /// <summary>
    /// 获取难度显示名称
    /// </summary>
    public string GetDifficultyDisplayName(string difficulty) => difficulty switch
    {
        "easy" => "简单",
        "medium" => "中等",
        "hard" => "困难",
        _ => "未知"
    };

    /// <summary>
    /// 获取学习风格显示名称
    /// </summary>
    public string GetLearningStyleDisplayName(string style) => style switch
    {
        "theoretical" => "理论导向",
        "practical" => "实践导向",
        "balanced" => "理论实践并重",
        "project-based" => "项目驱动",
        _ => "未知"
    };
}
