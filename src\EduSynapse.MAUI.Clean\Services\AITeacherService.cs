using System.Text.Json;
using System.Text;
using EduSynapse.MAUI.Models;

namespace EduSynapse.MAUI.Services;

/// <summary>
/// AI教师服务 - EduSynapse的核心智能教学引擎
/// 实现多种教学风格的AI教师协作系统
/// </summary>
public class AITeacherService
{
    private readonly HttpClient _httpClient;
    private readonly string _apiKey;
    private readonly string _baseUrl;

    public AITeacherService(HttpClient httpClient)
    {
        _httpClient = httpClient;
        _apiKey = Environment.GetEnvironmentVariable("OPENAI_API_KEY") ?? "";
        _baseUrl = Environment.GetEnvironmentVariable("OPENAI_API_BASE") ?? "https://api.openai.com/v1";
        
        _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_apiKey}");
    }



    /// <summary>
    /// 生成智能学习计划
    /// </summary>
    /// <param name="topic">学习主题</param>
    /// <param name="preferences">学习偏好</param>
    /// <param name="durationDays">学习天数</param>
    /// <returns>结构化学习计划</returns>
    public async Task<LearningPlan> GenerateIntelligentPlanAsync(string topic, LearningPreferences preferences, int durationDays = 14)
    {
        try
        {
            var prompt = BuildPlanGenerationPrompt(topic, preferences, durationDays);
            var response = await CallOpenAIAsync(prompt);
            
            return ParseLearningPlan(response, topic, durationDays);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ AI计划生成失败: {ex.Message}");
            return GenerateFallbackPlan(topic, durationDays);
        }
    }

    /// <summary>
    /// AI教师对话
    /// </summary>
    /// <param name="teacherType">教师类型</param>
    /// <param name="stage">学习阶段</param>
    /// <param name="topic">当前主题</param>
    /// <param name="userInput">用户输入</param>
    /// <param name="context">对话上下文</param>
    /// <returns>AI教师回复</returns>
    public async Task<TeacherResponse> ChatWithTeacherAsync(
        TeacherType teacherType, 
        LearningStage stage, 
        string topic, 
        string userInput, 
        List<ChatMessage> context)
    {
        try
        {
            var prompt = BuildTeacherPrompt(teacherType, stage, topic, userInput, context);
            var response = await CallOpenAIAsync(prompt);
            
            return new TeacherResponse
            {
                Content = response,
                TeacherType = teacherType,
                Stage = stage,
                Timestamp = DateTime.Now,
                SuggestedNextAction = GenerateNextAction(teacherType, stage, response)
            };
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ AI教师对话失败: {ex.Message}");
            return GenerateFallbackResponse(teacherType, stage, topic);
        }
    }

    /// <summary>
    /// 分析学习进度并推荐教学策略
    /// </summary>
    /// <param name="progressData">学习进度数据</param>
    /// <returns>教学策略建议</returns>
    public async Task<TeachingStrategy> AnalyzeProgressAndRecommendAsync(ProgressAnalysisData progressData)
    {
        try
        {
            var prompt = BuildProgressAnalysisPrompt(progressData);
            var response = await CallOpenAIAsync(prompt);
            
            return ParseTeachingStrategy(response, progressData);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ 进度分析失败: {ex.Message}");
            return GenerateFallbackStrategy(progressData);
        }
    }

    /// <summary>
    /// 生成智能学习回顾
    /// </summary>
    /// <param name="learningHistory">学习历史</param>
    /// <returns>智能回顾内容</returns>
    public async Task<LearningReview> GenerateIntelligentReviewAsync(LearningHistory learningHistory)
    {
        try
        {
            var prompt = BuildReviewPrompt(learningHistory);
            var response = await CallOpenAIAsync(prompt);
            
            return ParseLearningReview(response, learningHistory);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ 智能回顾生成失败: {ex.Message}");
            return GenerateFallbackReview(learningHistory);
        }
    }

    #region Private Methods

    private string BuildPlanGenerationPrompt(string topic, LearningPreferences preferences, int durationDays)
    {
        return $@"
作为EduSynapse智能教学系统的计划生成引擎，请为以下学习需求生成一个{durationDays}天的结构化学习计划：

学习主题：{topic}
学习偏好：
- 难度级别：{preferences.DifficultyLevel}
- 每日学习时间：{preferences.DailyHours}小时
- 学习风格：{preferences.LearningStyle}
- 重点关注：{preferences.FocusAreas}

请按照WWH教学框架（What-Why-How）组织内容，每天的计划应包含：
1. What（是什么）- 核心概念和定义
2. Why（为什么）- 重要性和应用场景  
3. How（怎么做）- 具体实践和代码示例

请以JSON格式返回，包含每日详细计划。";
    }

    private string BuildTeacherPrompt(TeacherType teacherType, LearningStage stage, string topic, string userInput, List<ChatMessage> context)
    {
        var teacherPersonality = teacherType switch
        {
            TeacherType.Socratic => "苏格拉底式教师：通过连续追问引导学生深入思考，不直接给出答案，而是启发学生自己发现真理。",
            TeacherType.CaseDriven => "案例驱动教师：通过真实的项目案例和实际应用场景来教学，注重实践性和可操作性。",
            TeacherType.Gamified => "游戏化教师：运用游戏化元素，通过积分、成就、挑战等方式激发学习兴趣。",
            _ => "通用教师：采用平衡的教学方式。"
        };

        var stageGuidance = stage switch
        {
            LearningStage.What => "当前处于'What'阶段，重点帮助学生理解概念的定义、特征和基本原理。",
            LearningStage.Why => "当前处于'Why'阶段，重点解释概念的重要性、历史背景和应用价值。",
            LearningStage.How => "当前处于'How'阶段，重点指导具体的实践方法和操作步骤。",
            _ => "综合教学阶段。"
        };

        var contextStr = context.Any() ? 
            $"对话历史：\n{string.Join("\n", context.TakeLast(5).Select(c => $"{c.Role}: {c.Content}"))}" : 
            "这是对话的开始。";

        return $@"
你是EduSynapse智能教学系统中的AI教师。

教师身份：{teacherPersonality}
教学阶段：{stageGuidance}
当前主题：{topic}
学生输入：{userInput}

{contextStr}

请根据你的教师身份和当前教学阶段，对学生的输入给出恰当的回应。保持你的教学风格一致性。";
    }

    private string BuildProgressAnalysisPrompt(ProgressAnalysisData progressData)
    {
        return $@"
作为EduSynapse智能教学系统的进度分析引擎，请分析以下学习数据并提供教学策略建议：

学习进度数据：
- 知识掌握度：{progressData.KnowledgeMastery}%
- 实践完成度：{progressData.PracticeCompletion}%
- 平均学习时间：{progressData.AverageStudyTime}小时/天
- 错误率趋势：{progressData.ErrorRateTrend}
- 学习连续性：{progressData.StudyConsistency}天

请提供：
1. 当前学习状态评估
2. 推荐的教学策略调整
3. 下一步学习重点
4. 个性化建议";
    }

    private string BuildReviewPrompt(LearningHistory learningHistory)
    {
        return $@"
作为EduSynapse智能教学系统的回顾生成引擎，请为以下学习历史生成智能回顾：

学习历史：
- 学习主题：{learningHistory.Topic}
- 学习天数：{learningHistory.StudyDays}
- 完成的概念：{string.Join(", ", learningHistory.CompletedConcepts)}
- 实践项目：{string.Join(", ", learningHistory.PracticeProjects)}
- 遇到的困难：{string.Join(", ", learningHistory.Difficulties)}

请生成包含以下内容的回顾：
1. 学习成果总结
2. 知识点掌握情况
3. 需要强化的领域
4. 下一步学习建议";
    }

    private async Task<string> CallOpenAIAsync(string prompt)
    {
        if (string.IsNullOrEmpty(_apiKey))
        {
            throw new InvalidOperationException("OpenAI API密钥未配置");
        }

        var requestBody = new
        {
            model = "gpt-3.5-turbo",
            messages = new[]
            {
                new { role = "user", content = prompt }
            },
            max_tokens = 2000,
            temperature = 0.7
        };

        var json = JsonSerializer.Serialize(requestBody);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        var response = await _httpClient.PostAsync($"{_baseUrl}/chat/completions", content);
        response.EnsureSuccessStatusCode();

        var responseJson = await response.Content.ReadAsStringAsync();
        var responseObj = JsonSerializer.Deserialize<JsonElement>(responseJson);
        
        return responseObj.GetProperty("choices")[0].GetProperty("message").GetProperty("content").GetString() ?? "";
    }

    private LearningPlan ParseLearningPlan(string aiResponse, string topic, int durationDays)
    {
        // 简化版解析，实际应该更复杂
        return new LearningPlan
        {
            Id = new Random().Next(1000, 9999),
            Name = $"AI生成：{topic} 学习计划",
            Topic = topic,
            Description = $"基于AI智能分析生成的{durationDays}天{topic}学习计划",
            DurationDays = durationDays,
            DifficultyLevel = "intermediate",
            DailyHours = 2.0,
            LearningStyle = "balanced",
            Status = "active",
            CreatedAt = DateTime.Now,
            AIGenerated = true,
            AIContent = aiResponse
        };
    }

    private LearningPlan GenerateFallbackPlan(string topic, int durationDays)
    {
        return new LearningPlan
        {
            Id = new Random().Next(1000, 9999),
            Name = $"标准学习计划：{topic}",
            Topic = topic,
            Description = $"标准的{durationDays}天{topic}学习计划",
            DurationDays = durationDays,
            DifficultyLevel = "intermediate",
            DailyHours = 2.0,
            LearningStyle = "balanced",
            Status = "active",
            CreatedAt = DateTime.Now,
            AIGenerated = false
        };
    }

    private TeacherResponse GenerateFallbackResponse(TeacherType teacherType, LearningStage stage, string topic)
    {
        var fallbackContent = teacherType switch
        {
            TeacherType.Socratic => $"让我们思考一下关于{topic}的问题。你认为这个概念最重要的特征是什么？",
            TeacherType.CaseDriven => $"在实际项目中，{topic}通常用于解决什么样的问题？让我们看一个具体的例子。",
            TeacherType.Gamified => $"太好了！你正在探索{topic}的世界。完成这个挑战可以获得10积分！",
            _ => $"让我们继续学习{topic}的相关内容。"
        };

        return new TeacherResponse
        {
            Content = fallbackContent,
            TeacherType = teacherType,
            Stage = stage,
            Timestamp = DateTime.Now,
            SuggestedNextAction = "继续学习"
        };
    }

    private TeachingStrategy GenerateFallbackStrategy(ProgressAnalysisData progressData)
    {
        return new TeachingStrategy
        {
            RecommendedTeacherType = TeacherType.CaseDriven,
            FocusAreas = new List<string> { "基础概念巩固", "实践练习" },
            DifficultyAdjustment = "maintain",
            EstimatedTimeToImprove = TimeSpan.FromDays(3),
            Reasoning = "基于当前进度的标准建议"
        };
    }

    private LearningReview GenerateFallbackReview(LearningHistory learningHistory)
    {
        return new LearningReview
        {
            Summary = $"在{learningHistory.StudyDays}天的学习中，你探索了{learningHistory.Topic}的相关内容。",
            Achievements = new List<string> { "坚持学习", "积极探索" },
            AreasToImprove = new List<string> { "继续练习", "深入理解" },
            NextSteps = new List<string> { "复习重点概念", "完成实践项目" },
            GeneratedAt = DateTime.Now
        };
    }

    private string GenerateNextAction(TeacherType teacherType, LearningStage stage, string response)
    {
        return teacherType switch
        {
            TeacherType.Socratic => "思考并回答问题",
            TeacherType.CaseDriven => "尝试实践案例",
            TeacherType.Gamified => "完成挑战任务",
            _ => "继续学习"
        };
    }

    private TeachingStrategy ParseTeachingStrategy(string aiResponse, ProgressAnalysisData progressData)
    {
        // 简化版解析
        return new TeachingStrategy
        {
            RecommendedTeacherType = TeacherType.CaseDriven,
            FocusAreas = new List<string> { "实践练习", "概念巩固" },
            DifficultyAdjustment = "increase",
            EstimatedTimeToImprove = TimeSpan.FromDays(5),
            Reasoning = aiResponse
        };
    }

    private LearningReview ParseLearningReview(string aiResponse, LearningHistory learningHistory)
    {
        return new LearningReview
        {
            Summary = aiResponse,
            Achievements = new List<string> { "完成学习目标" },
            AreasToImprove = new List<string> { "继续深入学习" },
            NextSteps = new List<string> { "制定下一阶段计划" },
            GeneratedAt = DateTime.Now
        };
    }

    #endregion
}
