# EduSynapse API 接口设计文档

## 📋 文档信息
| 项目 | EduSynapse 智能学习系统 API |
|------|---------------------------|
| 版本 | 2.0 |
| 日期 | 2025-07-06 |
| 状态 | 设计阶段 |
| 主要架构 | .NET MAUI Blazor Hybrid |

**说明:** 本文档描述 EduSynapse 系统的 API 设计。当前项目重点是 .NET MAUI 前端开发，API 设计作为未来扩展的参考。

**相关文档:**
- [项目需求文档](docs/project-requirements.md)
- [技术架构文档](technical-architecture.md)
- [数据库设计文档](database-design.md)

**版本更新:**
- v2.0 (2025-07-06): 调整为设计参考文档，配合 MAUI 前端架构
- v1.0 (2025-07-05): 初始 FastAPI 设计

---

## 🎯 当前项目状态

**重要说明:**
- 当前 EduSynapse 项目专注于 .NET MAUI Blazor Hybrid 桌面应用开发
- 本 API 设计文档作为未来后端服务的设计参考
- 第一阶段将实现本地数据存储和基础功能
- 后续阶段可能会实现独立的后端 API 服务

---

## 🎯 API 设计原则

### 1. RESTful 设计
- 使用标准HTTP方法 (GET, POST, PUT, DELETE)
- 资源导向的URL设计
- 统一的响应格式
- 适当的HTTP状态码

### 2. 响应格式标准
```json
{
    "success": true,
    "data": {},
    "message": "操作成功",
    "timestamp": "2025-07-05T10:30:00Z",
    "request_id": "uuid-string"
}
```

### 3. 错误处理格式
```json
{
    "success": false,
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "输入参数验证失败",
        "details": ["topic字段不能为空"]
    },
    "timestamp": "2025-07-05T10:30:00Z",
    "request_id": "uuid-string"
}
```

---

## 📚 学习计划管理 API

### 1. 生成学习计划
```http
POST /api/learning-plans/generate
Content-Type: application/json

{
    "topic": "Python面向对象编程",
    "style": "practical",
    "duration_days": 14,
    "difficulty_level": "medium",
    "daily_hours": 2.5,
    "preferences": {
        "include_projects": true,
        "focus_areas": ["设计模式", "实际应用"]
    }
}
```

**响应示例:**
```json
{
    "success": true,
    "data": {
        "plan_id": 123,
        "topic": "Python面向对象编程",
        "duration_days": 14,
        "wwh_structure": {
            "what": {
                "core_concepts": ["类与对象", "继承", "多态", "封装"],
                "key_features": ["属性", "方法", "构造函数"],
                "definitions": {
                    "类": "对象的蓝图或模板",
                    "对象": "类的实例"
                }
            },
            "why": {
                "historical_context": "面向对象编程的发展历史...",
                "practical_importance": "代码重用性和维护性...",
                "learning_motivation": "现代软件开发的基础..."
            },
            "how": {
                "practice_projects": ["图书管理系统", "学生成绩管理"],
                "code_examples": ["基础类定义", "继承示例"],
                "exercises": ["设计动物类层次", "实现银行账户系统"]
            }
        },
        "daily_breakdown": [
            {
                "day": 1,
                "focus": "what",
                "tasks": ["理解类和对象概念", "编写第一个类"],
                "estimated_hours": 2.5,
                "resources": ["tutorial_1.pdf", "video_intro.mp4"]
            }
        ]
    },
    "message": "学习计划生成成功"
}
```

### 2. 获取学习计划列表
```http
GET /api/learning-plans?status=active&limit=10&offset=0
```

### 3. 获取学习计划详情
```http
GET /api/learning-plans/{plan_id}
```

### 4. 更新学习计划
```http
PUT /api/learning-plans/{plan_id}
Content-Type: application/json

{
    "duration_days": 21,
    "daily_hours": 3.0,
    "status": "active"
}
```

### 5. 删除学习计划
```http
DELETE /api/learning-plans/{plan_id}
```

---

## 📊 学习进度管理 API

### 1. 记录学习进度
```http
POST /api/learning-progress
Content-Type: application/json

{
    "plan_id": 123,
    "day_number": 1,
    "what_mastery": 85.5,
    "why_mastery": 70.0,
    "how_mastery": 60.0,
    "time_spent": 150,
    "focus_time": 120,
    "break_count": 3,
    "notes": "今天学习了类的基本概念，理解了封装的重要性...",
    "mood_score": 4,
    "difficulty_rating": 3
}
```

### 2. 获取进度统计
```http
GET /api/learning-progress/stats/{plan_id}
```

**响应示例:**
```json
{
    "success": true,
    "data": {
        "plan_id": 123,
        "total_days": 14,
        "completed_days": 5,
        "completion_percentage": 35.7,
        "average_mastery": {
            "what": 78.5,
            "why": 65.2,
            "how": 58.8,
            "overall": 67.5
        },
        "total_time_spent": 750,
        "average_daily_time": 150,
        "mood_trend": [4, 3, 4, 5, 4],
        "difficulty_trend": [2, 3, 3, 4, 3]
    }
}
```

### 3. 获取学习日历
```http
GET /api/learning-progress/calendar/{plan_id}?month=2025-07
```

---

## 🤖 AI教师交互 API

### 1. 开始AI对话会话
```http
POST /api/ai-teacher/sessions
Content-Type: application/json

{
    "plan_id": 123,
    "teacher_type": "socratic",
    "topic": "Python继承机制",
    "context": "我对多重继承的概念不太理解"
}
```

**响应示例:**
```json
{
    "success": true,
    "data": {
        "session_id": "ai_session_456",
        "teacher_type": "socratic",
        "teacher_persona": {
            "name": "苏格拉底老师",
            "style": "通过提问引导思考",
            "greeting": "你好！我是苏格拉底老师，让我们通过问答来深入理解继承机制。"
        },
        "initial_message": "很好的问题！在我们深入多重继承之前，你能告诉我什么是单一继承吗？"
    }
}
```

### 2. 发送消息给AI教师
```http
POST /api/ai-teacher/sessions/{session_id}/messages
Content-Type: application/json

{
    "message": "单一继承就是一个子类只继承一个父类",
    "message_type": "text"
}
```

### 3. 获取对话历史
```http
GET /api/ai-teacher/sessions/{session_id}/messages?limit=20
```

### 4. 结束对话会话
```http
POST /api/ai-teacher/sessions/{session_id}/end
Content-Type: application/json

{
    "user_satisfaction": 4,
    "learning_effectiveness": 5,
    "feedback": "老师的提问很有启发性"
}
```

### 5. 获取AI教师统计
```http
GET /api/ai-teacher/stats?period=30days
```

---

## 📝 学习资源管理 API

### 1. 添加学习资源
```http
POST /api/learning-resources
Content-Type: application/json

{
    "plan_id": 123,
    "resource_type": "pdf",
    "title": "Python面向对象编程指南",
    "description": "详细介绍Python OOP概念和实践",
    "url": "https://example.com/python-oop-guide.pdf",
    "wwh_category": "what",
    "priority": 8,
    "difficulty_level": "medium"
}
```

### 2. 获取资源列表
```http
GET /api/learning-resources?plan_id=123&wwh_category=what&limit=10
```

### 3. 更新资源进度
```http
PUT /api/learning-resources/{resource_id}/progress
Content-Type: application/json

{
    "completion_percentage": 75.0,
    "user_rating": 4,
    "notes": "这个资源很有帮助，例子很实用"
}
```

---

## 🔬 实践记录管理 API

### 1. 提交代码实践
```http
POST /api/practice-records
Content-Type: application/json

{
    "progress_id": 789,
    "practice_type": "coding",
    "title": "实现动物类继承",
    "description": "创建Animal基类和Dog、Cat子类",
    "code_content": "class Animal:\n    def __init__(self, name):\n        self.name = name\n    \n    def speak(self):\n        pass\n\nclass Dog(Animal):\n    def speak(self):\n        return f'{self.name} says Woof!'\n\nclass Cat(Animal):\n    def speak(self):\n        return f'{self.name} says Meow!'",
    "code_language": "python",
    "expected_output": "Buddy says Woof!\nWhiskers says Meow!"
}
```

### 2. 执行代码并获取结果
```http
POST /api/practice-records/{record_id}/execute
```

**响应示例:**
```json
{
    "success": true,
    "data": {
        "execution_result": {
            "is_successful": true,
            "actual_output": "Buddy says Woof!\nWhiskers says Meow!",
            "execution_time_ms": 45,
            "memory_usage_mb": 2.1
        },
        "feedback": {
            "correctness_score": 100,
            "code_quality_score": 85,
            "suggestions": [
                "考虑添加文档字符串",
                "可以添加类型提示"
            ]
        }
    }
}
```

### 3. 获取实践统计
```http
GET /api/practice-records/stats/{plan_id}
```

---

## 📈 数据分析 API

### 1. 获取学习仪表板数据
```http
GET /api/analytics/dashboard
```

**响应示例:**
```json
{
    "success": true,
    "data": {
        "overview": {
            "active_plans": 2,
            "total_study_hours": 45.5,
            "average_mastery": 72.3,
            "streak_days": 7
        },
        "recent_activity": [
            {
                "date": "2025-07-05",
                "study_time": 2.5,
                "mastery_gain": 8.2,
                "activities": ["学习", "实践", "AI对话"]
            }
        ],
        "mastery_trends": {
            "what": [65, 70, 75, 78, 82],
            "why": [60, 65, 68, 72, 75],
            "how": [55, 60, 65, 70, 73]
        },
        "ai_usage": {
            "total_sessions": 15,
            "favorite_teacher": "socratic",
            "total_cost": 2.45
        }
    }
}
```

### 2. 生成学习报告
```http
POST /api/analytics/reports
Content-Type: application/json

{
    "plan_id": 123,
    "report_type": "weekly",
    "format": "markdown"
}
```

### 3. 导出学习数据
```http
GET /api/analytics/export?plan_id=123&format=json&include_progress=true
```

---

## 🔧 系统管理 API

### 1. 健康检查
```http
GET /api/health
```

### 2. 系统配置
```http
GET /api/config
PUT /api/config
```

### 3. 数据库备份
```http
POST /api/admin/backup
```

---

## 🔐 认证和安全

### 1. API密钥配置
```http
POST /api/auth/api-keys
Content-Type: application/json

{
    "provider": "openai",
    "api_key": "sk-...",
    "model": "gpt-4"
}
```

### 2. 使用统计
```http
GET /api/auth/usage-stats
```

---

## 📋 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 资源创建成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 404 | 资源不存在 |
| 422 | 数据验证失败 |
| 500 | 服务器内部错误 |

---

## 🚀 API使用示例 (C#)

```csharp
// MAUI应用中的API调用示例
public class ApiService
{
    private readonly HttpClient _httpClient;
    
    public async Task<LearningPlan> GeneratePlanAsync(PlanRequest request)
    {
        var response = await _httpClient.PostAsJsonAsync(
            "/api/learning-plans/generate", 
            request
        );
        
        var result = await response.Content
            .ReadFromJsonAsync<ApiResponse<LearningPlan>>();
            
        return result.Data;
    }
}
```

这个API设计支持EduSynapse的所有核心功能，提供了完整的RESTful接口，便于MAUI前端调用和集成。
