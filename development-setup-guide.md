# EduSynapse 开发环境配置指南

## 📋 文档信息
| 项目 | EduSynapse 智能学习系统 |
|------|------------------------|
| 版本 | 2.0 |
| 日期 | 2025-07-06 |
| 主要平台 | Windows 10/11 |
| 技术栈 | .NET MAUI Blazor Hybrid |

**相关文档:**
- [技术栈学习指南](technology-stack-guide.md) - 详细的技术学习资料
- [快速参考手册](quick-reference-guide.md) - 开发过程中的速查手册
- [开发规范](docs/development-standards.md) - 代码规范和最佳实践

**版本更新:**
- v2.0 (2025-07-06): 专注于 .NET MAUI Blazor Hybrid 开发环境
- v1.0 (2025-07-05): 初始版本

---

## 📋 环境要求

### 系统要求
- **操作系统**: Windows 10 版本 1809 (Build 17763) 或更高版本
- **内存**: 8GB RAM (推荐 16GB)
- **存储**: 至少 10GB 可用空间
- **网络**: 稳定的互联网连接 (用于AI API调用)

### 必需软件
1. **Visual Studio 2022** (Community版本即可)
2. **Python 3.11+**
3. **Git** (版本控制)
4. **SQLite Browser** (数据库管理，可选)

---

## 🛠️ 安装步骤

### 1. 安装 Visual Studio 2022

#### 1.1 下载和安装
```bash
# 下载地址
https://visualstudio.microsoft.com/zh-hans/vs/community/

# 必需工作负载
- .NET Multi-platform App UI development (.NET MAUI)
- ASP.NET and web development
- .NET desktop development
```

#### 1.2 验证安装
```bash
# 打开 Visual Studio Developer Command Prompt
dotnet --version
# 应显示 8.0.x 或更高版本
```

### 2. 安装 Python 环境

#### 2.1 安装 Python
```bash
# 下载 Python 3.11+
https://www.python.org/downloads/

# 验证安装
python --version
pip --version
```

#### 2.2 创建虚拟环境
```bash
# 在项目根目录创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate

# 升级 pip
python -m pip install --upgrade pip
```

### 3. 安装项目依赖

#### 3.1 Python 后端依赖
```bash
# 创建 requirements.txt
pip install fastapi==0.104.1
pip install uvicorn[standard]==0.24.0
pip install sqlalchemy==2.0.23
pip install langchain==0.0.340
pip install pyautogen==0.2.0
pip install python-multipart==0.0.6
pip install python-jose[cryptography]==3.3.0
pip install passlib[bcrypt]==1.7.4
pip install aiofiles==23.2.1

# 保存依赖列表
pip freeze > requirements.txt
```

#### 3.2 .NET MAUI 依赖
```xml
<!-- 在 .csproj 文件中添加 -->
<PackageReference Include="Microsoft.AspNetCore.Components.WebView.Maui" Version="8.0.0" />
<PackageReference Include="MudBlazor" Version="6.11.2" />
<PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
<PackageReference Include="System.Text.Json" Version="8.0.0" />
<PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="8.0.0" />
```

---

## 📁 项目结构创建

### 创建项目目录
```bash
# 项目根目录
mkdir EduSynapse
cd EduSynapse

# 创建子目录
mkdir src
mkdir docs
mkdir deploy
mkdir tests

# 前端项目
mkdir src\EduSynapse.MAUI
mkdir src\EduSynapse.Shared

# 后端项目
mkdir src\EduSynapse.Backend
mkdir src\EduSynapse.Backend\app
mkdir src\EduSynapse.Backend\app\api
mkdir src\EduSynapse.Backend\app\core
mkdir src\EduSynapse.Backend\app\models
mkdir src\EduSynapse.Backend\app\services
mkdir src\EduSynapse.Backend\app\database
```

---

## 🔧 开发工具配置

### 1. Visual Studio 配置

#### 1.1 扩展推荐
```markdown
- **C# Dev Kit** - C# 开发增强
- **MAUI Check** - MAUI 环境检查
- **Thunder Client** - API 测试
- **GitLens** - Git 增强
```

#### 1.2 调试配置
```json
// launch.json (VS Code)
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Launch MAUI (Windows)",
            "type": "dotnet-maui",
            "request": "launch",
            "preLaunchTask": "dotnet: build"
        },
        {
            "name": "Python: FastAPI",
            "type": "python",
            "request": "launch",
            "program": "${workspaceFolder}/src/EduSynapse.Backend/main.py",
            "console": "integratedTerminal",
            "args": ["--reload"]
        }
    ]
}
```

### 2. Python 开发环境

#### 2.1 VS Code 配置 (可选)
```json
// settings.json
{
    "python.defaultInterpreterPath": "./venv/Scripts/python.exe",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": true,
    "python.formatting.provider": "black",
    "python.testing.pytestEnabled": true
}
```

#### 2.2 代码格式化工具
```bash
# 安装代码格式化工具
pip install black==23.11.0
pip install pylint==3.0.3
pip install pytest==7.4.3

# 配置 .pylintrc
pylint --generate-rcfile > .pylintrc
```

---

## 🗄️ 数据库配置

### 1. SQLite 配置 (开发环境)
```python
# app/database/database.py
import sqlite3
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

# SQLite 数据库文件路径
SQLALCHEMY_DATABASE_URL = "sqlite:///./edusynapse.db"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL, 
    connect_args={"check_same_thread": False}
)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()
```

### 2. 数据库初始化脚本
```python
# scripts/init_db.py
from app.database.database import engine, Base
from app.models import learning_plan, progress, ai_interaction

def create_tables():
    """创建所有数据表"""
    Base.metadata.create_all(bind=engine)
    print("数据库表创建完成!")

if __name__ == "__main__":
    create_tables()
```

---

## 🔑 API 密钥配置

### 1. 环境变量设置
```bash
# 创建 .env 文件
# OpenAI API
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4

# Anthropic Claude API (可选)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# 应用配置
DEBUG=True
SECRET_KEY=your_secret_key_here
DATABASE_URL=sqlite:///./edusynapse.db
```

### 2. 配置文件管理
```python
# app/core/config.py
import os
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    openai_api_key: str = os.getenv("OPENAI_API_KEY", "")
    anthropic_api_key: str = os.getenv("ANTHROPIC_API_KEY", "")
    debug: bool = os.getenv("DEBUG", "False").lower() == "true"
    secret_key: str = os.getenv("SECRET_KEY", "default-secret-key")
    database_url: str = os.getenv("DATABASE_URL", "sqlite:///./edusynapse.db")
    
    class Config:
        env_file = ".env"

settings = Settings()
```

---

## 🚀 启动和测试

### 1. 后端服务启动
```bash
# 激活虚拟环境
venv\Scripts\activate

# 启动 FastAPI 服务
cd src\EduSynapse.Backend
python main.py

# 或使用 uvicorn
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 2. 前端应用启动
```bash
# 在 Visual Studio 中打开 MAUI 项目
# 选择 Windows Machine 作为启动目标
# 按 F5 启动调试

# 或使用命令行
cd src\EduSynapse.MAUI
dotnet run
```

### 3. 验证环境
```bash
# 检查后端 API
curl http://localhost:8000/docs

# 检查数据库连接
python scripts/init_db.py

# 检查 MAUI 应用
# 应该能看到应用界面启动
```

---

## 🔍 常见问题解决

### 1. MAUI 相关问题
```bash
# 检查 MAUI 环境
dotnet workload list

# 安装/更新 MAUI 工作负载
dotnet workload install maui

# 清理和重建
dotnet clean
dotnet build
```

### 2. Python 依赖问题
```bash
# 重新创建虚拟环境
rmdir /s venv
python -m venv venv
venv\Scripts\activate
pip install -r requirements.txt
```

### 3. 数据库问题
```bash
# 删除并重新创建数据库
del edusynapse.db
python scripts/init_db.py
```

---

## 📝 开发工作流

### 1. 日常开发流程
```bash
# 1. 激活 Python 环境
venv\Scripts\activate

# 2. 启动后端服务
cd src\EduSynapse.Backend
python main.py

# 3. 在另一个终端启动前端 (Visual Studio)
# 打开 EduSynapse.MAUI.sln
# 按 F5 启动调试
```

### 2. 代码提交流程
```bash
# 格式化 Python 代码
black src/EduSynapse.Backend/

# 运行测试
pytest tests/

# Git 提交
git add .
git commit -m "feat: 添加新功能"
git push
```

---

## ✅ 环境验证清单

- [ ] Visual Studio 2022 已安装并配置 MAUI 工作负载
- [ ] Python 3.11+ 已安装
- [ ] 虚拟环境已创建并激活
- [ ] 所有 Python 依赖已安装
- [ ] .NET 依赖包已添加到项目
- [ ] 数据库已初始化
- [ ] API 密钥已配置
- [ ] 后端服务可以启动 (http://localhost:8000)
- [ ] 前端应用可以启动
- [ ] 前后端可以正常通信

完成以上配置后，开发环境就准备就绪了！
