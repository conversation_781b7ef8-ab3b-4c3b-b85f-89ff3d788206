using System.Text.Json.Serialization;

namespace EduSynapse.MAUI.Models;

/// <summary>
/// 学习计划模型
/// </summary>
public class LearningPlan
{
    [JsonPropertyName("id")]
    public int Id { get; set; }

    [JsonPropertyName("topic")]
    public string Topic { get; set; } = string.Empty;

    [JsonPropertyName("description")]
    public string? Description { get; set; }

    [JsonPropertyName("wwh_structure")]
    public WWHStructure? WwhStructure { get; set; }

    [JsonPropertyName("duration_days")]
    public int DurationDays { get; set; }

    [JsonPropertyName("difficulty_level")]
    public string DifficultyLevel { get; set; } = "medium";

    [JsonPropertyName("target_hours_per_day")]
    public double TargetHoursPerDay { get; set; }

    [JsonPropertyName("status")]
    public string Status { get; set; } = "active";

    [JsonPropertyName("created_at")]
    public DateTime CreatedAt { get; set; }

    [JsonPropertyName("updated_at")]
    public DateTime UpdatedAt { get; set; }

    [JsonPropertyName("started_at")]
    public DateTime? StartedAt { get; set; }

    [JsonPropertyName("completed_at")]
    public DateTime? CompletedAt { get; set; }

    // 计算属性
    public string DifficultyDisplayName => DifficultyLevel switch
    {
        "easy" => "简单",
        "medium" => "中等",
        "hard" => "困难",
        _ => "未知"
    };

    public string StatusDisplayName => Status switch
    {
        "active" => "进行中",
        "completed" => "已完成",
        "paused" => "已暂停",
        "cancelled" => "已取消",
        _ => "未知"
    };

    public double ProgressPercentage { get; set; } = 0.0;

    public int CompletedDays { get; set; } = 0;

    public TimeSpan EstimatedTotalTime => TimeSpan.FromHours(DurationDays * TargetHoursPerDay);
}

/// <summary>
/// WWH框架结构
/// </summary>
public class WWHStructure
{
    [JsonPropertyName("what")]
    public WhatSection? What { get; set; }

    [JsonPropertyName("why")]
    public WhySection? Why { get; set; }

    [JsonPropertyName("how")]
    public HowSection? How { get; set; }

    [JsonPropertyName("daily_breakdown")]
    public List<DailyPlan> DailyBreakdown { get; set; } = new();

    [JsonPropertyName("metadata")]
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// What阶段内容
/// </summary>
public class WhatSection
{
    [JsonPropertyName("core_concepts")]
    public List<string> CoreConcepts { get; set; } = new();

    [JsonPropertyName("key_features")]
    public List<string> KeyFeatures { get; set; } = new();

    [JsonPropertyName("definitions")]
    public Dictionary<string, string> Definitions { get; set; } = new();

    [JsonPropertyName("prerequisites")]
    public List<string> Prerequisites { get; set; } = new();
}

/// <summary>
/// Why阶段内容
/// </summary>
public class WhySection
{
    [JsonPropertyName("historical_context")]
    public string? HistoricalContext { get; set; }

    [JsonPropertyName("practical_importance")]
    public string? PracticalImportance { get; set; }

    [JsonPropertyName("learning_motivation")]
    public string? LearningMotivation { get; set; }

    [JsonPropertyName("career_benefits")]
    public string? CareerBenefits { get; set; }

    [JsonPropertyName("real_world_applications")]
    public List<string> RealWorldApplications { get; set; } = new();
}

/// <summary>
/// How阶段内容
/// </summary>
public class HowSection
{
    [JsonPropertyName("practice_projects")]
    public List<PracticeProject> PracticeProjects { get; set; } = new();

    [JsonPropertyName("code_examples")]
    public List<CodeExample> CodeExamples { get; set; } = new();

    [JsonPropertyName("exercises")]
    public List<Exercise> Exercises { get; set; } = new();

    [JsonPropertyName("learning_methods")]
    public List<string> LearningMethods { get; set; } = new();

    [JsonPropertyName("recommended_resources")]
    public List<RecommendedResource> RecommendedResources { get; set; } = new();
}

/// <summary>
/// 每日学习计划
/// </summary>
public class DailyPlan
{
    [JsonPropertyName("day")]
    public int Day { get; set; }

    [JsonPropertyName("focus")]
    public string Focus { get; set; } = string.Empty;

    [JsonPropertyName("topics")]
    public List<string> Topics { get; set; } = new();

    [JsonPropertyName("tasks")]
    public List<DailyTask> Tasks { get; set; } = new();

    [JsonPropertyName("goals")]
    public List<string> Goals { get; set; } = new();

    [JsonPropertyName("assessment")]
    public string? Assessment { get; set; }

    // 计算属性
    public string FocusDisplayName => Focus switch
    {
        "what" => "理论学习",
        "why" => "背景理解",
        "how" => "实践应用",
        _ => "综合学习"
    };

    public int TotalEstimatedMinutes => Tasks.Sum(t => t.EstimatedMinutes);
}

/// <summary>
/// 每日任务
/// </summary>
public class DailyTask
{
    [JsonPropertyName("task")]
    public string Task { get; set; } = string.Empty;

    [JsonPropertyName("type")]
    public string Type { get; set; } = string.Empty;

    [JsonPropertyName("estimated_minutes")]
    public int EstimatedMinutes { get; set; }

    public string TypeDisplayName => Type switch
    {
        "reading" => "阅读学习",
        "practice" => "实践练习",
        "study" => "理论学习",
        "project" => "项目实战",
        _ => "其他"
    };
}

/// <summary>
/// 实践项目
/// </summary>
public class PracticeProject
{
    [JsonPropertyName("title")]
    public string Title { get; set; } = string.Empty;

    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;

    [JsonPropertyName("difficulty")]
    public string Difficulty { get; set; } = "medium";
}

/// <summary>
/// 代码示例
/// </summary>
public class CodeExample
{
    [JsonPropertyName("title")]
    public string Title { get; set; } = string.Empty;

    [JsonPropertyName("language")]
    public string Language { get; set; } = string.Empty;

    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// 练习题
/// </summary>
public class Exercise
{
    [JsonPropertyName("title")]
    public string Title { get; set; } = string.Empty;

    [JsonPropertyName("type")]
    public string Type { get; set; } = string.Empty;

    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// 推荐资源
/// </summary>
public class RecommendedResource
{
    [JsonPropertyName("type")]
    public string Type { get; set; } = string.Empty;

    [JsonPropertyName("title")]
    public string Title { get; set; } = string.Empty;

    [JsonPropertyName("author")]
    public string? Author { get; set; }

    [JsonPropertyName("url")]
    public string? Url { get; set; }
}
