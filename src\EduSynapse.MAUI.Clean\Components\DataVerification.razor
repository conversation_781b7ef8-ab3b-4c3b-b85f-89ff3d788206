@page "/data-verification"
@using EduSynapse.MAUI.Services
@using EduSynapse.MAUI.Models
@inject StorageService StorageService

<div class="container-fluid p-4">
    <div class="row">
        <div class="col-12">
            <h1 class="text-primary mb-4">🔍 数据验证中心</h1>
            <p class="text-muted">检查本地存储的数据是否正确保存</p>
        </div>
    </div>

    <!-- 操作按钮 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>🛠️ 数据操作</h5>
                </div>
                <div class="card-body">
                    <button class="btn btn-primary me-2" @onclick="LoadLearningPlans" disabled="@isLoading">
                        @if (isLoading)
                        {
                            <span class="spinner-border spinner-border-sm me-2"></span>
                        }
                        📋 加载学习计划
                    </button>

                    <button class="btn btn-success me-2" @onclick="LoadProgressRecords" disabled="@isLoading">
                        @if (isLoading)
                        {
                            <span class="spinner-border spinner-border-sm me-2"></span>
                        }
                        📊 加载学习记录
                    </button>

                    <button class="btn btn-info me-2" @onclick="ShowStorageInfo">
                        📁 存储信息
                    </button>

                    <button class="btn btn-warning me-2" @onclick="ClearAllData">
                        🗑️ 清空数据
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 存储信息 -->
    @if (!string.IsNullOrEmpty(storageInfo))
    {
        <div class="row mb-4">
            <div class="col-12">
                <div class="alert alert-info">
                    <h6>📁 存储路径信息</h6>
                    <pre>@storageInfo</pre>
                </div>
            </div>
        </div>
    }

    <!-- 学习计划数据 -->
    @if (learningPlans != null)
    {
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5>📚 已保存的学习计划 (@learningPlans.Count 个)</h5>
                        <span class="badge bg-success">@DateTime.Now.ToString("HH:mm:ss")</span>
                    </div>
                    <div class="card-body">
                        @if (learningPlans.Count == 0)
                        {
                            <div class="alert alert-warning">
                                <strong>⚠️ 没有找到学习计划数据</strong><br/>
                                可能的原因：
                                <ul>
                                    <li>还没有创建任何学习计划</li>
                                    <li>数据保存失败</li>
                                    <li>存储路径问题</li>
                                </ul>
                            </div>
                        }
                        else
                        {
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>学习主题</th>
                                            <th>周期(天)</th>
                                            <th>每日时间(小时)</th>
                                            <th>难度</th>
                                            <th>状态</th>
                                            <th>创建时间</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var plan in learningPlans)
                                        {
                                            <tr>
                                                <td>@plan.Id</td>
                                                <td>@plan.Topic</td>
                                                <td>@plan.DurationDays</td>
                                                <td>@plan.TargetHoursPerDay</td>
                                                <td>
                                                    <span class="badge @GetDifficultyBadgeClass(plan.DifficultyLevel)">
                                                        @plan.DifficultyDisplayName
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge @GetStatusBadgeClass(plan.Status)">
                                                        @plan.StatusDisplayName
                                                    </span>
                                                </td>
                                                <td>@plan.CreatedAt.ToString("yyyy-MM-dd HH:mm")</td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- 详细数据展示 -->
                            <div class="mt-4">
                                <h6>📄 原始 JSON 数据</h6>
                                <pre class="bg-light p-3" style="max-height: 300px; overflow-y: auto;">@rawJsonData</pre>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- 学习记录数据 -->
    @if (progressRecordsData != null)
    {
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5>📊 学习记录数据 (@progressRecordsData.Sum(kvp => kvp.Value.Count) 条记录)</h5>
                        <span class="badge bg-info">@DateTime.Now.ToString("HH:mm:ss")</span>
                    </div>
                    <div class="card-body">
                        @if (!progressRecordsData.Any())
                        {
                            <div class="alert alert-warning">
                                <strong>⚠️ 没有找到学习记录数据</strong><br/>
                                可能的原因：
                                <ul>
                                    <li>还没有记录任何学习进度</li>
                                    <li>学习记录保存失败</li>
                                    <li>存储路径问题</li>
                                </ul>
                            </div>
                        }
                        else
                        {
                            <!-- 学习记录统计 -->
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body text-center">
                                            <h4>@progressRecordsData.Count</h4>
                                            <p class="mb-0">有记录的计划</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-success text-white">
                                        <div class="card-body text-center">
                                            <h4>@progressRecordsData.Sum(kvp => kvp.Value.Count)</h4>
                                            <p class="mb-0">总记录数</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-info text-white">
                                        <div class="card-body text-center">
                                            <h4>@progressRecordsData.Sum(kvp => kvp.Value.Sum(r => r.StudyHours)).ToString("F1")</h4>
                                            <p class="mb-0">总学习时间(小时)</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-warning text-white">
                                        <div class="card-body text-center">
                                            <h4>@GetAverageStudyHours().ToString("F1")</h4>
                                            <p class="mb-0">平均每次(小时)</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 按计划分组显示学习记录 -->
                            @foreach (var planRecords in progressRecordsData.OrderByDescending(kvp => kvp.Value.Count))
                            {
                                var planName = GetPlanName(planRecords.Key);
                                <div class="card mb-3">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            📚 @planName
                                            <span class="badge bg-secondary ms-2">计划ID: @planRecords.Key</span>
                                            <span class="badge bg-primary ms-2">@planRecords.Value.Count 条记录</span>
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-sm table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>日期</th>
                                                        <th>学习时间</th>
                                                        <th>状态</th>
                                                        <th>学习内容</th>
                                                        <th>心情评分</th>
                                                        <th>笔记</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach (var record in planRecords.Value.OrderByDescending(r => r.Date))
                                                    {
                                                        <tr>
                                                            <td>@record.Date.ToString("MM-dd")</td>
                                                            <td>@record.StudyHours.ToString("F1")h</td>
                                                            <td>
                                                                <span class="badge @GetRecordStatusBadgeClass(record.Status)">
                                                                    @GetRecordStatusDisplayName(record.Status)
                                                                </span>
                                                            </td>
                                                            <td class="text-truncate" style="max-width: 200px;" title="@record.Content">
                                                                @record.Content
                                                            </td>
                                                            <td>
                                                                @if (record.MoodScore > 0)
                                                                {
                                                                    <span class="badge @GetMoodBadgeClass(record.MoodScore)">
                                                                        @record.MoodScore
                                                                    </span>
                                                                }
                                                            </td>
                                                            <td class="text-truncate" style="max-width: 250px;" title="@record.Notes">
                                                                @record.Notes
                                                            </td>
                                                        </tr>
                                                    }
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            }

                            <!-- 原始 JSON 数据 -->
                            <div class="mt-4">
                                <h6>📄 学习记录原始 JSON 数据</h6>
                                <pre class="bg-light p-3" style="max-height: 300px; overflow-y: auto;">@progressRecordsJsonData</pre>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- 操作结果 -->
    @if (!string.IsNullOrEmpty(resultMessage))
    {
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert @(isSuccess ? "alert-success" : "alert-danger") alert-dismissible fade show">
                    <strong>@(isSuccess ? "✅ 成功！" : "❌ 错误！")</strong> @resultMessage
                    <button type="button" class="btn-close" @onclick="ClearResult"></button>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private List<LearningPlan>? learningPlans = null;
    private Dictionary<int, List<ProgressRecord>>? progressRecordsData = null;
    private string storageInfo = "";
    private string rawJsonData = "";
    private string progressRecordsJsonData = "";
    private bool isLoading = false;
    private bool isSuccess = false;
    private string resultMessage = "";

    protected override async Task OnInitializedAsync()
    {
        await LoadLearningPlans();
        await LoadProgressRecords();
    }

    private async Task LoadLearningPlans()
    {
        isLoading = true;
        ClearResult();
        
        try
        {
            learningPlans = await StorageService.LoadAsync<List<LearningPlan>>("learning_plans");
            
            if (learningPlans == null)
            {
                learningPlans = new List<LearningPlan>();
                resultMessage = "没有找到学习计划数据文件";
                isSuccess = false;
            }
            else
            {
                // 获取原始 JSON 数据
                rawJsonData = System.Text.Json.JsonSerializer.Serialize(learningPlans, new System.Text.Json.JsonSerializerOptions 
                { 
                    WriteIndented = true,
                    PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
                });
                
                resultMessage = $"成功加载 {learningPlans.Count} 个学习计划";
                isSuccess = true;
            }
            
            System.Diagnostics.Debug.WriteLine($"✅ 加载学习计划: {learningPlans.Count} 个");
        }
        catch (Exception ex)
        {
            learningPlans = new List<LearningPlan>();
            resultMessage = $"加载学习计划失败: {ex.Message}";
            isSuccess = false;
            System.Diagnostics.Debug.WriteLine($"❌ 加载学习计划失败: {ex}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task LoadProgressRecords()
    {
        isLoading = true;
        ClearResult();

        try
        {
            progressRecordsData = new Dictionary<int, List<ProgressRecord>>();

            // 如果有学习计划，为每个计划加载进度记录
            if (learningPlans != null && learningPlans.Any())
            {
                foreach (var plan in learningPlans)
                {
                    var storageKey = $"progress_records_{plan.Id}";
                    var records = await StorageService.LoadAsync<List<ProgressRecord>>(storageKey);

                    if (records != null && records.Any())
                    {
                        progressRecordsData[plan.Id] = records;
                    }
                }
            }

            // 生成 JSON 数据
            progressRecordsJsonData = System.Text.Json.JsonSerializer.Serialize(progressRecordsData, new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
            });

            var totalRecords = progressRecordsData.Sum(kvp => kvp.Value.Count);
            resultMessage = $"成功加载 {progressRecordsData.Count} 个计划的学习记录，共 {totalRecords} 条记录";
            isSuccess = true;

            System.Diagnostics.Debug.WriteLine($"✅ 加载学习记录: {progressRecordsData.Count} 个计划，{totalRecords} 条记录");
        }
        catch (Exception ex)
        {
            progressRecordsData = new Dictionary<int, List<ProgressRecord>>();
            resultMessage = $"加载学习记录失败: {ex.Message}";
            isSuccess = false;
            System.Diagnostics.Debug.WriteLine($"❌ 加载学习记录失败: {ex}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private void ShowStorageInfo()
    {
        try
        {
            var appDataDir = FileSystem.AppDataDirectory;
            var eduSynapseDir = Path.Combine(appDataDir, "EduSynapse");
            var learningPlansFile = Path.Combine(eduSynapseDir, "learning_plans.json");
            
            storageInfo = $"应用数据目录: {appDataDir}\n";
            storageInfo += $"EduSynapse 目录: {eduSynapseDir}\n";
            storageInfo += $"学习计划文件: {learningPlansFile}\n";
            storageInfo += $"目录是否存在: {Directory.Exists(eduSynapseDir)}\n";
            storageInfo += $"文件是否存在: {File.Exists(learningPlansFile)}\n";
            
            if (File.Exists(learningPlansFile))
            {
                var fileInfo = new FileInfo(learningPlansFile);
                storageInfo += $"文件大小: {fileInfo.Length} 字节\n";
                storageInfo += $"最后修改: {fileInfo.LastWriteTime:yyyy-MM-dd HH:mm:ss}\n";
            }
            
            resultMessage = "存储信息已显示";
            isSuccess = true;
        }
        catch (Exception ex)
        {
            storageInfo = $"获取存储信息失败: {ex.Message}";
            resultMessage = "获取存储信息失败";
            isSuccess = false;
        }
    }

    private async Task ClearAllData()
    {
        try
        {
            await StorageService.ClearAllAsync();
            learningPlans = new List<LearningPlan>();
            rawJsonData = "";
            resultMessage = "所有数据已清空";
            isSuccess = true;
        }
        catch (Exception ex)
        {
            resultMessage = $"清空数据失败: {ex.Message}";
            isSuccess = false;
        }
    }

    private void ClearResult()
    {
        resultMessage = "";
        isSuccess = false;
    }

    private string GetDifficultyBadgeClass(string difficulty)
    {
        return difficulty switch
        {
            "easy" => "bg-success",
            "medium" => "bg-warning",
            "hard" => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private string GetStatusBadgeClass(string status)
    {
        return status switch
        {
            "active" => "bg-primary",
            "completed" => "bg-success",
            "paused" => "bg-warning",
            "cancelled" => "bg-secondary",
            _ => "bg-secondary"
        };
    }

    // 学习记录相关辅助方法
    private string GetPlanName(int planId)
    {
        var plan = learningPlans?.FirstOrDefault(p => p.Id == planId);
        return plan?.Topic ?? $"未知计划 (ID: {planId})";
    }

    private double GetAverageStudyHours()
    {
        if (progressRecordsData == null || !progressRecordsData.Any())
            return 0;

        var totalRecords = progressRecordsData.Sum(kvp => kvp.Value.Count);
        var totalHours = progressRecordsData.Sum(kvp => kvp.Value.Sum(r => r.StudyHours));

        return totalRecords > 0 ? totalHours / totalRecords : 0;
    }

    private string GetRecordStatusBadgeClass(string status)
    {
        return status switch
        {
            "completed" => "bg-success",
            "partial" => "bg-warning",
            "skipped" => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private string GetRecordStatusDisplayName(string status)
    {
        return status switch
        {
            "completed" => "已完成",
            "partial" => "部分完成",
            "skipped" => "未学习",
            _ => "未知"
        };
    }

    private string GetMoodBadgeClass(int moodScore)
    {
        return moodScore switch
        {
            >= 8 => "bg-success",
            >= 6 => "bg-info",
            >= 4 => "bg-warning",
            _ => "bg-danger"
        };
    }

    // 进度记录数据模型
    public class ProgressRecord
    {
        public DateTime Date { get; set; }
        public double StudyHours { get; set; }
        public string Status { get; set; } = "completed";
        public string Content { get; set; } = "";
        public int MoodScore { get; set; } = 8;
        public string Notes { get; set; } = "";
        public int PlanId { get; set; }
    }
}
