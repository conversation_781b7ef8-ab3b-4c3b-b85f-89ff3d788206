@page "/data-verification"
@using EduSynapse.MAUI.Services
@using EduSynapse.MAUI.Models
@inject StorageService StorageService

<div class="container-fluid p-4">
    <div class="row">
        <div class="col-12">
            <h1 class="text-primary mb-4">🔍 数据验证中心</h1>
            <p class="text-muted">检查本地存储的数据是否正确保存</p>
        </div>
    </div>

    <!-- 操作按钮 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>🛠️ 数据操作</h5>
                </div>
                <div class="card-body">
                    <button class="btn btn-primary me-2" @onclick="LoadLearningPlans" disabled="@isLoading">
                        @if (isLoading)
                        {
                            <span class="spinner-border spinner-border-sm me-2"></span>
                        }
                        📋 加载学习计划
                    </button>
                    
                    <button class="btn btn-info me-2" @onclick="ShowStorageInfo">
                        📁 存储信息
                    </button>
                    
                    <button class="btn btn-warning me-2" @onclick="ClearAllData">
                        🗑️ 清空数据
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 存储信息 -->
    @if (!string.IsNullOrEmpty(storageInfo))
    {
        <div class="row mb-4">
            <div class="col-12">
                <div class="alert alert-info">
                    <h6>📁 存储路径信息</h6>
                    <pre>@storageInfo</pre>
                </div>
            </div>
        </div>
    }

    <!-- 学习计划数据 -->
    @if (learningPlans != null)
    {
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5>📚 已保存的学习计划 (@learningPlans.Count 个)</h5>
                        <span class="badge bg-success">@DateTime.Now.ToString("HH:mm:ss")</span>
                    </div>
                    <div class="card-body">
                        @if (learningPlans.Count == 0)
                        {
                            <div class="alert alert-warning">
                                <strong>⚠️ 没有找到学习计划数据</strong><br/>
                                可能的原因：
                                <ul>
                                    <li>还没有创建任何学习计划</li>
                                    <li>数据保存失败</li>
                                    <li>存储路径问题</li>
                                </ul>
                            </div>
                        }
                        else
                        {
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>学习主题</th>
                                            <th>周期(天)</th>
                                            <th>每日时间(小时)</th>
                                            <th>难度</th>
                                            <th>状态</th>
                                            <th>创建时间</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var plan in learningPlans)
                                        {
                                            <tr>
                                                <td>@plan.Id</td>
                                                <td>@plan.Topic</td>
                                                <td>@plan.DurationDays</td>
                                                <td>@plan.TargetHoursPerDay</td>
                                                <td>
                                                    <span class="badge @GetDifficultyBadgeClass(plan.DifficultyLevel)">
                                                        @plan.DifficultyDisplayName
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge @GetStatusBadgeClass(plan.Status)">
                                                        @plan.StatusDisplayName
                                                    </span>
                                                </td>
                                                <td>@plan.CreatedAt.ToString("yyyy-MM-dd HH:mm")</td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- 详细数据展示 -->
                            <div class="mt-4">
                                <h6>📄 原始 JSON 数据</h6>
                                <pre class="bg-light p-3" style="max-height: 300px; overflow-y: auto;">@rawJsonData</pre>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- 操作结果 -->
    @if (!string.IsNullOrEmpty(resultMessage))
    {
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert @(isSuccess ? "alert-success" : "alert-danger") alert-dismissible fade show">
                    <strong>@(isSuccess ? "✅ 成功！" : "❌ 错误！")</strong> @resultMessage
                    <button type="button" class="btn-close" @onclick="ClearResult"></button>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private List<LearningPlan>? learningPlans = null;
    private string storageInfo = "";
    private string rawJsonData = "";
    private bool isLoading = false;
    private bool isSuccess = false;
    private string resultMessage = "";

    protected override async Task OnInitializedAsync()
    {
        await LoadLearningPlans();
    }

    private async Task LoadLearningPlans()
    {
        isLoading = true;
        ClearResult();
        
        try
        {
            learningPlans = await StorageService.LoadAsync<List<LearningPlan>>("learning_plans");
            
            if (learningPlans == null)
            {
                learningPlans = new List<LearningPlan>();
                resultMessage = "没有找到学习计划数据文件";
                isSuccess = false;
            }
            else
            {
                // 获取原始 JSON 数据
                rawJsonData = System.Text.Json.JsonSerializer.Serialize(learningPlans, new System.Text.Json.JsonSerializerOptions 
                { 
                    WriteIndented = true,
                    PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
                });
                
                resultMessage = $"成功加载 {learningPlans.Count} 个学习计划";
                isSuccess = true;
            }
            
            System.Diagnostics.Debug.WriteLine($"✅ 加载学习计划: {learningPlans.Count} 个");
        }
        catch (Exception ex)
        {
            learningPlans = new List<LearningPlan>();
            resultMessage = $"加载学习计划失败: {ex.Message}";
            isSuccess = false;
            System.Diagnostics.Debug.WriteLine($"❌ 加载学习计划失败: {ex}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private void ShowStorageInfo()
    {
        try
        {
            var appDataDir = FileSystem.AppDataDirectory;
            var eduSynapseDir = Path.Combine(appDataDir, "EduSynapse");
            var learningPlansFile = Path.Combine(eduSynapseDir, "learning_plans.json");
            
            storageInfo = $"应用数据目录: {appDataDir}\n";
            storageInfo += $"EduSynapse 目录: {eduSynapseDir}\n";
            storageInfo += $"学习计划文件: {learningPlansFile}\n";
            storageInfo += $"目录是否存在: {Directory.Exists(eduSynapseDir)}\n";
            storageInfo += $"文件是否存在: {File.Exists(learningPlansFile)}\n";
            
            if (File.Exists(learningPlansFile))
            {
                var fileInfo = new FileInfo(learningPlansFile);
                storageInfo += $"文件大小: {fileInfo.Length} 字节\n";
                storageInfo += $"最后修改: {fileInfo.LastWriteTime:yyyy-MM-dd HH:mm:ss}\n";
            }
            
            resultMessage = "存储信息已显示";
            isSuccess = true;
        }
        catch (Exception ex)
        {
            storageInfo = $"获取存储信息失败: {ex.Message}";
            resultMessage = "获取存储信息失败";
            isSuccess = false;
        }
    }

    private async Task ClearAllData()
    {
        try
        {
            await StorageService.ClearAllAsync();
            learningPlans = new List<LearningPlan>();
            rawJsonData = "";
            resultMessage = "所有数据已清空";
            isSuccess = true;
        }
        catch (Exception ex)
        {
            resultMessage = $"清空数据失败: {ex.Message}";
            isSuccess = false;
        }
    }

    private void ClearResult()
    {
        resultMessage = "";
        isSuccess = false;
    }

    private string GetDifficultyBadgeClass(string difficulty)
    {
        return difficulty switch
        {
            "easy" => "bg-success",
            "medium" => "bg-warning",
            "hard" => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private string GetStatusBadgeClass(string status)
    {
        return status switch
        {
            "active" => "bg-primary",
            "completed" => "bg-success",
            "paused" => "bg-warning",
            "cancelled" => "bg-secondary",
            _ => "bg-secondary"
        };
    }
}
