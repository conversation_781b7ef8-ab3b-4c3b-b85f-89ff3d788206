@echo off
chcp 65001 >nul
echo ========================================
echo  EduSynapse Dependencies Fix Script
echo ========================================

echo Fixing LangChain and Pydantic version conflicts...

echo.
echo Uninstalling conflicting packages...
python -m pip uninstall -y langchain langchain-openai langchain-community pyautogen pydantic pydantic-settings

echo.
echo Installing compatible Pydantic versions...
python -m pip install "pydantic>=2.0.0,<2.6.0" "pydantic-settings>=2.0.0,<2.6.0"

echo.
echo Installing core web framework...
python -m pip install "fastapi>=0.104.0" "uvicorn[standard]>=0.24.0"

echo.
echo Installing database support...
python -m pip install "sqlalchemy>=2.0.0" "alembic>=1.13.0"

echo.
echo Installing basic utilities...
python -m pip install "python-multipart>=0.0.6"
python -m pip install "python-jose[cryptography]>=3.3.0"
python -m pip install "passlib[bcrypt]>=1.7.4"
python -m pip install "aiofiles>=23.2.0"
python -m pip install "python-dotenv>=1.0.0"
python -m pip install "httpx>=0.25.0"

echo.
echo Installing AI services (optional)...
python -m pip install "openai>=1.6.0"
python -m pip install "anthropic>=0.8.0"

echo.
echo Installing test tools...
python -m pip install "pytest>=7.4.0" "pytest-asyncio>=0.21.0"

echo.
echo ========================================
echo Fix completed!
echo ========================================
echo.
echo Next steps:
echo   1. Run: python main.py
echo   2. Visit: http://localhost:8000
echo   3. Check status: http://localhost:8000/api/ai/status
echo.
echo Note:
echo   - LangChain and AutoGen skipped due to compatibility issues
echo   - System will run in fallback mode with basic functionality
echo   - AI features can be added later with compatible versions
echo.
pause
