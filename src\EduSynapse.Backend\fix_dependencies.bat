@echo off
echo ========================================
echo  🔧 EduSynapse 依赖问题修复脚本
echo ========================================

echo 🚨 修复LangChain和Pydantic版本冲突问题...

echo.
echo 📦 卸载可能冲突的包...
python -m pip uninstall -y langchain langchain-openai langchain-community pyautogen pydantic pydantic-settings

echo.
echo 🔧 安装兼容版本的Pydantic...
python -m pip install "pydantic>=2.0.0,<2.6.0" "pydantic-settings>=2.0.0,<2.6.0"

echo.
echo 📚 安装核心Web框架...
python -m pip install "fastapi>=0.104.0" "uvicorn[standard]>=0.24.0"

echo.
echo 💾 安装数据库支持...
python -m pip install "sqlalchemy>=2.0.0" "alembic>=1.13.0"

echo.
echo 🔧 安装基础工具...
python -m pip install "python-multipart>=0.0.6" "python-jose[cryptography]>=3.3.0" "passlib[bcrypt]>=1.7.4" "aiofiles>=23.2.0" "python-dotenv>=1.0.0" "httpx>=0.25.0"

echo.
echo 🤖 尝试安装AI服务 (可选)...
python -m pip install "openai>=1.6.0" "anthropic>=0.8.0"

echo.
echo 🧪 安装测试工具...
python -m pip install "pytest>=7.4.0" "pytest-asyncio>=0.21.0"

echo.
echo ========================================
echo 🎉 修复完成！
echo ========================================
echo.
echo 📋 现在可以尝试启动服务:
echo   python main.py
echo.
echo 💡 说明:
echo   - 暂时跳过了LangChain和AutoGen的安装
echo   - 系统将运行在备用模式，基本功能正常
echo   - 如需AI功能，请稍后手动安装兼容版本
echo.
echo 🔍 检查服务状态:
echo   访问 http://localhost:8000/api/ai/status
echo.
pause
