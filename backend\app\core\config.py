"""
EduSynapse Backend Configuration
应用配置管理
"""

import os
from typing import List, Optional
from pydantic import BaseSettings, Field


class Settings(BaseSettings):
    """应用设置"""
    
    # 基础配置
    APP_NAME: str = "EduSynapse AI Backend"
    VERSION: str = "1.0.0"
    DEBUG: bool = Field(default=False, env="DEBUG")
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=8000, env="PORT")
    
    # CORS配置
    ALLOWED_ORIGINS: List[str] = Field(
        default=[
            "http://localhost:3000",
            "http://localhost:8080", 
            "http://127.0.0.1:3000",
            "http://127.0.0.1:8080"
        ],
        env="ALLOWED_ORIGINS"
    )
    
    # AI服务配置
    OPENAI_API_KEY: str = Field(..., env="OPENAI_API_KEY")
    OPENAI_API_BASE: str = Field(
        default="https://api.openai.com/v1",
        env="OPENAI_API_BASE"
    )
    OPENAI_MODEL: str = Field(default="gpt-4", env="OPENAI_MODEL")
    OPENAI_TEMPERATURE: float = Field(default=0.7, env="OPENAI_TEMPERATURE")
    OPENAI_MAX_TOKENS: int = Field(default=2000, env="OPENAI_MAX_TOKENS")
    
    # 数据库配置
    DATABASE_URL: str = Field(
        default="sqlite:///./edusynapse.db",
        env="DATABASE_URL"
    )
    DATABASE_ECHO: bool = Field(default=False, env="DATABASE_ECHO")
    
    # Redis配置 (可选)
    REDIS_URL: Optional[str] = Field(default=None, env="REDIS_URL")
    REDIS_PASSWORD: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    
    # 日志配置
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FORMAT: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        env="LOG_FORMAT"
    )
    
    # 安全配置
    SECRET_KEY: str = Field(
        default="your-secret-key-change-in-production",
        env="SECRET_KEY"
    )
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(
        default=30,
        env="ACCESS_TOKEN_EXPIRE_MINUTES"
    )
    
    # 文件存储配置
    UPLOAD_DIR: str = Field(default="./uploads", env="UPLOAD_DIR")
    MAX_FILE_SIZE: int = Field(default=10 * 1024 * 1024, env="MAX_FILE_SIZE")  # 10MB
    ALLOWED_FILE_TYPES: List[str] = Field(
        default=[".pdf", ".txt", ".docx", ".py", ".js", ".html", ".css"],
        env="ALLOWED_FILE_TYPES"
    )
    
    # AI教学引擎配置
    MAX_SESSION_DURATION: int = Field(default=3600, env="MAX_SESSION_DURATION")  # 1小时
    MAX_CONCURRENT_SESSIONS: int = Field(default=100, env="MAX_CONCURRENT_SESSIONS")
    SESSION_CLEANUP_INTERVAL: int = Field(default=300, env="SESSION_CLEANUP_INTERVAL")  # 5分钟
    
    # WWH框架配置
    WWH_WHAT_WEIGHT: float = Field(default=0.3, env="WWH_WHAT_WEIGHT")
    WWH_WHY_WEIGHT: float = Field(default=0.3, env="WWH_WHY_WEIGHT")
    WWH_HOW_WEIGHT: float = Field(default=0.4, env="WWH_HOW_WEIGHT")
    
    # 进度跟踪配置
    PROGRESS_UPDATE_INTERVAL: int = Field(default=60, env="PROGRESS_UPDATE_INTERVAL")  # 1分钟
    MASTERY_THRESHOLD: float = Field(default=0.7, env="MASTERY_THRESHOLD")
    ERROR_PATTERN_MIN_FREQUENCY: int = Field(default=3, env="ERROR_PATTERN_MIN_FREQUENCY")
    
    # 缓存配置
    CACHE_TTL: int = Field(default=3600, env="CACHE_TTL")  # 1小时
    CACHE_MAX_SIZE: int = Field(default=1000, env="CACHE_MAX_SIZE")
    
    # 监控配置
    ENABLE_METRICS: bool = Field(default=True, env="ENABLE_METRICS")
    METRICS_PORT: int = Field(default=9090, env="METRICS_PORT")
    
    # 开发配置
    RELOAD: bool = Field(default=False, env="RELOAD")
    WORKERS: int = Field(default=1, env="WORKERS")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# 创建全局设置实例
settings = Settings()


# 环境特定配置
class DevelopmentSettings(Settings):
    """开发环境配置"""
    DEBUG: bool = True
    LOG_LEVEL: str = "DEBUG"
    RELOAD: bool = True
    DATABASE_ECHO: bool = True


class ProductionSettings(Settings):
    """生产环境配置"""
    DEBUG: bool = False
    LOG_LEVEL: str = "INFO"
    RELOAD: bool = False
    DATABASE_ECHO: bool = False
    WORKERS: int = 4


class TestingSettings(Settings):
    """测试环境配置"""
    DEBUG: bool = True
    LOG_LEVEL: str = "DEBUG"
    DATABASE_URL: str = "sqlite:///./test_edusynapse.db"
    CACHE_TTL: int = 60  # 1分钟


def get_settings() -> Settings:
    """根据环境变量获取相应的配置"""
    env = os.getenv("ENVIRONMENT", "development").lower()
    
    if env == "production":
        return ProductionSettings()
    elif env == "testing":
        return TestingSettings()
    else:
        return DevelopmentSettings()


# 配置验证
def validate_settings(settings: Settings) -> None:
    """验证配置的有效性"""
    
    # 验证必需的环境变量
    if not settings.OPENAI_API_KEY:
        raise ValueError("OPENAI_API_KEY environment variable is required")
    
    # 验证权重配置
    wwh_total_weight = (
        settings.WWH_WHAT_WEIGHT + 
        settings.WWH_WHY_WEIGHT + 
        settings.WWH_HOW_WEIGHT
    )
    if abs(wwh_total_weight - 1.0) > 0.01:
        raise ValueError(f"WWH weights must sum to 1.0, got {wwh_total_weight}")
    
    # 验证端口配置
    if not (1024 <= settings.PORT <= 65535):
        raise ValueError(f"PORT must be between 1024 and 65535, got {settings.PORT}")
    
    # 验证文件大小限制
    if settings.MAX_FILE_SIZE <= 0:
        raise ValueError("MAX_FILE_SIZE must be positive")
    
    # 验证掌握度阈值
    if not (0.0 <= settings.MASTERY_THRESHOLD <= 1.0):
        raise ValueError("MASTERY_THRESHOLD must be between 0.0 and 1.0")


# 应用启动时验证配置
validate_settings(settings)
