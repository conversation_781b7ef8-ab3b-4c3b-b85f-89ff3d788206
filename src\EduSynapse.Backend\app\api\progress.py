"""
EduSynapse Learning Progress API Endpoints
学习进度相关API端点
"""

import uuid
from datetime import datetime, timedelta
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import desc, func, and_

from app.database.database import get_db
from app.models.learning_plan import LearningPlan, LearningProgress
from app.schemas.learning_plan import (
    ProgressRecordRequest,
    ProgressResponse,
    ProgressStatsResponse,
    ApiResponse
)

# 创建路由器
router = APIRouter(prefix="/api/progress", tags=["学习进度"])


@router.get("/stats/{plan_id}", response_model=ApiResponse)
async def get_progress_stats(
    plan_id: int,
    db: Session = Depends(get_db)
):
    """
    获取学习计划的进度统计
    """
    try:
        # 验证学习计划是否存在
        plan = db.query(LearningPlan).filter(LearningPlan.id == plan_id).first()
        if not plan:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"学习计划 {plan_id} 不存在"
            )

        # 获取所有进度记录
        progress_records = db.query(LearningProgress).filter(
            LearningProgress.plan_id == plan_id
        ).order_by(LearningProgress.day_number).all()

        # 计算统计数据
        total_days = plan.duration_days
        completed_days = len(progress_records)
        completion_percentage = (completed_days / total_days * 100) if total_days > 0 else 0

        # 计算平均掌握度
        if progress_records:
            avg_what = sum(p.what_mastery or 0 for p in progress_records) / len(progress_records)
            avg_why = sum(p.why_mastery or 0 for p in progress_records) / len(progress_records)
            avg_how = sum(p.how_mastery or 0 for p in progress_records) / len(progress_records)
            avg_overall = (avg_what + avg_why + avg_how) / 3
        else:
            avg_what = avg_why = avg_how = avg_overall = 0.0

        # 计算学习时间统计
        total_time_spent = sum(p.time_spent or 0 for p in progress_records)
        average_daily_time = total_time_spent / completed_days if completed_days > 0 else 0

        # 心情和难度趋势
        mood_trend = [p.mood_score for p in progress_records]
        difficulty_trend = [p.difficulty_rating for p in progress_records]

        # 计算学习连续天数
        streak_days = calculate_streak_days(progress_records)

        # 最后学习日期
        last_study_date = progress_records[-1].completed_at if progress_records else None

        stats_data = {
            "plan_id": plan_id,
            "total_days": total_days,
            "completed_days": completed_days,
            "completion_percentage": round(completion_percentage, 1),
            "average_mastery": {
                "what": round(avg_what, 1),
                "why": round(avg_why, 1),
                "how": round(avg_how, 1),
                "overall": round(avg_overall, 1)
            },
            "total_time_spent": total_time_spent,
            "average_daily_time": round(average_daily_time, 1),
            "mood_trend": mood_trend,
            "difficulty_trend": difficulty_trend,
            "streak_days": streak_days,
            "last_study_date": last_study_date.isoformat() if last_study_date else None
        }

        return ApiResponse(
            success=True,
            data=stats_data,
            message="进度统计获取成功"
        )

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 获取进度统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取进度统计失败: {str(e)}"
        )


@router.get("/calendar/{plan_id}", response_model=ApiResponse)
async def get_progress_calendar(
    plan_id: int,
    year: int = None,
    month: int = None,
    db: Session = Depends(get_db)
):
    """
    获取学习日历数据
    """
    try:
        # 默认使用当前年月
        if year is None or month is None:
            now = datetime.now()
            year = year or now.year
            month = month or now.month

        # 验证学习计划是否存在
        plan = db.query(LearningPlan).filter(LearningPlan.id == plan_id).first()
        if not plan:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"学习计划 {plan_id} 不存在"
            )

        # 获取指定月份的进度记录
        start_date = datetime(year, month, 1)
        if month == 12:
            end_date = datetime(year + 1, 1, 1)
        else:
            end_date = datetime(year, month + 1, 1)

        progress_records = db.query(LearningProgress).filter(
            and_(
                LearningProgress.plan_id == plan_id,
                LearningProgress.completed_at >= start_date,
                LearningProgress.completed_at < end_date
            )
        ).all()

        # 构建日历数据
        calendar_data = []
        for progress in progress_records:
            if progress.completed_at:
                calendar_data.append({
                    "date": progress.completed_at.date().isoformat(),
                    "day_number": progress.day_number,
                    "overall_mastery": progress.overall_mastery,
                    "time_spent": progress.time_spent,
                    "mood_score": progress.mood_score,
                    "difficulty_rating": progress.difficulty_rating,
                    "has_notes": bool(progress.notes)
                })

        return ApiResponse(
            success=True,
            data={
                "year": year,
                "month": month,
                "calendar_data": calendar_data,
                "total_days": len(calendar_data)
            },
            message="学习日历获取成功"
        )

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 获取学习日历失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取学习日历失败: {str(e)}"
        )


@router.get("/analysis/{plan_id}", response_model=ApiResponse)
async def get_progress_analysis(
    plan_id: int,
    db: Session = Depends(get_db)
):
    """
    获取学习进度深度分析
    """
    try:
        # 验证学习计划是否存在
        plan = db.query(LearningPlan).filter(LearningPlan.id == plan_id).first()
        if not plan:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"学习计划 {plan_id} 不存在"
            )

        # 获取所有进度记录
        progress_records = db.query(LearningProgress).filter(
            LearningProgress.plan_id == plan_id
        ).order_by(LearningProgress.day_number).all()

        if not progress_records:
            return ApiResponse(
                success=True,
                data={"message": "暂无学习数据"},
                message="暂无学习数据可分析"
            )

        # WWH掌握度趋势分析
        wwh_trends = {
            "what_trend": [p.what_mastery or 0 for p in progress_records],
            "why_trend": [p.why_mastery or 0 for p in progress_records],
            "how_trend": [p.how_mastery or 0 for p in progress_records],
            "overall_trend": [p.overall_mastery for p in progress_records]
        }

        # 学习效率分析
        efficiency_data = []
        for progress in progress_records:
            if progress.time_spent and progress.time_spent > 0:
                mastery_per_hour = progress.overall_mastery / (progress.time_spent / 60)
                efficiency_data.append({
                    "day": progress.day_number,
                    "mastery_per_hour": round(mastery_per_hour, 2),
                    "time_spent": progress.time_spent,
                    "overall_mastery": progress.overall_mastery
                })

        # 学习模式分析
        patterns = analyze_learning_patterns(progress_records)

        # 预测和建议
        predictions = generate_learning_predictions(progress_records, plan)

        analysis_data = {
            "wwh_trends": wwh_trends,
            "efficiency_analysis": efficiency_data,
            "learning_patterns": patterns,
            "predictions": predictions,
            "total_records": len(progress_records)
        }

        return ApiResponse(
            success=True,
            data=analysis_data,
            message="学习分析完成"
        )

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 学习分析失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"学习分析失败: {str(e)}"
        )


@router.get("/daily/{plan_id}/{day_number}", response_model=ApiResponse)
async def get_daily_progress(
    plan_id: int,
    day_number: int,
    db: Session = Depends(get_db)
):
    """
    获取特定天数的学习进度详情
    """
    try:
        progress = db.query(LearningProgress).filter(
            and_(
                LearningProgress.plan_id == plan_id,
                LearningProgress.day_number == day_number
            )
        ).first()

        if not progress:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"第{day_number}天的学习记录不存在"
            )

        return ApiResponse(
            success=True,
            data=progress.to_dict(),
            message="每日进度获取成功"
        )

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 获取每日进度失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取每日进度失败: {str(e)}"
        )


def calculate_streak_days(progress_records: List[LearningProgress]) -> int:
    """计算连续学习天数"""
    if not progress_records:
        return 0
    
    # 按完成日期排序
    sorted_records = sorted(
        [p for p in progress_records if p.completed_at],
        key=lambda x: x.completed_at,
        reverse=True
    )
    
    if not sorted_records:
        return 0
    
    streak = 1
    current_date = sorted_records[0].completed_at.date()
    
    for i in range(1, len(sorted_records)):
        prev_date = sorted_records[i].completed_at.date()
        if (current_date - prev_date).days == 1:
            streak += 1
            current_date = prev_date
        else:
            break
    
    return streak


def analyze_learning_patterns(progress_records: List[LearningProgress]) -> dict:
    """分析学习模式"""
    if not progress_records:
        return {}
    
    # 最佳学习时间分析
    time_performance = {}
    for progress in progress_records:
        if progress.completed_at and progress.overall_mastery:
            hour = progress.completed_at.hour
            if hour not in time_performance:
                time_performance[hour] = []
            time_performance[hour].append(progress.overall_mastery)
    
    best_hours = []
    for hour, masteries in time_performance.items():
        avg_mastery = sum(masteries) / len(masteries)
        best_hours.append({"hour": hour, "avg_mastery": avg_mastery})
    
    best_hours.sort(key=lambda x: x["avg_mastery"], reverse=True)
    
    # 学习强度分析
    intensity_analysis = {
        "high_intensity_days": len([p for p in progress_records if (p.time_spent or 0) > 180]),  # 超过3小时
        "medium_intensity_days": len([p for p in progress_records if 60 <= (p.time_spent or 0) <= 180]),  # 1-3小时
        "low_intensity_days": len([p for p in progress_records if (p.time_spent or 0) < 60])  # 少于1小时
    }
    
    # 心情与表现关联分析
    mood_performance = {}
    for progress in progress_records:
        if progress.mood_score and progress.overall_mastery:
            mood = progress.mood_score
            if mood not in mood_performance:
                mood_performance[mood] = []
            mood_performance[mood].append(progress.overall_mastery)
    
    return {
        "best_learning_hours": best_hours[:3],  # 前3个最佳时间
        "intensity_analysis": intensity_analysis,
        "mood_performance_correlation": mood_performance
    }


def generate_learning_predictions(progress_records: List[LearningProgress], plan: LearningPlan) -> dict:
    """生成学习预测和建议"""
    if len(progress_records) < 3:
        return {"message": "数据不足，无法生成预测"}
    
    # 计算平均进步速度
    recent_records = progress_records[-3:]  # 最近3天
    avg_mastery_gain = sum(p.overall_mastery for p in recent_records) / len(recent_records)
    
    # 预测完成时间
    remaining_days = plan.duration_days - len(progress_records)
    if avg_mastery_gain > 0:
        estimated_completion = len(progress_records) + (100 - avg_mastery_gain) / (avg_mastery_gain / len(progress_records))
    else:
        estimated_completion = plan.duration_days
    
    # 生成建议
    suggestions = []
    
    # 基于最近表现的建议
    if avg_mastery_gain < 60:
        suggestions.append("建议增加复习时间，巩固基础知识")
    
    recent_time = sum(p.time_spent or 0 for p in recent_records) / len(recent_records)
    if recent_time < plan.target_hours_per_day * 60 * 0.8:  # 少于目标时间的80%
        suggestions.append("建议增加每日学习时间")
    
    # WWH平衡建议
    recent_what = sum(p.what_mastery or 0 for p in recent_records) / len(recent_records)
    recent_why = sum(p.why_mastery or 0 for p in recent_records) / len(recent_records)
    recent_how = sum(p.how_mastery or 0 for p in recent_records) / len(recent_records)
    
    if recent_what - recent_how > 20:
        suggestions.append("理论掌握较好，建议增加实践练习")
    elif recent_how - recent_what > 20:
        suggestions.append("实践能力较强，建议加强理论基础")
    
    return {
        "estimated_completion_day": round(estimated_completion),
        "current_pace": "ahead" if estimated_completion < plan.duration_days else "behind",
        "suggestions": suggestions,
        "confidence_level": min(len(progress_records) / 7 * 100, 100)  # 基于数据量的置信度
    }
